# financial_management/signals.py
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import models
from django.contrib.auth import get_user_model
from orders.models import Order
from restaurant.models import Restaurant
from .models import RestaurantEarnings, CommissionStructure
from .delivery_agent_payments import DeliveryAgentCommission, DeliveryAgentEarnings
from decimal import Decimal
import logging
from django.utils import timezone

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(post_save, sender=Order)
def create_restaurant_earnings(sender, instance, created, **kwargs):
    """Create restaurant earnings when order is completed"""
    if instance.status == 'delivered' and not hasattr(instance, 'earnings'):
        # Get or create commission structure for restaurant
        commission_structure, created = CommissionStructure.objects.get_or_create(
            restaurant=instance.restaurant,
            defaults={
                'commission_rate': Decimal('15.00'),
                'payment_processing_fee': Decimal('2.50'),
                'delivery_fee_share': Decimal('30.00'),
                'minimum_payout_amount': Decimal('50.00'),
                'payout_frequency': 'weekly'
            }
        )
        
        # Create earnings record with default values
        earnings = RestaurantEarnings.objects.create(
            restaurant=instance.restaurant,
            order=instance,
            order_total=instance.total_amount,
            delivery_fee=instance.delivery_fee,
            tax_amount=instance.tax_amount,
            commission_amount=Decimal('0.00'),  # Will be calculated
            gross_earnings=Decimal('0.00'),     # Will be calculated
            net_earnings=Decimal('0.00')        # Will be calculated
        )

        # Calculate earnings
        earnings.calculate_earnings()
        earnings.save()


@receiver(post_save, sender=Order)
def create_delivery_agent_earnings(sender, instance, created, **kwargs):
    """Create delivery agent earnings when order is delivered"""
    if (instance.status == 'delivered' and
        instance.delivery_agent and
        not hasattr(instance, 'delivery_earnings')):

        # Calculate delivery distance (enhanced calculation)
        delivery_distance = calculate_delivery_distance(instance)

        # Calculate delivery time (from assignment to delivery)
        delivery_time = calculate_delivery_time(instance)

        # Create earnings record
        earnings = DeliveryAgentEarnings.objects.create(
            delivery_agent=instance.delivery_agent,
            order=instance,
            delivery_distance_km=Decimal(str(delivery_distance)),
            delivery_time_minutes=delivery_time,
            tips=Decimal('0.00')  # Tips can be added separately
        )

        # Calculate earnings
        earnings.calculate_earnings()

        logger.info(f"Created delivery earnings for agent {instance.delivery_agent.email}")


@receiver(post_save, sender=Order)
def update_delivery_agent_stats(sender, instance, created, **kwargs):
    """Update delivery agent statistics when order status changes"""
    if instance.delivery_agent:
        try:
            from deliveryAgent.models import DeliveryAgentProfile
            agent_profile = DeliveryAgentProfile.objects.get(user=instance.delivery_agent)

            # Count total deliveries assigned to this agent
            total_assigned = Order.objects.filter(delivery_agent=instance.delivery_agent).count()

            # Count successful deliveries
            successful_deliveries = Order.objects.filter(
                delivery_agent=instance.delivery_agent,
                status__in=['delivered', 'completed']
            ).count()

            # Count cancelled/rejected deliveries
            cancelled_deliveries = Order.objects.filter(
                delivery_agent=instance.delivery_agent,
                status__in=['cancelled', 'rejected']
            ).count()

            # Update agent profile statistics
            agent_profile.total_deliveries = total_assigned
            agent_profile.successful_deliveries = successful_deliveries

            # Calculate total earnings from DeliveryAgentEarnings
            total_earnings = DeliveryAgentEarnings.objects.filter(
                delivery_agent=instance.delivery_agent
            ).aggregate(total=models.Sum('net_earnings'))['total'] or Decimal('0.00')

            agent_profile.total_earnings = total_earnings
            agent_profile.save(update_fields=['total_deliveries', 'successful_deliveries', 'total_earnings'])

            logger.info(f"Updated stats for agent {instance.delivery_agent.email}: {successful_deliveries}/{total_assigned} deliveries")

        except DeliveryAgentProfile.DoesNotExist:
            logger.warning(f"No delivery agent profile found for user {instance.delivery_agent.email}")
        except Exception as e:
            logger.error(f"Error updating delivery agent stats: {str(e)}")


@receiver(post_save, sender=Restaurant)
def create_commission_structure(sender, instance, created, **kwargs):
    """Create default commission structure for new restaurants"""
    if created:
        CommissionStructure.objects.get_or_create(
            restaurant=instance,
            defaults={
                'commission_rate': Decimal('15.00'),
                'payment_processing_fee': Decimal('2.50'),
                'delivery_fee_share': Decimal('30.00'),
                'minimum_payout_amount': Decimal('50.00'),
                'payout_frequency': 'weekly'
            }
        )


@receiver(post_save, sender=User)
def create_delivery_agent_commission(sender, instance, created, **kwargs):
    """Create default commission structure for new delivery agents"""
    if created and instance.role == 'delivery_agent':
        DeliveryAgentCommission.objects.get_or_create(
            delivery_agent=instance,
            defaults={
                'base_delivery_fee': Decimal('5.00'),
                'distance_rate_per_km': Decimal('0.50'),
                'time_bonus_per_minute': Decimal('0.10'),
                'platform_commission_rate': Decimal('30.00'),
                'minimum_payout_amount': Decimal('25.00'),
                'payout_frequency': 'weekly',
                'performance_bonus_threshold': 20,
                'performance_bonus_amount': Decimal('10.00')
            }
        )
        logger.info(f"Created delivery commission structure for {instance.email}")


def calculate_delivery_distance(order):
    """Calculate delivery distance between restaurant and customer"""
    try:
        # If you have geolocation data, calculate actual distance
        # For now, use a reasonable estimate based on delivery fee
        if order.delivery_fee:
            # Estimate: AFN 50 base fee + AFN 10 per km
            base_fee = 50
            per_km_fee = 10
            if order.delivery_fee > base_fee:
                estimated_distance = (float(order.delivery_fee) - base_fee) / per_km_fee
                return max(1.0, min(estimated_distance, 20.0))  # Between 1-20 km

        # Default fallback
        return 3.0  # 3km default
    except:
        return 3.0


def calculate_delivery_time(order):
    """Calculate actual delivery time from assignment to delivery"""
    try:
        if order.last_assigned_at and order.delivered_at:
            time_diff = order.delivered_at - order.last_assigned_at
            delivery_time = int(time_diff.total_seconds() / 60)
            return max(10, min(delivery_time, 120))  # Between 10-120 minutes
        elif order.accepted_at and order.delivered_at:
            time_diff = order.delivered_at - order.accepted_at
            delivery_time = int(time_diff.total_seconds() / 60)
            return max(10, min(delivery_time, 120))
        else:
            # Default based on distance
            distance = calculate_delivery_distance(order)
            return int(15 + (distance * 3))  # 15 min base + 3 min per km
    except:
        return 30  # 30 minutes default
