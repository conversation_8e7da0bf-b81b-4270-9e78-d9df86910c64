import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { useCart } from "../../context/CartContext";
import { useMenu } from "../../context/MenuContext";
import { useRestaurant } from "../../context/RestaurantContext";
import { getImageProps } from "../../utils/imageUtils";
import { getMenuItemCategoryName } from "../../utils/categoryUtils";
import {
  Search,
  Filter,
  PlusCircle,
  Edit,
  Trash2,
  Image as ImageIcon,
  DollarSign,
  Tag,
  Info,
  X,
  Check,
  ShoppingBag,
  ShoppingCart,
  Plus,
  AlertCircle,
  Shield,
  ShieldCheck,
  Lock,
} from "lucide-react";
import { menuCategories } from "../../data/menuItems";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";
import FormControl from "../../components/common/FormControl";
import { useForm } from "react-hook-form";
import { Link } from "react-router-dom";

function MenuManagement() {
  const { user } = useAuth();
  const { cart, addToCart, itemCount } = useCart();
  const { currentRestaurant, getRestaurant } = useRestaurant();
  const {
    categories,
    menuItems,
    loading,
    error,
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
    loadMenuItems,
    clearError,
  } = useMenu();

  const [filteredItems, setFilteredItems] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("All");
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [imagePreview, setImagePreview] = useState("");

  // Debug: Log when imagePreview changes
  useEffect(() => {
    console.log(
      "🖼️ Image preview state changed:",
      imagePreview ? "HAS IMAGE" : "NO IMAGE"
    );
  }, [imagePreview]);
  const [addedToCart, setAddedToCart] = useState(null);
  const [cartError, setCartError] = useState(null);
  const [selectedImageFile, setSelectedImageFile] = useState(null);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm();

  // Filter menu items when they change
  useEffect(() => {
    setFilteredItems(menuItems);
  }, [menuItems]);

  // Filter menu items when category or search query changes
  useEffect(() => {
    let filtered = menuItems;

    // Filter by category - need to match with category name from API
    if (activeCategory !== "All") {
      filtered = filtered.filter((item) => {
        // Find the category object for this item
        const itemCategory = categories.find((cat) => cat.id === item.category);
        return itemCategory?.name === activeCategory;
      });
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.name.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query)
      );
    }

    setFilteredItems(filtered);
  }, [activeCategory, searchQuery, menuItems, categories]);

  const handleAddItem = () => {
    setEditingItem(null);
    setImagePreview("");
    setSelectedImageFile(null);
    reset({
      name: "",
      description: "",
      price: "",
      category: categories.length > 0 ? categories[0].id : "", // Default to first category
      is_vegetarian: false,
      is_available: true,
      preparation_time: 30,
      image: "",
    });
    setShowAddModal(true);
  };

  const handleEditItem = (item) => {
    setEditingItem(item);
    setImagePreview(item.image || "");
    setSelectedImageFile(null);
    setValue("name", item.name);
    setValue("description", item.description);
    setValue("price", item.price);
    setValue("category", item.category_id || item.category);
    setValue("is_vegetarian", item.is_vegetarian);
    setValue("is_available", item.is_available);
    setValue("preparation_time", item.preparation_time);
    setValue("image", "");
    setShowAddModal(true);
  };

  const handleDeleteItem = async (itemId) => {
    if (window.confirm("Are you sure you want to delete this menu item?")) {
      const result = await deleteMenuItem(itemId);
      if (!result.success) {
        console.error("Failed to delete menu item:", result.error);
      }
    }
  };

  const handleImageChange = (e) => {
    console.log("🖼️ Image change triggered");
    const file = e.target.files[0];
    console.log("📁 Selected file:", file);

    if (file) {
      console.log("📋 File details:", {
        name: file.name,
        type: file.type,
        size: file.size,
      });

      // Validate file type
      const validTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/webp",
      ];
      if (!validTypes.includes(file.type)) {
        alert("Please select a valid image file (JPEG, PNG, GIF, or WebP)");
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        alert("Image file size must be less than 5MB");
        return;
      }

      console.log("✅ File validation passed");
      setSelectedImageFile(file);

      const reader = new FileReader();
      reader.onloadend = () => {
        console.log("✅ FileReader finished, setting preview");
        setImagePreview(reader.result);
      };
      reader.onerror = (error) => {
        console.error("❌ FileReader error:", error);
        alert("Error reading file. Please try again.");
      };
      reader.readAsDataURL(file);
    } else {
      console.log("❌ No file selected");
    }
  };

  const handleAddToCart = (item, event) => {
    // Prevent the card click event from triggering
    event.stopPropagation();

    // Clear any previous messages
    setCartError(null);

    // Add item to cart
    const result = addToCart(
      restaurant.id,
      restaurant.name,
      item,
      1 // Default quantity
    );

    if (result.success) {
      // Show success message
      setAddedToCart(item.id);

      // Clear success message after 2 seconds
      setTimeout(() => {
        setAddedToCart(null);
      }, 2000);
    } else {
      // Show error message
      setCartError(result.error);
    }
  };

  const handleClearCartAndAdd = async (item, event) => {
    event.stopPropagation();

    // Import clearCart from useCart
    const { clearCart } = useCart();

    // Clear the cart
    await clearCart();

    // Add the item after a small delay to ensure cart is cleared
    setTimeout(() => {
      const result = addToCart(restaurant.id, restaurant.name, item, 1);

      if (result.success) {
        setAddedToCart(item.id);
        setTimeout(() => {
          setAddedToCart(null);
        }, 2000);
      }

      setCartError(null);
    }, 100);
  };

  const onSubmit = async (data) => {
    clearError();

    try {
      if (editingItem) {
        // Update existing item
        const updateData = {
          name: data.name,
          description: data.description,
          price: parseFloat(data.price),
          category_id: parseInt(data.category),
          is_vegetarian: data.is_vegetarian,
          is_available: data.is_available,
          preparation_time: parseInt(data.preparation_time),
        };

        const result = await updateMenuItem(editingItem.id, updateData);
        if (result.success) {
          setShowAddModal(false);
          setEditingItem(null);
          setImagePreview("");
          setSelectedImageFile(null);
        } else {
          console.error("Failed to update menu item:", result.error);
        }
      } else {
        // Add new item
        const itemData = {
          category_id: parseInt(data.category),
          name: data.name,
          price: parseFloat(data.price),
          image: selectedImageFile,
          is_vegetarian: data.is_vegetarian,
          is_available: data.is_available,
          description: data.description,
          preparation_time: parseInt(data.preparation_time),
        };

        const result = await createMenuItem(itemData);
        if (result.success) {
          setShowAddModal(false);
          setImagePreview("");
          setSelectedImageFile(null);
          reset();
        } else {
          console.error("Failed to create menu item:", result.error);
        }
      }
    } catch (err) {
      console.error("Form submission error:", err);
    }
  };

  return (
    <div className='p-6 animate-fade-in'>
      <div className='flex sm:flex-row flex-col sm:justify-between sm:items-center mb-6'>
        <h1 className='mb-4 sm:mb-0 font-bold text-2xl'>Menu Management</h1>
        <Button
          variant='primary'
          icon={<PlusCircle size={18} />}
          onClick={handleAddItem}
          disabled={
            loading ||
            (user?.role === "restaurant" && !currentRestaurant?.is_verified)
          }
          title={
            user?.role === "restaurant" && !currentRestaurant?.is_verified
              ? "Restaurant must be verified to add menu items"
              : ""
          }
        >
          Add Menu Item
        </Button>
      </div>

      {/* Verification Status Check */}
      {user && user.role === "restaurant" && (
        <Card className='mb-6 border-yellow-500 border-l-4'>
          <div className='flex items-start p-4'>
            <Lock
              size={18}
              className='flex-shrink-0 mt-0.5 mr-2 text-yellow-500'
            />
            <div>
              <h3 className='mb-1 font-medium text-yellow-800 text-sm'>
                Restaurant Verification Required
              </h3>
              <p className='mb-2 text-yellow-700 text-sm'>
                Your restaurant must be verified by admin before you can manage
                menu items. Menu management is currently disabled.
              </p>
              <div className='flex items-center space-x-4 text-yellow-600 text-xs'>
                <div className='flex items-center space-x-1'>
                  <Shield size={12} />
                  <span>Pending Admin Approval</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className='mb-6 border-red-500 border-l-4'>
          <div className='flex items-start p-4'>
            <AlertCircle
              size={18}
              className='flex-shrink-0 mt-0.5 mr-2 text-red-500'
            />
            <div>
              <p className='text-red-600 text-sm'>{error}</p>
              <button
                onClick={clearError}
                className='mt-1 text-red-500 text-sm underline'
              >
                Dismiss
              </button>
            </div>
          </div>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card className='mb-6'>
          <div className='flex justify-center items-center p-8'>
            <div className='border-primary-500 border-b-2 rounded-full w-8 h-8 animate-spin'></div>
            <span className='ml-3 text-gray-600'>Loading...</span>
          </div>
        </Card>
      )}

      {/* Search & Filter */}
      <Card className='mb-6'>
        <div className='flex md:flex-row flex-col justify-between md:items-center gap-4 mb-4'>
          <div className='relative flex-1'>
            <Search
              className='top-1/2 left-3 absolute text-gray-400 -translate-y-1/2 transform'
              size={20}
            />
            <input
              type='text'
              placeholder='Search menu items...'
              className='py-2 pr-4 pl-10 border focus:border-transparent rounded-lg focus:ring-2 focus:ring-primary-500 w-full'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className='flex gap-4'>
            <select
              className='px-4 py-2 border focus:border-transparent rounded-lg focus:ring-2 focus:ring-primary-500'
              value={activeCategory}
              onChange={(e) => setActiveCategory(e.target.value)}
            >
              <option value='All'>All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.name}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Category Pills */}
        <div className='flex flex-wrap gap-2'>
          <button
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              activeCategory === "All"
                ? "bg-primary-500 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            onClick={() => setActiveCategory("All")}
          >
            All Categories
          </button>
          {categories.map((category) => (
            <button
              key={category.id}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                activeCategory === category.name
                  ? "bg-primary-500 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
              onClick={() => setActiveCategory(category.name)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </Card>

      {/* Menu Items */}
      <div className='gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
        {filteredItems.map((item) => (
          <Card key={item.id} className='overflow-hidden'>
            <div className='relative -mx-5 -mt-5 mb-4 h-48'>
              {item.image && item.image !== "null" && item.image !== null ? (
                <img
                  src={item.image}
                  alt={item.name}
                  className='w-full h-full object-cover'
                  onError={(e) => {
                    e.target.style.display = "none";
                    e.target.nextSibling.style.display = "flex";
                  }}
                />
              ) : null}
              <div
                className='flex justify-center items-center bg-gray-200 w-full h-full'
                style={{
                  display:
                    item.image && item.image !== "null" && item.image !== null
                      ? "none"
                      : "flex",
                }}
              >
                <div className='text-center'>
                  <ImageIcon size={48} className='mx-auto mb-2 text-gray-400' />
                  <span className='text-gray-500 text-sm'>No Image</span>
                </div>
              </div>
              <div className='top-2 right-2 absolute flex gap-2'>
                <button
                  className='bg-white hover:bg-gray-100 shadow-md p-2 rounded-full'
                  onClick={() => handleEditItem(item)}
                >
                  <Edit size={16} className='text-primary-500' />
                </button>
                <button
                  className='bg-white hover:bg-gray-100 shadow-md p-2 rounded-full'
                  onClick={() => handleDeleteItem(item.id)}
                >
                  <Trash2 size={16} className='text-accent-red' />
                </button>
              </div>
              <div className='bottom-2 left-2 absolute flex gap-2'>
                {item.is_vegetarian && (
                  <Badge variant='success' size='small'>
                    Vegetarian
                  </Badge>
                )}
                {!item.is_available && (
                  <Badge variant='danger' size='small'>
                    Unavailable
                  </Badge>
                )}
              </div>
            </div>

            <h3 className='font-medium text-lg'>{item.name}</h3>
            <p className='mt-1 mb-2 text-text-secondary text-sm line-clamp-2'>
              {item.description}
            </p>
            <div className='flex justify-between items-center mt-auto'>
              <span className='font-semibold'>
                ${parseFloat(item.price).toFixed(2)}
              </span>
              <Badge variant='secondary' size='small'>
                {getMenuItemCategoryName(item)}
              </Badge>
            </div>

            {/* Preparation Time */}
            <div className='mt-2 text-gray-500 text-sm'>
              <span>Prep time: {item.preparation_time} min</span>
            </div>

            {/* Add to Cart Button */}
            <div className='mt-3 pt-3 border-gray-100 border-t'>
              {addedToCart === item.id ? (
                <div className='flex items-center bg-green-50 px-3 py-2 rounded-md text-green-700 text-sm'>
                  <Check size={16} className='mr-1' />
                  Added to cart!
                </div>
              ) : (
                <Button
                  variant='primary'
                  size='small'
                  fullWidth
                  icon={<ShoppingCart size={16} />}
                  onClick={(e) => handleAddToCart(item, e)}
                >
                  Add to Cart
                </Button>
              )}
            </div>
          </Card>
        ))}
      </div>

      {filteredItems.length === 0 && (
        <div className='bg-white shadow py-12 rounded-lg text-center'>
          <Info size={48} className='mx-auto mb-4 text-text-secondary' />
          <h3 className='mb-2 font-medium text-xl'>No Menu Items Found</h3>
          <p className='mb-6 text-text-secondary'>
            {menuItems.length === 0
              ? "You haven't added any menu items yet."
              : "No items match your current filters."}
          </p>
          {menuItems.length === 0 && (
            <Button
              variant='primary'
              icon={<PlusCircle size={18} />}
              onClick={handleAddItem}
            >
              Add Your First Menu Item
            </Button>
          )}
        </div>
      )}

      {/* Cart Error Modal */}
      {cartError && (
        <div className='z-50 fixed inset-0 flex justify-center items-center bg-black bg-opacity-50 p-4'>
          <div className='bg-white shadow-lg p-6 rounded-lg w-full max-w-md animate-fade-in'>
            <div className='flex items-start mb-4'>
              <AlertCircle
                size={24}
                className='flex-shrink-0 mr-3 text-accent-red'
              />
              <div>
                <h3 className='mb-2 font-semibold text-lg'>
                  Cannot Add to Cart
                </h3>
                <p className='mb-4 text-text-secondary'>{cartError}</p>
                <p className='mb-4 text-text-secondary'>
                  Would you like to clear your cart and add this item instead?
                </p>
              </div>
            </div>

            <div className='flex justify-end gap-3'>
              <Button variant='secondary' onClick={() => setCartError(null)}>
                Cancel
              </Button>
              <Button
                variant='primary'
                onClick={(e) => {
                  // Find the item that caused the error
                  const item = filteredItems.find(
                    (item) =>
                      cart.restaurantId &&
                      item.restaurantId !== cart.restaurantId
                  );
                  if (item) {
                    handleClearCartAndAdd(item, e);
                  }
                  setCartError(null);
                }}
              >
                Clear Cart & Add
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Floating Cart Button */}
      {itemCount > 0 && (
        <div className='right-6 bottom-6 z-30 fixed'>
          <Link to='/cart'>
            <Button
              variant='primary'
              className='flex justify-center items-center shadow-lg rounded-full w-14 h-14'
            >
              <div className='relative'>
                <ShoppingBag size={24} />
                <span className='-top-2 -right-2 absolute flex justify-center items-center rounded-full w-5 h-5 text-white text-xs bg-accent-red'>
                  {itemCount}
                </span>
              </div>
            </Button>
          </Link>
        </div>
      )}

      {/* Add/Edit Menu Item Modal */}
      {showAddModal && (
        <div className='z-50 fixed inset-0 flex justify-center items-center bg-black bg-opacity-50 p-4'>
          <div className='bg-white shadow-lg rounded-lg w-full max-w-2xl max-h-[90vh] overflow-auto animate-fade-in'>
            <div className='flex justify-between items-center p-6 border-b'>
              <h3 className='font-semibold text-xl'>
                {editingItem ? "Edit Menu Item" : "Add New Menu Item"}
              </h3>
              <button
                className='text-gray-400 hover:text-gray-500'
                onClick={() => setShowAddModal(false)}
              >
                <X size={24} />
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className='p-6'>
              <div className='gap-6 grid grid-cols-1 md:grid-cols-2'>
                <div className='md:col-span-2'>
                  <FormControl label='Item Image' htmlFor='image'>
                    <div className='flex flex-col items-center'>
                      <div className='flex justify-center items-center bg-gray-100 mb-4 border-2 border-gray-300 border-dashed rounded-lg w-full h-48 overflow-hidden'>
                        {imagePreview ? (
                          <img
                            src={imagePreview}
                            alt='Preview'
                            className='w-full h-full object-cover'
                            onLoad={() =>
                              console.log(
                                "✅ Preview image loaded successfully"
                              )
                            }
                            onError={() =>
                              console.log("❌ Preview image failed to load")
                            }
                          />
                        ) : (
                          <div className='text-center'>
                            <ImageIcon
                              size={48}
                              className='mx-auto mb-2 text-gray-300'
                            />
                            <p className='text-gray-500 text-sm'>
                              No image selected
                            </p>
                          </div>
                        )}
                      </div>
                      <input
                        type='file'
                        id='image'
                        accept='image/*'
                        className='hidden'
                        onChange={handleImageChange}
                      />
                      <div className='flex gap-2'>
                        <label
                          htmlFor='image'
                          className='bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-md text-gray-700 transition-colors cursor-pointer'
                          onClick={() => {
                            console.log("🖱️ Upload button clicked");
                            document.getElementById("image").click();
                          }}
                        >
                          {imagePreview ? "Change Image" : "Upload Image"}
                        </label>
                        {imagePreview && (
                          <button
                            type='button'
                            onClick={() => {
                              console.log("🗑️ Removing image preview");
                              setImagePreview("");
                              setSelectedImageFile(null);
                              setValue("image", "");
                            }}
                            className='bg-red-100 hover:bg-red-200 px-4 py-2 rounded-md text-red-700 transition-colors'
                          >
                            Remove
                          </button>
                        )}

                        {/* Debug: Test button to set a sample preview */}
                        <button
                          type='button'
                          onClick={() => {
                            console.log("🧪 Setting test image preview");
                            setImagePreview(
                              "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwN2JmZiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+VEVTVCBJTUFHRTwvdGV4dD48L3N2Zz4="
                            );
                          }}
                          className='bg-blue-100 hover:bg-blue-200 px-4 py-2 rounded-md text-blue-700 text-xs transition-colors'
                        >
                          Test Preview
                        </button>
                      </div>
                    </div>
                  </FormControl>
                </div>

                <div className='md:col-span-2'>
                  <Input
                    label='Item Name'
                    placeholder='Enter item name'
                    error={errors.name?.message}
                    required
                    {...register("name", { required: "Item name is required" })}
                  />
                </div>

                <div className='md:col-span-2'>
                  <FormControl
                    label='Description'
                    htmlFor='description'
                    error={errors.description?.message}
                    required
                  >
                    <textarea
                      id='description'
                      placeholder='Enter item description'
                      className={`w-full px-4 py-2 bg-white border rounded-md outline-none transition-colors duration-200 resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                        errors.description
                          ? "border-accent-red"
                          : "border-gray-300"
                      }`}
                      rows={3}
                      {...register("description", {
                        required: "Description is required",
                      })}
                    ></textarea>
                  </FormControl>
                </div>

                <div>
                  <Input
                    label='Price'
                    type='number'
                    step='0.01'
                    placeholder='0.00'
                    icon={<DollarSign size={18} />}
                    error={errors.price?.message}
                    required
                    {...register("price", {
                      required: "Price is required",
                      min: {
                        value: 0.01,
                        message: "Price must be greater than 0",
                      },
                      pattern: {
                        value: /^\d+(\.\d{1,2})?$/,
                        message: "Please enter a valid price",
                      },
                    })}
                  />
                </div>

                <div>
                  <FormControl
                    label='Category'
                    htmlFor='category'
                    error={errors.category?.message}
                    required
                  >
                    <select
                      id='category'
                      className='bg-white px-4 py-2 border border-gray-300 focus:border-transparent rounded-md outline-none focus:ring-2 focus:ring-primary-500 w-full transition-colors duration-200'
                      {...register("category", {
                        required: "Category is required",
                      })}
                    >
                      <option value=''>Select a category</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </FormControl>
                </div>

                <div>
                  <Input
                    label='Preparation Time (minutes)'
                    type='number'
                    placeholder='30'
                    error={errors.preparation_time?.message}
                    required
                    {...register("preparation_time", {
                      required: "Preparation time is required",
                      min: {
                        value: 1,
                        message: "Preparation time must be at least 1 minute",
                      },
                    })}
                  />
                </div>

                <div className='flex items-center space-x-4 md:col-span-2'>
                  <label className='flex items-center space-x-2 cursor-pointer'>
                    <input
                      type='checkbox'
                      className='border-gray-300 rounded focus:ring-primary-500 text-primary-500'
                      {...register("is_available")}
                    />
                    <span>Available</span>
                  </label>

                  <label className='flex items-center space-x-2 cursor-pointer'>
                    <input
                      type='checkbox'
                      className='border-gray-300 rounded focus:ring-primary-500 text-primary-500'
                      {...register("is_vegetarian")}
                    />
                    <span>Vegetarian</span>
                  </label>
                </div>
              </div>

              <div className='flex justify-end gap-4 mt-8'>
                <Button
                  type='button'
                  variant='secondary'
                  onClick={() => setShowAddModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  type='submit'
                  variant='primary'
                  icon={<Check size={18} />}
                >
                  {editingItem ? "Update Item" : "Add Item"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default MenuManagement;
