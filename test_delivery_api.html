<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>Delivery Agent API Test</h1>
    
    <div class="test-section">
        <h3>Authentication Test</h3>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Get My Orders</h3>
        <button onclick="getMyOrders()">Get My Orders</button>
        <div id="orders-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Update Order Status Test</h3>
        <input type="text" id="order-id" placeholder="Order ID" />
        <select id="new-status">
            <option value="picked_up">Picked Up</option>
            <option value="in_transit">In Transit</option>
            <option value="delivered">Delivered</option>
        </select>
        <button onclick="updateOrderStatus()">Update Status</button>
        <div id="update-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api/delivery-agent';
        
        function getAuthToken() {
            const user = JSON.parse(localStorage.getItem('afghanSofraUser') || '{}');
            return user.access_token || localStorage.getItem('access_token');
        }

        async function testAuth() {
            const resultDiv = document.getElementById('auth-result');
            const token = getAuthToken();
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No auth token found. Please login first.</div>';
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/profile/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<div class="success">Auth OK: ${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `<div class="error">Auth Failed (${response.status}): ${error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Network Error: ${error.message}</div>`;
            }
        }

        async function getMyOrders() {
            const resultDiv = document.getElementById('orders-result');
            const token = getAuthToken();
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No auth token found. Please login first.</div>';
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/my-orders/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<div class="success">Orders: ${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `<div class="error">Failed (${response.status}): ${error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Network Error: ${error.message}</div>`;
            }
        }

        async function updateOrderStatus() {
            const resultDiv = document.getElementById('update-result');
            const token = getAuthToken();
            const orderId = document.getElementById('order-id').value;
            const newStatus = document.getElementById('new-status').value;
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No auth token found. Please login first.</div>';
                return;
            }

            if (!orderId) {
                resultDiv.innerHTML = '<div class="error">Please enter an Order ID</div>';
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/update-order-status/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        order_id: orderId,
                        status: newStatus
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<div class="success">Status Updated: ${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `<div class="error">Update Failed (${response.status}): ${error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Network Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
