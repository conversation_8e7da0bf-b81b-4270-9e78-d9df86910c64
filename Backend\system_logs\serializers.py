from rest_framework import serializers
from .models import SystemLog, LogStatistics


class SystemLogSerializer(serializers.ModelSerializer):
    """Serializer for SystemLog model"""
    user_display = serializers.ReadOnlyField()
    formatted_timestamp = serializers.ReadOnlyField()
    
    class Meta:
        model = SystemLog
        fields = [
            'id', 'timestamp', 'formatted_timestamp', 'level', 'category',
            'message', 'details', 'user', 'user_display', 'ip_address',
            'user_agent', 'source', 'request_id', 'session_id',
            'extra_data', 'response_time'
        ]
        read_only_fields = ['id', 'timestamp']


class LogStatisticsSerializer(serializers.ModelSerializer):
    """Serializer for LogStatistics model"""
    total_count = serializers.ReadOnlyField()
    
    class Meta:
        model = LogStatistics
        fields = [
            'date', 'debug_count', 'info_count', 'warning_count',
            'error_count', 'critical_count', 'authentication_count',
            'database_count', 'api_count', 'payments_count', 'orders_count',
            'system_count', 'other_count', 'total_logs', 'total_count',
            'created_at', 'updated_at'
        ]


class LogStatsSerializer(serializers.Serializer):
    """Serializer for log statistics summary"""
    all = serializers.IntegerField()
    debug = serializers.IntegerField()
    info = serializers.IntegerField()
    warning = serializers.IntegerField()
    error = serializers.IntegerField()
    critical = serializers.IntegerField()
    
    # Category counts
    authentication = serializers.IntegerField()
    database = serializers.IntegerField()
    api = serializers.IntegerField()
    payments = serializers.IntegerField()
    orders = serializers.IntegerField()
    system = serializers.IntegerField()
    other = serializers.IntegerField()


class LogExportSerializer(serializers.Serializer):
    """Serializer for log export parameters"""
    level = serializers.ChoiceField(
        choices=['all'] + [choice[0] for choice in SystemLog.LOG_LEVELS],
        default='all'
    )
    category = serializers.ChoiceField(
        choices=['all'] + [choice[0] for choice in SystemLog.LOG_CATEGORIES],
        default='all'
    )
    date_range = serializers.ChoiceField(
        choices=['today', 'yesterday', 'last_7_days', 'last_30_days', 'custom'],
        default='today'
    )
    start_date = serializers.DateField(required=False)
    end_date = serializers.DateField(required=False)
    search = serializers.CharField(required=False, allow_blank=True)
    format = serializers.ChoiceField(choices=['csv', 'json'], default='csv')
