#!/usr/bin/env python3
"""
Test script to check what users exist and their permissions
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from backup_management.models import BackupJob

User = get_user_model()

def check_users_and_backups():
    """Check all users and their backup permissions"""
    
    print("🔍 Checking all users in the system...")
    users = User.objects.all()
    
    for user in users:
        print(f"\n👤 User: {user.name} ({user.user_name})")
        print(f"   📧 Email: {user.email}")
        print(f"   🔑 Role: {user.role}")
        print(f"   ✅ Verified: {user.is_verified}")
        print(f"   👑 Staff: {user.is_staff}")
        print(f"   🔐 Superuser: {user.is_superuser}")
        
        # Check backups created by this user
        user_backups = BackupJob.objects.filter(created_by=user)
        print(f"   📦 Backups created: {user_backups.count()}")
        
        if user_backups.exists():
            latest_backup = user_backups.order_by('-created_at').first()
            print(f"   📅 Latest backup: {latest_backup.created_at}")
            print(f"   📊 Latest status: {latest_backup.status}")
    
    print(f"\n📊 Total users: {users.count()}")
    print(f"📦 Total backups: {BackupJob.objects.count()}")
    
    # Show backup distribution by user
    print(f"\n📈 Backup distribution:")
    for user in users:
        backup_count = BackupJob.objects.filter(created_by=user).count()
        if backup_count > 0:
            print(f"   {user.name}: {backup_count} backups")
    
    # Show recent backups
    print(f"\n📅 Recent backups:")
    recent_backups = BackupJob.objects.order_by('-created_at')[:5]
    for backup in recent_backups:
        print(f"   {backup.name} - {backup.status} - {backup.created_by.name} - {backup.created_at}")

if __name__ == '__main__':
    check_users_and_backups()
