#!/usr/bin/env python3
"""
Comprehensive test suite for backup and restore functionality
"""

import os
import sys
import django
import requests
import json
import time

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from backup_management.models import BackupJob, BackupSettings
from backup_management.services import BackupService

User = get_user_model()

class BackupTestSuite:
    def __init__(self):
        self.base_url = "http://127.0.0.1:8000"
        self.admin_user = None
        self.access_token = None
        self.headers = {}
        
    def setup_admin_user(self):
        """Setup admin user for testing"""
        print("🔧 Setting up admin user...")
        
        # Get or create admin user
        try:
            self.admin_user = User.objects.get(user_name='test_admin_backup')
            created = False
        except User.DoesNotExist:
            # Use a unique phone number
            import random
            unique_phone = f"555{random.randint(1000000, 9999999)}"
            self.admin_user = User.objects.create_user(
                user_name='test_admin_backup',
                name='Test Admin Backup',
                phone=unique_phone,
                email='<EMAIL>',
                role='admin',
                is_staff=True,
                is_superuser=True,
                is_verified=True
            )
            created = True
        
        if created:
            self.admin_user.set_password('admin123')
            self.admin_user.save()
            print("✅ Created new admin user")
        else:
            # Ensure existing user is verified
            if not self.admin_user.is_verified:
                self.admin_user.is_verified = True
                self.admin_user.save()
            print("✅ Using existing admin user")
        
        print(f"👤 Admin user: {self.admin_user.name} ({self.admin_user.user_name})")
        
    def login_admin(self):
        """Login admin user and get token"""
        print("\n🔐 Logging in admin user...")
        
        login_data = {
            'user_name': 'test_admin_backup',
            'password': 'admin123'
        }
        
        response = requests.post(f"{self.base_url}/api/auth/login/", json=login_data)
        
        if response.status_code == 200:
            result = response.json()
            self.access_token = result.get('data', {}).get('access_token')
            self.headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            print("✅ Admin login successful")
            return True
        else:
            print(f"❌ Login failed: {response.text}")
            return False
    
    def test_backup_settings(self):
        """Test backup settings API"""
        print("\n📋 Testing Backup Settings...")
        
        # Get settings
        response = requests.get(f"{self.base_url}/api/admin/backup/settings/", headers=self.headers)
        print(f"GET Settings Status: {response.status_code}")
        
        if response.status_code == 200:
            settings = response.json()
            print("✅ Get settings successful")
            print(f"   Default storage: {settings['data']['default_storage_location']}")
            print(f"   Max backup size: {settings['data']['max_backup_size_gb']} GB")
            print(f"   Default retention: {settings['data']['default_retention_days']} days")
            
            # Update settings
            update_data = {
                'default_retention_days': 15,
                'max_backup_size_gb': 20,
                'compression_enabled': True
            }
            
            update_response = requests.put(f"{self.base_url}/api/admin/backup/settings/", 
                                         json=update_data, headers=self.headers)
            print(f"PUT Settings Status: {update_response.status_code}")
            
            if update_response.status_code == 200:
                print("✅ Update settings successful")
            else:
                print(f"❌ Update settings failed: {update_response.text}")
        else:
            print(f"❌ Get settings failed: {response.text}")
    
    def test_backup_stats(self):
        """Test backup statistics API"""
        print("\n📊 Testing Backup Statistics...")
        
        response = requests.get(f"{self.base_url}/api/admin/backup/stats/", headers=self.headers)
        print(f"Stats Status: {response.status_code}")
        
        if response.status_code == 200:
            stats = response.json()['data']
            print("✅ Stats retrieval successful")
            print(f"   Total backups: {stats['total_backups']}")
            print(f"   Successful: {stats['successful_backups']}")
            print(f"   Failed: {stats['failed_backups']}")
            print(f"   Pending: {stats['pending_backups']}")
            print(f"   Storage used: {stats['total_storage_used']}")
            print(f"   Success rate: {stats['success_rate']}%")
            print(f"   Last backup: {stats['last_backup_date']}")
        else:
            print(f"❌ Stats failed: {response.text}")
    
    def test_backup_list(self):
        """Test backup list API"""
        print("\n📋 Testing Backup List...")
        
        response = requests.get(f"{self.base_url}/api/admin/backup/backups/", headers=self.headers)
        print(f"List Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            backups = result['data']['results']
            print("✅ Backup list retrieval successful")
            print(f"   Total count: {result['data']['total_count']}")
            print(f"   Page: {result['data']['page']}")
            print(f"   Page size: {result['data']['page_size']}")
            print(f"   Results in page: {len(backups)}")
            
            if backups:
                print("\n   Recent backups:")
                for backup in backups[:3]:
                    print(f"   - {backup['name']} ({backup['backup_type']}) - {backup['status']}")
            
            return backups
        else:
            print(f"❌ List failed: {response.text}")
            return []
    
    def test_backup_creation(self, backup_type="database"):
        """Test backup creation and monitoring"""
        print(f"\n🚀 Testing {backup_type.title()} Backup Creation...")
        
        create_data = {
            'name': f'Test {backup_type.title()} Backup - {int(time.time())}',
            'backup_type': backup_type,
            'storage_location': 'local',
            'retention_days': 7
        }
        
        # Create backup
        response = requests.post(f"{self.base_url}/api/admin/backup/backups/", 
                               json=create_data, headers=self.headers)
        print(f"Create Status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            backup_id = result['data']['id']
            print("✅ Backup creation successful")
            print(f"   Backup ID: {backup_id}")
            print(f"   Initial status: {result['data']['status']}")
            print(f"   Initial progress: {result['data']['progress']}%")
            
            # Monitor progress
            print("\n📊 Monitoring backup progress...")
            for i in range(30):  # Monitor for up to 30 seconds
                status_response = requests.get(f"{self.base_url}/api/admin/backup/backups/{backup_id}/status/", 
                                             headers=self.headers)
                
                if status_response.status_code == 200:
                    status_data = status_response.json()['data']
                    progress = status_data['progress']
                    status = status_data['status']
                    
                    print(f"   Progress: {progress}% - Status: {status}")
                    
                    if status in ['completed', 'failed']:
                        break
                    
                    time.sleep(1)
                else:
                    print(f"   ❌ Status check failed: {status_response.text}")
                    break
            
            # Final status check
            final_response = requests.get(f"{self.base_url}/api/admin/backup/backups/{backup_id}/", 
                                        headers=self.headers)
            if final_response.status_code == 200:
                final_data = final_response.json()['data']
                print(f"\n✅ Final backup status:")
                print(f"   Status: {final_data['status']}")
                print(f"   Progress: {final_data['progress']}%")
                print(f"   File size: {final_data['file_size_formatted']}")
                print(f"   Duration: {final_data['duration']} seconds" if final_data['duration'] else "   Duration: N/A")
                
                if final_data['error_message']:
                    print(f"   Error: {final_data['error_message']}")
                
                return backup_id, final_data['status'] == 'completed'
            
        else:
            print(f"❌ Backup creation failed: {response.text}")
            return None, False
    
    def test_backup_download(self, backup_id):
        """Test backup download"""
        print(f"\n⬇️ Testing Backup Download for ID: {backup_id}")
        
        response = requests.get(f"{self.base_url}/api/admin/backup/backups/{backup_id}/download/", 
                              headers=self.headers)
        print(f"Download Status: {response.status_code}")
        
        if response.status_code == 200:
            content_length = len(response.content)
            print("✅ Backup download successful")
            print(f"   Downloaded size: {content_length} bytes")
            print(f"   Content type: {response.headers.get('content-type', 'unknown')}")
            return True
        else:
            print(f"❌ Download failed: {response.text}")
            return False
    
    def test_backup_deletion(self, backup_id):
        """Test backup deletion"""
        print(f"\n🗑️ Testing Backup Deletion for ID: {backup_id}")
        
        response = requests.delete(f"{self.base_url}/api/admin/backup/backups/{backup_id}/", 
                                 headers=self.headers)
        print(f"Delete Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Backup deletion successful")
            return True
        else:
            print(f"❌ Deletion failed: {response.text}")
            return False
    
    def run_comprehensive_test(self):
        """Run all backup tests"""
        print("🧪 Starting Comprehensive Backup Test Suite")
        print("=" * 50)
        
        # Setup
        self.setup_admin_user()
        if not self.login_admin():
            print("❌ Cannot proceed without admin login")
            return
        
        # Test all functionality
        self.test_backup_settings()
        self.test_backup_stats()
        existing_backups = self.test_backup_list()
        
        # Test different backup types
        backup_types = ['database', 'full', 'partial']
        created_backups = []
        
        for backup_type in backup_types:
            backup_id, success = self.test_backup_creation(backup_type)
            if success and backup_id:
                created_backups.append(backup_id)
        
        # Test download for first successful backup
        if created_backups:
            self.test_backup_download(created_backups[0])
        
        # Test final stats after creating backups
        print("\n📊 Final Statistics After Tests:")
        self.test_backup_stats()
        
        # Cleanup - delete test backups
        print("\n🧹 Cleaning up test backups...")
        for backup_id in created_backups:
            self.test_backup_deletion(backup_id)
        
        print("\n✅ Comprehensive backup test completed!")
        print("=" * 50)

if __name__ == '__main__':
    test_suite = BackupTestSuite()
    test_suite.run_comprehensive_test()
