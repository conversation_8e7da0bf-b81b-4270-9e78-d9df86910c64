from django.contrib import admin
from .models import BackupJob, BackupSchedule, BackupSettings


@admin.register(BackupJob)
class BackupJobAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'backup_type', 'status', 'progress', 'formatted_file_size',
        'storage_location', 'created_by', 'created_at'
    ]
    list_filter = ['backup_type', 'status', 'storage_location', 'created_at']
    search_fields = ['name', 'created_by__username', 'created_by__name']
    readonly_fields = [
        'id', 'created_at', 'started_at', 'completed_at', 'file_size', 
        'file_path', 'progress', 'error_message'
    ]
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'name', 'backup_type', 'status', 'progress')
        }),
        ('File Information', {
            'fields': ('file_path', 'file_size', 'storage_location')
        }),
        ('Configuration', {
            'fields': ('options', 'retention_days')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'started_at', 'completed_at')
        }),
        ('User Information', {
            'fields': ('created_by',)
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
    )


@admin.register(BackupSchedule)
class BackupScheduleAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'backup_type', 'frequency', 'time', 'is_active',
        'storage_location', 'created_by', 'last_run', 'next_run'
    ]
    list_filter = ['backup_type', 'frequency', 'is_active', 'storage_location']
    search_fields = ['name', 'created_by__username', 'created_by__name']
    readonly_fields = ['created_at', 'last_run', 'next_run']
    ordering = ['-created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'backup_type', 'frequency', 'time', 'is_active')
        }),
        ('Configuration', {
            'fields': ('options', 'retention_days', 'storage_location')
        }),
        ('Schedule Information', {
            'fields': ('last_run', 'next_run')
        }),
        ('User Information', {
            'fields': ('created_by', 'created_at')
        }),
    )


@admin.register(BackupSettings)
class BackupSettingsAdmin(admin.ModelAdmin):
    list_display = [
        'default_storage_location', 'max_backup_size_gb', 'default_retention_days',
        'auto_cleanup_enabled', 'email_notifications', 'updated_at'
    ]
    readonly_fields = ['updated_at']
    
    fieldsets = (
        ('Storage Settings', {
            'fields': ('default_storage_location', 'max_backup_size_gb', 'backup_directory')
        }),
        ('Retention Settings', {
            'fields': ('default_retention_days', 'auto_cleanup_enabled')
        }),
        ('Notification Settings', {
            'fields': ('email_notifications', 'notification_emails')
        }),
        ('Performance Settings', {
            'fields': ('max_concurrent_backups', 'compression_enabled')
        }),
        ('Metadata', {
            'fields': ('updated_at', 'updated_by')
        }),
    )
    
    def has_add_permission(self, request):
        # Only allow one settings instance
        return not BackupSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False
