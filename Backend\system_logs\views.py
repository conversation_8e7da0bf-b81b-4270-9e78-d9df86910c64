import csv
import json
from datetime import datetime, timedelta
from django.http import HttpResponse
from django.db.models import Q, Count
from django.utils import timezone
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.pagination import PageNumberPagination
from .models import SystemLog, LogStatistics
from .serializers import (
    SystemLogSerializer, LogStatisticsSerializer,
    LogStatsSerializer, LogExportSerializer
)


class LogPagination(PageNumberPagination):
    """Custom pagination for logs"""
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200


class SystemLogListView(generics.ListAPIView):
    """
    API view to retrieve system logs with filtering
    """
    serializer_class = SystemLogSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    pagination_class = LogPagination

    def get_queryset(self):
        queryset = SystemLog.objects.select_related('user').all()

        # Filter by level
        level = self.request.query_params.get('level')
        if level and level != 'all':
            queryset = queryset.filter(level=level)

        # Filter by category
        category = self.request.query_params.get('category')
        if category and category != 'all':
            queryset = queryset.filter(category=category)

        # Filter by date range
        date_range = self.request.query_params.get('date_range', 'today')
        now = timezone.now()

        if date_range == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            queryset = queryset.filter(timestamp__gte=start_date)
        elif date_range == 'yesterday':
            yesterday = now - timedelta(days=1)
            start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
            queryset = queryset.filter(timestamp__range=[start_date, end_date])
        elif date_range == 'last_7_days':
            start_date = now - timedelta(days=7)
            queryset = queryset.filter(timestamp__gte=start_date)
        elif date_range == 'last_30_days':
            start_date = now - timedelta(days=30)
            queryset = queryset.filter(timestamp__gte=start_date)

        # Search functionality
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(message__icontains=search) |
                Q(details__icontains=search) |
                Q(source__icontains=search) |
                Q(user__user_name__icontains=search) |
                Q(user__email__icontains=search)
            )

        return queryset.order_by('-timestamp')

    def list(self, request, *args, **kwargs):
        """Override list to return custom response format"""
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response({
                'logs': serializer.data
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'logs': serializer.data
            }
        })


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def log_stats_view(request):
    """
    API view to get log statistics
    """
    try:
        # Get date range for stats
        date_range = request.query_params.get('date_range', 'today')
        now = timezone.now()

        if date_range == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            queryset = SystemLog.objects.filter(timestamp__gte=start_date)
        elif date_range == 'last_7_days':
            start_date = now - timedelta(days=7)
            queryset = SystemLog.objects.filter(timestamp__gte=start_date)
        elif date_range == 'last_30_days':
            start_date = now - timedelta(days=30)
            queryset = SystemLog.objects.filter(timestamp__gte=start_date)
        else:
            queryset = SystemLog.objects.all()

        # Count by level
        level_counts = queryset.values('level').annotate(count=Count('level'))
        level_stats = {item['level']: item['count'] for item in level_counts}

        # Count by category
        category_counts = queryset.values('category').annotate(count=Count('category'))
        category_stats = {item['category']: item['count'] for item in category_counts}

        # Prepare response data
        stats_data = {
            'all': queryset.count(),
            'debug': level_stats.get('debug', 0),
            'info': level_stats.get('info', 0),
            'warning': level_stats.get('warning', 0),
            'error': level_stats.get('error', 0),
            'critical': level_stats.get('critical', 0),
            'authentication': category_stats.get('authentication', 0),
            'database': category_stats.get('database', 0),
            'api': category_stats.get('api', 0),
            'payments': category_stats.get('payments', 0),
            'orders': category_stats.get('orders', 0),
            'system': category_stats.get('system', 0),
            'other': sum(category_stats.get(cat, 0) for cat in category_stats
                        if cat not in ['authentication', 'database', 'api', 'payments', 'orders', 'system'])
        }

        return Response({
            'success': True,
            'data': stats_data
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def export_logs_view(request):
    """
    API view to export logs in CSV or JSON format
    """
    try:
        # Get export parameters
        export_format = request.query_params.get('format', 'csv')
        level = request.query_params.get('level')
        category = request.query_params.get('category')
        date_range = request.query_params.get('date_range', 'today')
        search = request.query_params.get('search')

        # Build queryset with same filtering logic as list view
        queryset = SystemLog.objects.select_related('user').all()

        # Apply filters
        if level and level != 'all':
            queryset = queryset.filter(level=level)

        if category and category != 'all':
            queryset = queryset.filter(category=category)

        # Date range filtering
        now = timezone.now()
        if date_range == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            queryset = queryset.filter(timestamp__gte=start_date)
        elif date_range == 'yesterday':
            yesterday = now - timedelta(days=1)
            start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
            queryset = queryset.filter(timestamp__range=[start_date, end_date])
        elif date_range == 'last_7_days':
            start_date = now - timedelta(days=7)
            queryset = queryset.filter(timestamp__gte=start_date)
        elif date_range == 'last_30_days':
            start_date = now - timedelta(days=30)
            queryset = queryset.filter(timestamp__gte=start_date)

        # Search filtering
        if search:
            queryset = queryset.filter(
                Q(message__icontains=search) |
                Q(details__icontains=search) |
                Q(source__icontains=search) |
                Q(user__user_name__icontains=search) |
                Q(user__email__icontains=search)
            )

        queryset = queryset.order_by('-timestamp')

        # Generate filename
        timestamp = now.strftime('%Y%m%d_%H%M%S')
        filename = f'system_logs_{timestamp}.{export_format}'

        if export_format == 'csv':
            return export_logs_csv(queryset, filename)
        elif export_format == 'json':
            return export_logs_json(queryset, filename)
        else:
            return Response({
                'success': False,
                'error': {'message': 'Invalid export format'}
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def export_logs_csv(queryset, filename):
    """Export logs to CSV format"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'Timestamp', 'Level', 'Category', 'Message', 'Details',
        'User', 'IP Address', 'Source', 'Request ID'
    ])

    # Write data
    for log in queryset:
        writer.writerow([
            log.formatted_timestamp,
            log.level,
            log.get_category_display(),
            log.message,
            log.details or '',
            log.user_display,
            log.ip_address or '',
            log.source or '',
            log.request_id or ''
        ])

    return response


def export_logs_json(queryset, filename):
    """Export logs to JSON format"""
    response = HttpResponse(content_type='application/json')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    logs_data = []
    for log in queryset:
        logs_data.append({
            'id': str(log.id),
            'timestamp': log.formatted_timestamp,
            'level': log.level,
            'category': log.get_category_display(),
            'message': log.message,
            'details': log.details,
            'user': log.user_display,
            'ip_address': log.ip_address,
            'source': log.source,
            'request_id': log.request_id,
            'extra_data': log.extra_data
        })

    export_data = {
        'export_info': {
            'timestamp': timezone.now().isoformat(),
            'total_records': len(logs_data),
            'filename': filename
        },
        'logs': logs_data
    }

    response.write(json.dumps(export_data, indent=2))
    return response


@api_view(['POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def clear_old_logs_view(request):
    """
    API view to clear old logs
    """
    try:
        days_old = request.data.get('days_old', 30)

        # Calculate cutoff date
        cutoff_date = timezone.now() - timedelta(days=days_old)

        # Delete old logs
        deleted_count, _ = SystemLog.objects.filter(
            timestamp__lt=cutoff_date
        ).delete()

        return Response({
            'success': True,
            'data': {
                'deleted_count': deleted_count,
                'cutoff_date': cutoff_date.isoformat()
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
