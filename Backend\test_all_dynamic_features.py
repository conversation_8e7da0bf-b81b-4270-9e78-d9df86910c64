#!/usr/bin/env python3
"""
Comprehensive test script for all dynamic notification features
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()

def test_all_dynamic_features():
    """Test all dynamic notification features comprehensively"""
    print("🚀 COMPREHENSIVE DYNAMIC FEATURES TEST")
    print("=" * 70)
    
    # Setup authentication
    admin_user = User.objects.filter(role='admin').first()
    client = Client()
    
    login_response = client.post('/api/auth/login/', 
                                json.dumps({
                                    'user_name': admin_user.user_name,
                                    'password': 'admin123'
                                }),
                                content_type='application/json')
    
    access_token = login_response.json().get('data', {}).get('access_token')
    headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
    
    test_results = []
    
    # Test 1: Dynamic User Selection
    print("\n1️⃣ Testing Dynamic User Selection...")
    
    # Test user search
    search_response = client.get('/api/admin/notifications/users/search/?q=admin&limit=5', **headers)
    if search_response.status_code == 200:
        search_data = search_response.json()
        users = search_data.get('data', {}).get('users', [])
        print(f"   ✅ User search: Found {len(users)} users")
        test_results.append(('User Search', 'PASS'))
    else:
        print(f"   ❌ User search failed: {search_response.status_code}")
        test_results.append(('User Search', 'FAIL'))
    
    # Test 2: Dynamic Role Management
    print("\n2️⃣ Testing Dynamic Role Management...")
    
    roles_response = client.get('/api/admin/notifications/roles/', **headers)
    if roles_response.status_code == 200:
        roles_data = roles_response.json()
        roles = roles_data.get('data', {}).get('roles', [])
        print(f"   ✅ Role management: Found {len(roles)} roles")
        test_results.append(('Role Management', 'PASS'))
    else:
        print(f"   ❌ Role management failed: {roles_response.status_code}")
        test_results.append(('Role Management', 'FAIL'))
    
    # Test 3: Dynamic Template Management
    print("\n3️⃣ Testing Dynamic Template Management...")
    
    # Get templates
    templates_response = client.get('/api/admin/notifications/templates/', **headers)
    if templates_response.status_code == 200:
        print("   ✅ Template listing working")
        test_results.append(('Template Listing', 'PASS'))
        
        # Test template creation
        template_data = {
            'name': 'Dynamic Test Template',
            'template_type': 'general',
            'title_template': 'Test: {{title}}',
            'message_template': 'Hello {{name}}, this is a test: {{message}}',
            'supports_email': True,
            'supports_sms': False,
            'supports_push': False
        }
        
        create_response = client.post('/api/admin/notifications/templates/create/',
                                    json.dumps(template_data),
                                    content_type='application/json',
                                    **headers)
        
        if create_response.status_code == 200:
            print("   ✅ Template creation working")
            test_results.append(('Template Creation', 'PASS'))
            
            # Get the created template ID for cleanup
            created_template = create_response.json().get('data', {})
            template_id = created_template.get('id')
            
            if template_id:
                # Test template deletion
                delete_response = client.delete(f'/api/admin/notifications/templates/{template_id}/delete/',
                                              **headers)
                if delete_response.status_code == 200:
                    print("   ✅ Template deletion working")
                    test_results.append(('Template Deletion', 'PASS'))
                else:
                    print("   ❌ Template deletion failed")
                    test_results.append(('Template Deletion', 'FAIL'))
        else:
            print(f"   ❌ Template creation failed: {create_response.status_code}")
            test_results.append(('Template Creation', 'FAIL'))
    else:
        print(f"   ❌ Template listing failed: {templates_response.status_code}")
        test_results.append(('Template Listing', 'FAIL'))
    
    # Test 4: Real-time Updates (WebSocket simulation)
    print("\n4️⃣ Testing Real-time Updates...")
    
    # Test notification sending with real-time tracking
    realtime_notification = {
        'title': 'Real-time Test Notification',
        'message': 'Testing real-time updates and tracking',
        'notification_type': 'system',
        'recipient_type': 'user',
        'recipient_users': [admin_user.id],
        'channels': ['email'],
        'priority': 2
    }
    
    realtime_response = client.post('/api/admin/notifications/send/',
                                  json.dumps(realtime_notification),
                                  content_type='application/json',
                                  **headers)
    
    if realtime_response.status_code == 200:
        print("   ✅ Real-time notification sending working")
        test_results.append(('Real-time Notifications', 'PASS'))
    else:
        print(f"   ❌ Real-time notifications failed: {realtime_response.status_code}")
        test_results.append(('Real-time Notifications', 'FAIL'))
    
    # Test 5: Dynamic Settings
    print("\n5️⃣ Testing Dynamic Settings...")
    
    # Get current settings
    settings_response = client.get('/api/admin/notifications/settings/', **headers)
    if settings_response.status_code == 200:
        print("   ✅ Settings retrieval working")
        test_results.append(('Settings Retrieval', 'PASS'))
        
        # Test settings update
        settings_update = {
            'email_from_name': 'Afghan Sofra Dynamic Test',
            'max_emails_per_hour': 1500
        }
        
        update_response = client.patch('/api/admin/notifications/settings/',
                                     json.dumps(settings_update),
                                     content_type='application/json',
                                     **headers)
        
        if update_response.status_code == 200:
            print("   ✅ Settings update working")
            test_results.append(('Settings Update', 'PASS'))
        else:
            print(f"   ❌ Settings update failed: {update_response.status_code}")
            test_results.append(('Settings Update', 'FAIL'))
    else:
        print(f"   ❌ Settings retrieval failed: {settings_response.status_code}")
        test_results.append(('Settings Retrieval', 'FAIL'))
    
    # Test 6: Dynamic Filtering
    print("\n6️⃣ Testing Dynamic Filtering...")
    
    # Test filtered notifications
    filter_tests = [
        ('type=system', 'Type Filter'),
        ('status=sent', 'Status Filter'),
        ('priority=2', 'Priority Filter'),
        ('search=test', 'Search Filter')
    ]
    
    for filter_param, filter_name in filter_tests:
        filter_response = client.get(f'/api/admin/notifications/?{filter_param}', **headers)
        if filter_response.status_code == 200:
            print(f"   ✅ {filter_name} working")
            test_results.append((filter_name, 'PASS'))
        else:
            print(f"   ❌ {filter_name} failed")
            test_results.append((filter_name, 'FAIL'))
    
    # Test 7: Dynamic Notification Types
    print("\n7️⃣ Testing Dynamic Notification Types...")
    
    types_response = client.get('/api/admin/notifications/types/', **headers)
    if types_response.status_code == 200:
        types_data = types_response.json()
        types = types_data.get('data', {}).get('types', [])
        print(f"   ✅ Notification types: Found {len(types)} types")
        test_results.append(('Notification Types', 'PASS'))
    else:
        print(f"   ❌ Notification types failed: {types_response.status_code}")
        test_results.append(('Notification Types', 'FAIL'))
    
    # Test 8: Comprehensive Notification Sending
    print("\n8️⃣ Testing Comprehensive Notification Sending...")
    
    comprehensive_tests = [
        {
            'name': 'All Users Notification',
            'data': {
                'title': 'Dynamic Test: All Users',
                'message': 'Testing comprehensive dynamic notification system',
                'notification_type': 'general',
                'recipient_type': 'all',
                'channels': ['email'],
                'priority': 1
            }
        },
        {
            'name': 'Role-based with Multiple Channels',
            'data': {
                'title': 'Dynamic Test: Multi-channel',
                'message': 'Testing multiple delivery channels',
                'notification_type': 'system',
                'recipient_type': 'role',
                'recipient_roles': ['admin'],
                'channels': ['email', 'sms', 'push'],
                'priority': 3
            }
        }
    ]
    
    for test in comprehensive_tests:
        response = client.post('/api/admin/notifications/send/',
                             json.dumps(test['data']),
                             content_type='application/json',
                             **headers)
        
        if response.status_code == 200:
            print(f"   ✅ {test['name']} working")
            test_results.append((test['name'], 'PASS'))
        else:
            print(f"   ❌ {test['name']} failed")
            test_results.append((test['name'], 'FAIL'))
    
    # Test Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 70)
    
    total_tests = len(test_results)
    passed_tests = len([r for r in test_results if r[1] == 'PASS'])
    failed_tests = total_tests - passed_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    if failed_tests > 0:
        print("\n🔍 Failed Tests:")
        for test_name, result in test_results:
            if result == 'FAIL':
                print(f"   ❌ {test_name}")
    
    print("\n" + "=" * 70)
    if passed_tests == total_tests:
        print("🎉 ALL DYNAMIC FEATURES WORKING PERFECTLY!")
        print("🚀 The notification system is fully dynamic and ready for production!")
    else:
        print("⚠️ Some features need attention. Please review failed tests.")
    print("=" * 70)


if __name__ == '__main__':
    test_all_dynamic_features()
