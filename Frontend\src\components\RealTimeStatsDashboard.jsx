import React, { useState, useEffect } from 'react';
import { TrendingUp, Users, Send, CheckCircle, XCir<PERSON>, Clock, Wifi, WifiOff } from 'lucide-react';
import websocketService from '../services/websocketService';
import Card from './common/Card';

const RealTimeStatsDashboard = ({ initialStats = {} }) => {
  const [stats, setStats] = useState({
    sent_today: 0,
    delivery_rate: 0,
    pending: 0,
    failed: 0,
    email_sent: 0,
    email_delivered: 0,
    email_failed: 0,
    sms_sent: 0,
    sms_delivered: 0,
    sms_failed: 0,
    push_sent: 0,
    push_delivered: 0,
    push_failed: 0,
    ...initialStats
  });
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);

  useEffect(() => {
    // Check initial connection status
    setIsConnected(websocketService.getConnectionStatus().connected);

    // Set up event listeners
    const handleConnectionChange = (connected) => {
      setIsConnected(connected);
      if (connected) {
        // Subscribe to stats updates when connected
        websocketService.subscribeToNotificationStats();
      }
    };

    const handleStatsUpdate = (data) => {
      setStats(prev => ({
        ...prev,
        ...data
      }));
      setLastUpdate(new Date());
    };

    const handleNotificationSent = (data) => {
      // Add to recent activity
      setRecentActivity(prev => [
        {
          id: data.notification_id,
          type: 'sent',
          title: data.title,
          timestamp: new Date(),
          recipients: data.recipient_count
        },
        ...prev.slice(0, 9) // Keep only last 10 activities
      ]);

      // Update stats
      setStats(prev => ({
        ...prev,
        sent_today: prev.sent_today + 1
      }));
    };

    const handleDeliveryUpdate = (data) => {
      // Add to recent activity
      setRecentActivity(prev => [
        {
          id: data.notification_id,
          type: data.status,
          title: data.title,
          timestamp: new Date(),
          channel: data.channel
        },
        ...prev.slice(0, 9)
      ]);
    };

    // Subscribe to events
    websocketService.on('connected', () => handleConnectionChange(true));
    websocketService.on('disconnected', () => handleConnectionChange(false));
    websocketService.on('statsUpdate', handleStatsUpdate);
    websocketService.on('notificationSent', handleNotificationSent);
    websocketService.on('deliveryUpdate', handleDeliveryUpdate);

    // Subscribe to stats if already connected
    if (isConnected) {
      websocketService.subscribeToNotificationStats();
    }

    // Cleanup
    return () => {
      websocketService.off('connected', () => handleConnectionChange(true));
      websocketService.off('disconnected', () => handleConnectionChange(false));
      websocketService.off('statsUpdate', handleStatsUpdate);
      websocketService.off('notificationSent', handleNotificationSent);
      websocketService.off('deliveryUpdate', handleDeliveryUpdate);
      websocketService.unsubscribeFromNotificationStats();
    };
  }, [isConnected]);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'sent':
        return <Send size={14} className="text-blue-600" />;
      case 'delivered':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'failed':
        return <XCircle size={14} className="text-red-600" />;
      default:
        return <Clock size={14} className="text-gray-600" />;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'sent':
        return 'bg-blue-50 border-blue-200';
      case 'delivered':
        return 'bg-green-50 border-green-200';
      case 'failed':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Real-time Dashboard</h3>
        <div className="flex items-center gap-2">
          {isConnected ? (
            <>
              <Wifi size={16} className="text-green-600" />
              <span className="text-sm text-green-600">Live</span>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            </>
          ) : (
            <>
              <WifiOff size={16} className="text-red-600" />
              <span className="text-sm text-red-600">Offline</span>
            </>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Sent Today</p>
              <p className="text-2xl font-bold text-gray-900">{stats.sent_today}</p>
            </div>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Send size={20} className="text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Delivery Rate</p>
              <p className="text-2xl font-bold text-gray-900">{stats.delivery_rate}%</p>
            </div>
            <div className="p-2 bg-green-100 rounded-lg">
              <TrendingUp size={20} className="text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
            </div>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock size={20} className="text-yellow-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Failed</p>
              <p className="text-2xl font-bold text-gray-900">{stats.failed}</p>
            </div>
            <div className="p-2 bg-red-100 rounded-lg">
              <XCircle size={20} className="text-red-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Channel Performance */}
      <Card className="p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Channel Performance</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Email */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Email</span>
              <span className="text-sm text-gray-500">
                {stats.email_sent > 0 ? Math.round((stats.email_delivered / stats.email_sent) * 100) : 0}%
              </span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-xs text-gray-600">
                <span>Sent: {stats.email_sent}</span>
                <span>Delivered: {stats.email_delivered}</span>
                <span>Failed: {stats.email_failed}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${stats.email_sent > 0 ? (stats.email_delivered / stats.email_sent) * 100 : 0}%` 
                  }}
                />
              </div>
            </div>
          </div>

          {/* SMS */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">SMS</span>
              <span className="text-sm text-gray-500">
                {stats.sms_sent > 0 ? Math.round((stats.sms_delivered / stats.sms_sent) * 100) : 0}%
              </span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-xs text-gray-600">
                <span>Sent: {stats.sms_sent}</span>
                <span>Delivered: {stats.sms_delivered}</span>
                <span>Failed: {stats.sms_failed}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${stats.sms_sent > 0 ? (stats.sms_delivered / stats.sms_sent) * 100 : 0}%` 
                  }}
                />
              </div>
            </div>
          </div>

          {/* Push */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Push</span>
              <span className="text-sm text-gray-500">
                {stats.push_sent > 0 ? Math.round((stats.push_delivered / stats.push_sent) * 100) : 0}%
              </span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-xs text-gray-600">
                <span>Sent: {stats.push_sent}</span>
                <span>Delivered: {stats.push_delivered}</span>
                <span>Failed: {stats.push_failed}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${stats.push_sent > 0 ? (stats.push_delivered / stats.push_sent) * 100 : 0}%` 
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Recent Activity */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-gray-900">Recent Activity</h4>
          {lastUpdate && (
            <span className="text-xs text-gray-500">
              Last update: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
        </div>
        
        {recentActivity.length > 0 ? (
          <div className="space-y-2">
            {recentActivity.map((activity, index) => (
              <div 
                key={`${activity.id}-${index}`}
                className={`flex items-center gap-3 p-3 rounded-lg border ${getActivityColor(activity.type)}`}
              >
                {getActivityIcon(activity.type)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {activity.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {activity.type === 'sent' && activity.recipients && `${activity.recipients} recipients • `}
                    {activity.channel && `${activity.channel} • `}
                    {activity.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Clock size={32} className="mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No recent activity</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default RealTimeStatsDashboard;
