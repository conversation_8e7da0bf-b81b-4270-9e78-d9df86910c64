#!/usr/bin/env python3
"""
Test script to create a frontend-compatible admin user and get a token
"""

import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

def create_frontend_admin():
    """Create an admin user that can be used from the frontend"""
    
    print("🔧 Creating frontend-compatible admin user...")
    
    # Create or get admin user
    try:
        admin_user = User.objects.get(user_name='frontend_admin')
        print("✅ Using existing frontend admin user")
    except User.DoesNotExist:
        admin_user = User.objects.create_user(
            user_name='frontend_admin',
            name='Frontend Admin',
            phone='5551234567',
            email='<EMAIL>',
            role='admin',
            is_staff=True,
            is_superuser=True,
            is_verified=True
        )
        admin_user.set_password('admin123')
        admin_user.save()
        print("✅ Created new frontend admin user")
    
    print(f"👤 Admin user: {admin_user.name} ({admin_user.user_name})")
    print(f"📧 Email: {admin_user.email}")
    print(f"🔑 Role: {admin_user.role}")
    print(f"✅ Verified: {admin_user.is_verified}")
    
    return admin_user

def test_login_and_api():
    """Test login and API access"""
    
    print("\n🔐 Testing login and API access...")
    
    # Login
    login_data = {
        'user_name': 'frontend_admin',
        'password': 'admin123'
    }
    
    response = requests.post('http://127.0.0.1:8000/api/auth/login/', json=login_data)
    
    if response.status_code == 200:
        result = response.json()
        access_token = result.get('data', {}).get('access_token')
        
        print("✅ Login successful")
        print(f"🎫 Access token: {access_token[:50]}..." if access_token else "❌ No token")
        
        if access_token:
            # Test backup APIs
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            print("\n📊 Testing backup APIs...")
            
            # Test stats
            stats_response = requests.get('http://127.0.0.1:8000/api/admin/backup/stats/', headers=headers)
            print(f"Stats API: {stats_response.status_code} - {'✅' if stats_response.status_code == 200 else '❌'}")
            
            # Test list
            list_response = requests.get('http://127.0.0.1:8000/api/admin/backup/backups/', headers=headers)
            print(f"List API: {list_response.status_code} - {'✅' if list_response.status_code == 200 else '❌'}")
            
            if list_response.status_code == 200:
                list_data = list_response.json()
                backup_count = len(list_data.get('data', {}).get('results', []))
                print(f"📦 Backups found: {backup_count}")
            
            # Print token for manual use
            print(f"\n🎫 Use this token in frontend localStorage:")
            print(f"localStorage.setItem('afghanSofraUser', JSON.stringify({{")
            print(f"  access_token: '{access_token}',")
            print(f"  role: 'admin',")
            print(f"  name: 'Frontend Admin',")
            print(f"  username: 'frontend_admin'")
            print(f"}}));")
            
        return access_token
    else:
        print(f"❌ Login failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None

if __name__ == '__main__':
    admin_user = create_frontend_admin()
    token = test_login_and_api()
    
    if token:
        print("\n✅ Frontend admin setup complete!")
        print("💡 You can now use the frontend with this admin user:")
        print("   Username: frontend_admin")
        print("   Password: admin123")
    else:
        print("\n❌ Setup failed!")
