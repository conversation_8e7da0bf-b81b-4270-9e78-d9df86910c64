from django.db.models.signals import post_save, post_delete, pre_save
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from .utils import create_log, log_user_action, log_admin_action

User = get_user_model()


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login"""
    ip_address = None
    if request:
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
    
    create_log(
        level='info',
        category='authentication',
        message=f"User login successful: {user.user_name}",
        details=f"User role: {user.role}",
        user=user,
        ip_address=ip_address,
        user_agent=request.META.get('HTTP_USER_AGENT') if request else None,
        extra_data={
            'user_role': user.role,
            'login_time': timezone.now().isoformat()
        }
    )


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout"""
    if user:
        ip_address = None
        if request:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')
        
        create_log(
            level='info',
            category='authentication',
            message=f"User logout: {user.user_name}",
            user=user,
            ip_address=ip_address,
            user_agent=request.META.get('HTTP_USER_AGENT') if request else None,
            extra_data={
                'user_role': user.role,
                'logout_time': timezone.now().isoformat()
            }
        )


@receiver(user_login_failed)
def log_user_login_failed(sender, credentials, request, **kwargs):
    """Log failed login attempts"""
    ip_address = None
    if request:
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
    
    username = credentials.get('user_name', 'unknown')
    
    create_log(
        level='warning',
        category='security',
        message=f"Failed login attempt for user: {username}",
        details="Invalid credentials provided",
        ip_address=ip_address,
        user_agent=request.META.get('HTTP_USER_AGENT') if request else None,
        extra_data={
            'attempted_username': username,
            'failure_time': timezone.now().isoformat()
        }
    )


@receiver(post_save, sender=User)
def log_user_creation_or_update(sender, instance, created, **kwargs):
    """Log user creation and updates"""
    if created:
        create_log(
            level='info',
            category='user_management',
            message=f"New user created: {instance.user_name}",
            details=f"User role: {instance.role}, Email: {instance.email}",
            extra_data={
                'user_id': str(instance.id),
                'user_role': instance.role,
                'email': instance.email,
                'is_verified': instance.is_verified
            }
        )
    else:
        # Log significant user updates
        create_log(
            level='info',
            category='user_management',
            message=f"User updated: {instance.user_name}",
            details=f"User role: {instance.role}",
            user=instance,
            extra_data={
                'user_id': str(instance.id),
                'user_role': instance.role,
                'is_verified': instance.is_verified
            }
        )


# Log order-related events
try:
    from orders.models import Order
    
    @receiver(post_save, sender=Order)
    def log_order_events(sender, instance, created, **kwargs):
        """Log order creation and status changes"""
        if created:
            create_log(
                level='info',
                category='orders',
                message=f"New order created: {instance.order_id}",
                details=f"Total amount: {instance.total_amount}, Restaurant: {instance.restaurant.name if instance.restaurant else 'N/A'}",
                user=instance.customer,
                extra_data={
                    'order_id': instance.order_id,
                    'total_amount': str(instance.total_amount),
                    'restaurant_id': str(instance.restaurant.id) if instance.restaurant else None,
                    'status': instance.status
                }
            )
        else:
            # Log status changes
            create_log(
                level='info',
                category='orders',
                message=f"Order status updated: {instance.order_id}",
                details=f"New status: {instance.status}",
                user=instance.customer,
                extra_data={
                    'order_id': instance.order_id,
                    'status': instance.status,
                    'total_amount': str(instance.total_amount)
                }
            )

except ImportError:
    # Orders app not available
    pass


# Log restaurant-related events
try:
    from restaurant.models import Restaurant
    
    @receiver(post_save, sender=Restaurant)
    def log_restaurant_events(sender, instance, created, **kwargs):
        """Log restaurant creation and updates"""
        if created:
            create_log(
                level='info',
                category='restaurants',
                message=f"New restaurant registered: {instance.name}",
                details=f"Owner: {instance.owner.user_name if instance.owner else 'N/A'}",
                user=instance.owner,
                extra_data={
                    'restaurant_id': str(instance.id),
                    'restaurant_name': instance.name,
                    'owner_id': str(instance.owner.id) if instance.owner else None,
                    'is_verified': instance.is_verified
                }
            )
        else:
            # Log verification status changes
            if instance.is_verified:
                create_log(
                    level='info',
                    category='restaurants',
                    message=f"Restaurant verified: {instance.name}",
                    details=f"Restaurant is now active and verified",
                    user=instance.owner,
                    extra_data={
                        'restaurant_id': str(instance.id),
                        'restaurant_name': instance.name,
                        'is_verified': True
                    }
                )

except ImportError:
    # Restaurant app not available
    pass


# Log backup-related events
try:
    from backup_management.models import BackupJob
    
    @receiver(post_save, sender=BackupJob)
    def log_backup_events(sender, instance, created, **kwargs):
        """Log backup job events"""
        if created:
            create_log(
                level='info',
                category='backup',
                message=f"Backup job created: {instance.name}",
                details=f"Type: {instance.backup_type}, Created by: {instance.created_by.user_name if instance.created_by else 'System'}",
                user=instance.created_by,
                extra_data={
                    'backup_id': str(instance.id),
                    'backup_type': instance.backup_type,
                    'backup_name': instance.name
                }
            )
        else:
            # Log status changes
            if instance.status == 'completed':
                create_log(
                    level='info',
                    category='backup',
                    message=f"Backup completed: {instance.name}",
                    details=f"File size: {instance.formatted_file_size}",
                    user=instance.created_by,
                    extra_data={
                        'backup_id': str(instance.id),
                        'backup_type': instance.backup_type,
                        'file_size': instance.file_size,
                        'status': instance.status
                    }
                )
            elif instance.status == 'failed':
                create_log(
                    level='error',
                    category='backup',
                    message=f"Backup failed: {instance.name}",
                    details=instance.error_message,
                    user=instance.created_by,
                    extra_data={
                        'backup_id': str(instance.id),
                        'backup_type': instance.backup_type,
                        'error_message': instance.error_message,
                        'status': instance.status
                    }
                )

except ImportError:
    # Backup management app not available
    pass
