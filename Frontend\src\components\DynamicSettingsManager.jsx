import React, { useState, useEffect } from 'react';
import { Settings, Save, TestTube, Mail, MessageSquare, Smartphone, AlertCircle, CheckCircle, Loader } from 'lucide-react';
import Button from './common/Button';
import Card from './common/Card';
import Input from './common/Input';

const DynamicSettingsManager = () => {
  const [settings, setSettings] = useState({
    email_enabled: true,
    email_from_name: '',
    email_from_address: '',
    email_reply_to: '',
    sms_enabled: false,
    sms_provider: '',
    sms_from_number: '',
    push_enabled: false,
    push_provider: '',
    max_emails_per_hour: 1000,
    max_sms_per_hour: 500,
    max_push_per_hour: 2000,
    max_retry_attempts: 3,
    retry_delay_minutes: 5,
    default_email_template: ''
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState({});
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [testResults, setTestResults] = useState({});

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/notifications/settings/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data.data || {});
      } else {
        setError('Failed to load settings');
      }
    } catch (err) {
      setError('Error loading settings');
      console.error('Settings loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/admin/notifications/settings/', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        setSuccess('Settings saved successfully!');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        const errorData = await response.json();
        setError(errorData.error?.message || 'Failed to save settings');
      }
    } catch (err) {
      setError('Error saving settings');
      console.error('Settings save error:', err);
    } finally {
      setSaving(false);
    }
  };

  const testConnection = async (channel) => {
    try {
      setTesting(prev => ({ ...prev, [channel]: true }));
      setTestResults(prev => ({ ...prev, [channel]: null }));

      const response = await fetch('/api/admin/notifications/test/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ channel })
      });

      const result = await response.json();
      
      setTestResults(prev => ({
        ...prev,
        [channel]: {
          success: response.ok,
          message: result.message || (response.ok ? 'Test successful!' : 'Test failed')
        }
      }));

      // Clear test result after 5 seconds
      setTimeout(() => {
        setTestResults(prev => ({ ...prev, [channel]: null }));
      }, 5000);

    } catch (err) {
      setTestResults(prev => ({
        ...prev,
        [channel]: {
          success: false,
          message: 'Test connection failed'
        }
      }));
      console.error(`${channel} test error:`, err);
    } finally {
      setTesting(prev => ({ ...prev, [channel]: false }));
    }
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const getChannelIcon = (channel) => {
    switch (channel) {
      case 'email':
        return <Mail size={20} className="text-blue-600" />;
      case 'sms':
        return <MessageSquare size={20} className="text-green-600" />;
      case 'push':
        return <Smartphone size={20} className="text-purple-600" />;
      default:
        return <Settings size={20} className="text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        <span className="ml-3 text-gray-600">Loading settings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Notification Settings</h2>
          <p className="text-gray-600">Configure notification channels and delivery settings</p>
        </div>
        <Button
          variant="primary"
          icon={<Save size={16} />}
          onClick={saveSettings}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
          <AlertCircle size={16} className="text-red-600" />
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {success && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-2">
          <CheckCircle size={16} className="text-green-600" />
          <p className="text-green-600 text-sm">{success}</p>
        </div>
      )}

      {/* Email Settings */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            {getChannelIcon('email')}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Email Settings</h3>
              <p className="text-sm text-gray-600">Configure email delivery settings</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {testResults.email && (
              <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
                testResults.email.success 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {testResults.email.success ? <CheckCircle size={14} /> : <AlertCircle size={14} />}
                {testResults.email.message}
              </div>
            )}
            <Button
              size="small"
              variant="outline"
              icon={testing.email ? <Loader size={14} className="animate-spin" /> : <TestTube size={14} />}
              onClick={() => testConnection('email')}
              disabled={testing.email || !settings.email_enabled}
            >
              Test Email
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.email_enabled}
                onChange={(e) => handleSettingChange('email_enabled', e.target.checked)}
                className="mr-3"
              />
              <span className="font-medium">Enable Email Notifications</span>
            </label>
          </div>

          {settings.email_enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  From Name
                </label>
                <Input
                  value={settings.email_from_name}
                  onChange={(e) => handleSettingChange('email_from_name', e.target.value)}
                  placeholder="Afghan Sofra"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  From Email
                </label>
                <Input
                  type="email"
                  value={settings.email_from_address}
                  onChange={(e) => handleSettingChange('email_from_address', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reply To Email
                </label>
                <Input
                  type="email"
                  value={settings.email_reply_to}
                  onChange={(e) => handleSettingChange('email_reply_to', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Emails per Hour
                </label>
                <Input
                  type="number"
                  value={settings.max_emails_per_hour}
                  onChange={(e) => handleSettingChange('max_emails_per_hour', parseInt(e.target.value))}
                  min="1"
                  max="10000"
                />
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* SMS Settings */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            {getChannelIcon('sms')}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">SMS Settings</h3>
              <p className="text-sm text-gray-600">Configure SMS delivery settings</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {testResults.sms && (
              <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
                testResults.sms.success 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {testResults.sms.success ? <CheckCircle size={14} /> : <AlertCircle size={14} />}
                {testResults.sms.message}
              </div>
            )}
            <Button
              size="small"
              variant="outline"
              icon={testing.sms ? <Loader size={14} className="animate-spin" /> : <TestTube size={14} />}
              onClick={() => testConnection('sms')}
              disabled={testing.sms || !settings.sms_enabled}
            >
              Test SMS
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.sms_enabled}
                onChange={(e) => handleSettingChange('sms_enabled', e.target.checked)}
                className="mr-3"
              />
              <span className="font-medium">Enable SMS Notifications</span>
            </label>
          </div>

          {settings.sms_enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMS Provider
                </label>
                <select
                  value={settings.sms_provider}
                  onChange={(e) => handleSettingChange('sms_provider', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select Provider</option>
                  <option value="twilio">Twilio</option>
                  <option value="aws_sns">AWS SNS</option>
                  <option value="nexmo">Nexmo</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  From Number
                </label>
                <Input
                  value={settings.sms_from_number}
                  onChange={(e) => handleSettingChange('sms_from_number', e.target.value)}
                  placeholder="+**********"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max SMS per Hour
                </label>
                <Input
                  type="number"
                  value={settings.max_sms_per_hour}
                  onChange={(e) => handleSettingChange('max_sms_per_hour', parseInt(e.target.value))}
                  min="1"
                  max="5000"
                />
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Push Notification Settings */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            {getChannelIcon('push')}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Push Notification Settings</h3>
              <p className="text-sm text-gray-600">Configure push notification delivery</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {testResults.push && (
              <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
                testResults.push.success 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {testResults.push.success ? <CheckCircle size={14} /> : <AlertCircle size={14} />}
                {testResults.push.message}
              </div>
            )}
            <Button
              size="small"
              variant="outline"
              icon={testing.push ? <Loader size={14} className="animate-spin" /> : <TestTube size={14} />}
              onClick={() => testConnection('push')}
              disabled={testing.push || !settings.push_enabled}
            >
              Test Push
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={settings.push_enabled}
                onChange={(e) => handleSettingChange('push_enabled', e.target.checked)}
                className="mr-3"
              />
              <span className="font-medium">Enable Push Notifications</span>
            </label>
          </div>

          {settings.push_enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Push Provider
                </label>
                <select
                  value={settings.push_provider}
                  onChange={(e) => handleSettingChange('push_provider', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select Provider</option>
                  <option value="firebase">Firebase FCM</option>
                  <option value="apns">Apple Push Notification</option>
                  <option value="onesignal">OneSignal</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Push per Hour
                </label>
                <Input
                  type="number"
                  value={settings.max_push_per_hour}
                  onChange={(e) => handleSettingChange('max_push_per_hour', parseInt(e.target.value))}
                  min="1"
                  max="20000"
                />
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* General Settings */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <Settings size={20} className="text-gray-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">General Settings</h3>
            <p className="text-sm text-gray-600">Configure retry and delivery settings</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Retry Attempts
            </label>
            <Input
              type="number"
              value={settings.max_retry_attempts}
              onChange={(e) => handleSettingChange('max_retry_attempts', parseInt(e.target.value))}
              min="0"
              max="10"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Retry Delay (minutes)
            </label>
            <Input
              type="number"
              value={settings.retry_delay_minutes}
              onChange={(e) => handleSettingChange('retry_delay_minutes', parseInt(e.target.value))}
              min="1"
              max="60"
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DynamicSettingsManager;
