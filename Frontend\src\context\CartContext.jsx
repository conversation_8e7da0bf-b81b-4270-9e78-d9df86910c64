import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { cartApi } from "../utils/orderApi";
import { useAuth } from "./AuthContext";

const CartContext = createContext(null);

export const useCart = () => useContext(CartContext);

export const CartProvider = ({ children }) => {
  const { user } = useAuth();
  const [cart, setCart] = useState({
    restaurantId: null,
    restaurantName: "",
    items: [],
    subtotal: 0,
    deliveryFee: 0,
    total: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Load cart from localStorage on component mount
    const savedCart = localStorage.getItem("afghanSofraCart");
    console.log("🔍 Raw cart from localStorage:", savedCart);

    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        console.log("🔍 Parsed cart from localStorage:", parsedCart);

        // Ensure cart has the expected structure
        const normalizedCart = {
          restaurantId: parsedCart.restaurantId || null,
          restaurantName: parsedCart.restaurantName || "",
          items: Array.isArray(parsedCart.items) ? parsedCart.items : [],
          subtotal: parsedCart.subtotal || 0,
          deliveryFee: parsedCart.deliveryFee || 0,
          total: parsedCart.total || 0,
        };

        console.log("🔍 Normalized cart:", normalizedCart);
        setCart(normalizedCart);
      } catch (error) {
        console.error("❌ Error parsing cart from localStorage:", error);
        // Reset cart if parsing fails
        const defaultCart = {
          restaurantId: null,
          restaurantName: "",
          items: [],
          subtotal: 0,
          deliveryFee: 0,
          total: 0,
        };
        console.log("🔍 Using default cart:", defaultCart);
        setCart(defaultCart);
      }
    } else {
      console.log("🔍 No saved cart found, using default");
    }
  }, []);

  useEffect(() => {
    // Save cart to localStorage whenever it changes
    console.log("💾 Saving cart to localStorage:", cart);
    localStorage.setItem("afghanSofraCart", JSON.stringify(cart));

    // Debug: verify what was actually saved
    const saved = localStorage.getItem("afghanSofraCart");
    console.log("💾 Verified saved cart:", JSON.parse(saved));
  }, [cart]);

  const addToCart = (restaurantId, restaurantName, item, quantity = 1) => {
    console.log("🔍 Adding to cart:", { restaurantId, restaurantName, item: item.name, quantity });
    console.log("🔍 Current cart state:", cart);
    console.log("🔍 Current cart restaurant ID:", cart.restaurantId);
    console.log("🔍 Current cart items count:", cart.items?.length || 0);

    // Ensure restaurant ID is a number for consistency
    const normalizedRestaurantId = parseInt(restaurantId, 10);
    const currentRestaurantId = cart.restaurantId ? parseInt(cart.restaurantId, 10) : null;

    console.log("🔍 Normalized restaurant ID:", normalizedRestaurantId);
    console.log("🔍 Current restaurant ID:", currentRestaurantId);

    // Check if trying to add item from different restaurant
    if (currentRestaurantId && currentRestaurantId !== normalizedRestaurantId) {
      return {
        success: false,
        error: "Items in cart are from another restaurant. Clear cart first.",
      };
    }

    setCart((prevCart) => {
      // Check if item already exists in cart
      const existingItemIndex = prevCart.items.findIndex(
        (cartItem) => cartItem.id === item.id
      );

      let newItems = [...prevCart.items];

      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        newItems[existingItemIndex] = {
          ...newItems[existingItemIndex],
          quantity: newItems[existingItemIndex].quantity + quantity,
        };
      } else {
        // Add new item
        newItems.push({
          ...item,
          quantity,
        });
      }

      // Calculate new totals
      const subtotal = newItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );

      // For this demo, we'll use a fixed delivery fee or calculate based on subtotal
      const deliveryFee = normalizedRestaurantId ? 5 : 0; // Example fixed fee

      const newCart = {
        restaurantId: normalizedRestaurantId,
        restaurantName,
        items: newItems,
        subtotal,
        deliveryFee,
        total: subtotal + deliveryFee,
      };

      console.log("🔍 New cart after adding item:", newCart);
      console.log("🔍 New cart items count:", newItems.length);
      console.log("🔍 New cart restaurant ID:", normalizedRestaurantId);
      console.log("🔍 New cart restaurant name:", restaurantName);
      return newCart;
    });

    return { success: true };
  };

  const updateItemQuantity = (itemId, quantity) => {
    if (quantity <= 0) {
      return removeFromCart(itemId);
    }

    setCart((prevCart) => {
      const newItems = prevCart.items.map((item) =>
        item.id === itemId ? { ...item, quantity } : item
      );

      const subtotal = newItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );

      return {
        ...prevCart,
        items: newItems,
        subtotal,
        total: subtotal + prevCart.deliveryFee,
      };
    });
  };

  const removeFromCart = (itemId) => {
    setCart((prevCart) => {
      const newItems = prevCart.items.filter((item) => item.id !== itemId);

      const subtotal = newItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );

      // If cart becomes empty, reset restaurant info
      const newRestaurantId =
        newItems.length > 0 ? prevCart.restaurantId : null;
      const newRestaurantName =
        newItems.length > 0 ? prevCart.restaurantName : "";
      const deliveryFee = newItems.length > 0 ? prevCart.deliveryFee : 0;

      return {
        restaurantId: newRestaurantId,
        restaurantName: newRestaurantName,
        items: newItems,
        subtotal,
        deliveryFee,
        total: subtotal + deliveryFee,
      };
    });
  };

  const clearCart = useCallback(async () => {
    console.log("🧹 Clearing cart - removing all items and restaurant data");
    const clearedCart = {
      restaurantId: null,
      restaurantName: "",
      items: [],
      subtotal: 0,
      deliveryFee: 0,
      total: 0,
    };
    setCart(clearedCart);

    // Immediately save cleared cart to localStorage to ensure consistency
    localStorage.setItem("afghanSofraCart", JSON.stringify(clearedCart));

    // If user is logged in, also clear cart from API to prevent restoration on next login
    if (user) {
      try {
        console.log("🗑️ Clearing cart from API to prevent restoration on next login");
        const result = await cartApi.deleteCart();
        if (result.success) {
          console.log("✅ Cart cleared from API successfully");
        } else {
          console.error("❌ Failed to clear cart from API:", result.error);
        }
      } catch (error) {
        console.error("❌ Error clearing cart from API:", error);
      }
    }

    console.log("✅ Cart cleared successfully from local storage and API");
  }, [user]);

  /**
   * Load cart from API
   */
  const loadCartFromAPI = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const result = await cartApi.getCart();

      if (result.success && result.data) {
        const apiCart = result.data;

        // Transform API cart to local cart format
        const transformedCart = {
          restaurantId: apiCart.restaurant?.id || null,
          restaurantName: apiCart.restaurant?.name || "",
          items:
            apiCart.items?.map((item) => ({
              id: item.menu_item.id,
              name: item.menu_item.name,
              price: parseFloat(item.menu_item.price),
              image: item.menu_item.image,
              description: item.menu_item.description,
              is_vegetarian: item.menu_item.is_vegetarian,
              is_available: item.menu_item.is_available,
              preparation_time: item.menu_item.preparation_time,
              quantity: item.quantity,
              special_requests: item.special_requests || "",
            })) || [],
          subtotal: 0,
          deliveryFee: parseFloat(apiCart.restaurant?.delivery_fee || 0),
          total: 0,
        };

        // Calculate totals
        const subtotal = transformedCart.items.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0
        );
        transformedCart.subtotal = subtotal;
        transformedCart.total = subtotal + transformedCart.deliveryFee;

        setCart(transformedCart);
      }
    } catch (err) {
      console.error("Load cart from API error:", err);
      setError("Failed to load cart");
    } finally {
      setLoading(false);
    }
  }, [user]);

  /**
   * Save cart to API
   */
  const saveCartToAPI = useCallback(async () => {
    if (!user || !cart.restaurantId || cart.items.length === 0) return;

    setLoading(true);
    setError(null);

    try {
      const cartData = {
        restaurant_id: cart.restaurantId,
        items: cart.items.map((item) => ({
          menu_item_id: item.id,
          quantity: item.quantity,
          special_requests: item.special_requests || "",
        })),
      };

      const result = await cartApi.saveCart(cartData);

      if (!result.success) {
        setError(result.error);
        console.error("Failed to save cart:", result.error);
      }
    } catch (err) {
      console.error("Save cart to API error:", err);
      setError("Failed to save cart");
    } finally {
      setLoading(false);
    }
  }, [user, cart.restaurantId, cart.items]);

  /**
   * Delete cart from API
   */
  const deleteCartFromAPI = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const result = await cartApi.deleteCart();

      if (result.success) {
        await clearCart();
      } else {
        setError(result.error);
        console.error("Failed to delete cart:", result.error);
      }
    } catch (err) {
      console.error("Delete cart from API error:", err);
      setError("Failed to delete cart");
    } finally {
      setLoading(false);
    }
  };

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Load cart from API when user logs in, clear cart when user logs out
  useEffect(() => {
    if (user) {
      loadCartFromAPI();
    } else {
      // User logged out - clear the cart state
      console.log("🚪 User logged out - clearing cart state");
      clearCart(); // This is fine as async function - useEffect doesn't need to wait
    }
  }, [user, loadCartFromAPI, clearCart]);

  // Auto-save cart to API when cart changes (debounced)
  useEffect(() => {
    if (user && cart.items.length > 0) {
      const timeoutId = setTimeout(() => {
        saveCartToAPI();
      }, 1000); // Save after 1 second of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [cart, user, saveCartToAPI]);

  const value = {
    cart,
    loading,
    error,
    addToCart,
    updateItemQuantity,
    removeFromCart,
    clearCart,
    loadCartFromAPI,
    saveCartToAPI,
    deleteCartFromAPI,
    clearError,
    itemCount: cart.items.reduce((count, item) => count + item.quantity, 0),
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};
