import os
import subprocess
import threading
import time
import zipfile
import shutil
from datetime import datetime
from django.conf import settings
from django.core.management import call_command
from django.db import connection
from .models import BackupJob, BackupSettings
import logging

logger = logging.getLogger(__name__)


class BackupService:
    """Service class for handling backup operations"""
    
    def __init__(self):
        self.backup_settings = BackupSettings.get_settings()
        self.backup_dir = os.path.join(settings.BASE_DIR, self.backup_settings.backup_directory)
        self.ensure_backup_directory()
    
    def ensure_backup_directory(self):
        """Ensure backup directory exists"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir, exist_ok=True)
    
    def create_backup(self, backup_job):
        """Create a backup based on the job type"""
        try:
            backup_job.mark_started()
            
            if backup_job.backup_type == 'full':
                return self._create_full_backup(backup_job)
            elif backup_job.backup_type == 'database':
                return self._create_database_backup(backup_job)
            elif backup_job.backup_type == 'partial':
                return self._create_partial_backup(backup_job)
            elif backup_job.backup_type == 'custom':
                return self._create_custom_backup(backup_job)
            else:
                raise ValueError(f"Unknown backup type: {backup_job.backup_type}")
                
        except Exception as e:
            logger.error(f"Backup failed for job {backup_job.id}: {str(e)}")
            backup_job.mark_failed(str(e))
            return False
    
    def _create_full_backup(self, backup_job):
        """Create a full system backup"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"full_backup_{timestamp}.zip"
        backup_path = os.path.join(self.backup_dir, backup_filename)
        
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Update progress
                backup_job.update_progress(10)
                
                # Backup database
                db_backup_path = self._backup_database(backup_job)
                if db_backup_path:
                    zipf.write(db_backup_path, 'database.json')
                    os.remove(db_backup_path)  # Clean up temp file
                
                backup_job.update_progress(40)
                
                # Backup media files
                media_root = getattr(settings, 'MEDIA_ROOT', None)
                if media_root and os.path.exists(media_root):
                    self._add_directory_to_zip(zipf, media_root, 'media/')
                
                backup_job.update_progress(70)
                
                # Backup static files (if collected)
                static_root = getattr(settings, 'STATIC_ROOT', None)
                if static_root and os.path.exists(static_root):
                    self._add_directory_to_zip(zipf, static_root, 'static/')
                
                backup_job.update_progress(90)
                
                # Add configuration files
                self._add_config_files(zipf)
                
                backup_job.update_progress(100)
            
            # Get file size
            file_size = os.path.getsize(backup_path)
            backup_job.mark_completed(backup_path, file_size)
            
            logger.info(f"Full backup completed: {backup_path}")
            return True
            
        except Exception as e:
            if os.path.exists(backup_path):
                os.remove(backup_path)
            raise e
    
    def _create_database_backup(self, backup_job):
        """Create a database-only backup"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"database_backup_{timestamp}.json"
        backup_path = os.path.join(self.backup_dir, backup_filename)

        try:
            backup_job.update_progress(20)

            # Create database backup using Django's dumpdata
            with open(backup_path, 'w', encoding='utf-8') as f:
                call_command('dumpdata',
                           '--natural-foreign',
                           '--natural-primary',
                           '--indent', '2',
                           stdout=f)

            backup_job.update_progress(80)
            
            # Compress if enabled
            if self.backup_settings.compression_enabled:
                compressed_path = backup_path + '.gz'
                import gzip
                with open(backup_path, 'rb') as f_in:
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                os.remove(backup_path)
                backup_path = compressed_path
            
            backup_job.update_progress(100)
            
            # Get file size
            file_size = os.path.getsize(backup_path)
            backup_job.mark_completed(backup_path, file_size)
            
            logger.info(f"Database backup completed: {backup_path}")
            return True
            
        except Exception as e:
            if os.path.exists(backup_path):
                os.remove(backup_path)
            raise e
    
    def _create_partial_backup(self, backup_job):
        """Create a partial backup (user data only)"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"partial_backup_{timestamp}.json"
        backup_path = os.path.join(self.backup_dir, backup_filename)

        try:
            backup_job.update_progress(20)

            # Define apps to include in partial backup
            apps_to_backup = [
                'users',
                'customer',
                'restaurant',
                'orders',
                'financial_management'
            ]

            with open(backup_path, 'w', encoding='utf-8') as f:
                call_command('dumpdata',
                           *apps_to_backup,
                           '--natural-foreign',
                           '--natural-primary',
                           '--indent', '2',
                           stdout=f)
            
            backup_job.update_progress(100)
            
            # Get file size
            file_size = os.path.getsize(backup_path)
            backup_job.mark_completed(backup_path, file_size)
            
            logger.info(f"Partial backup completed: {backup_path}")
            return True
            
        except Exception as e:
            if os.path.exists(backup_path):
                os.remove(backup_path)
            raise e
    
    def _create_custom_backup(self, backup_job):
        """Create a custom backup based on options"""
        options = backup_job.options or {}
        include_database = options.get('include_database', True)
        include_media = options.get('include_media', False)
        include_static = options.get('include_static', False)
        selected_apps = options.get('selected_apps', [])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"custom_backup_{timestamp}.zip"
        backup_path = os.path.join(self.backup_dir, backup_filename)
        
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                progress = 0
                total_steps = sum([include_database, include_media, include_static]) + len(selected_apps)
                step_size = 90 / max(total_steps, 1)
                
                if include_database:
                    db_backup_path = self._backup_database(backup_job)
                    if db_backup_path:
                        zipf.write(db_backup_path, 'database.json')
                        os.remove(db_backup_path)
                    progress += step_size
                    backup_job.update_progress(int(progress))
                
                if include_media:
                    media_root = getattr(settings, 'MEDIA_ROOT', None)
                    if media_root and os.path.exists(media_root):
                        self._add_directory_to_zip(zipf, media_root, 'media/')
                    progress += step_size
                    backup_job.update_progress(int(progress))
                
                if include_static:
                    static_root = getattr(settings, 'STATIC_ROOT', None)
                    if static_root and os.path.exists(static_root):
                        self._add_directory_to_zip(zipf, static_root, 'static/')
                    progress += step_size
                    backup_job.update_progress(int(progress))
                
                # Backup selected apps
                for app in selected_apps:
                    app_backup_path = self._backup_app_data(app)
                    if app_backup_path:
                        zipf.write(app_backup_path, f'{app}_data.json')
                        os.remove(app_backup_path)
                    progress += step_size
                    backup_job.update_progress(int(progress))
                
                backup_job.update_progress(100)
            
            # Get file size
            file_size = os.path.getsize(backup_path)
            backup_job.mark_completed(backup_path, file_size)
            
            logger.info(f"Custom backup completed: {backup_path}")
            return True
            
        except Exception as e:
            if os.path.exists(backup_path):
                os.remove(backup_path)
            raise e
    
    def _backup_database(self, backup_job):
        """Create a temporary database backup file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_path = os.path.join(self.backup_dir, f"temp_db_{timestamp}.json")

        try:
            with open(temp_path, 'w', encoding='utf-8') as f:
                call_command('dumpdata',
                           '--natural-foreign',
                           '--natural-primary',
                           '--indent', '2',
                           stdout=f)
            return temp_path
        except Exception as e:
            if os.path.exists(temp_path):
                os.remove(temp_path)
            logger.error(f"Database backup failed: {str(e)}")
            return None
    
    def _backup_app_data(self, app_name):
        """Backup data for a specific app"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_path = os.path.join(self.backup_dir, f"temp_{app_name}_{timestamp}.json")

        try:
            with open(temp_path, 'w', encoding='utf-8') as f:
                call_command('dumpdata',
                           app_name,
                           '--natural-foreign',
                           '--natural-primary',
                           '--indent', '2',
                           stdout=f)
            return temp_path
        except Exception as e:
            if os.path.exists(temp_path):
                os.remove(temp_path)
            logger.error(f"App {app_name} backup failed: {str(e)}")
            return None
    
    def _add_directory_to_zip(self, zipf, directory, arcname_prefix):
        """Add a directory to zip file"""
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.join(arcname_prefix, os.path.relpath(file_path, directory))
                zipf.write(file_path, arcname)
    
    def _add_config_files(self, zipf):
        """Add configuration files to backup"""
        config_files = [
            'requirements.txt',
            'manage.py',
        ]
        
        for config_file in config_files:
            file_path = os.path.join(settings.BASE_DIR, config_file)
            if os.path.exists(file_path):
                zipf.write(file_path, f'config/{config_file}')
    
    def create_backup_async(self, backup_job):
        """Create backup in a separate thread"""
        def backup_thread():
            self.create_backup(backup_job)
        
        thread = threading.Thread(target=backup_thread)
        thread.daemon = True
        thread.start()
        return thread
