#!/usr/bin/env python3
"""
Test script to verify the delivery agent dashboard returns dynamic data
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:8000/api"

def test_delivery_agent_login():
    """Test delivery agent login"""
    print("🔐 Testing delivery agent login...")
    
    login_data = {
        "user_name": "DA007",
        "password": "DA8645YR"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"Debug - Login response: {result}")

            # Handle different response formats
            if result.get('status') == 'success' or result.get('message') == 'Login successful':
                # Try different token locations
                token = None
                if 'data' in result and 'access_token' in result['data']:
                    token = result['data']['access_token']
                elif 'access_token' in result:
                    token = result['access_token']
                elif 'data' in result and 'access' in result['data']:
                    token = result['data']['access']

                if token:
                    print("✅ Login successful!")
                    print(f"   Token: {token[:20]}...")
                    return token
                else:
                    print(f"❌ Token not found in response: {result}")
                    return None
            else:
                print(f"❌ Login failed: {result.get('message')}")
                return None
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None

def test_dashboard_api(token):
    """Test the dashboard API"""
    print("\n📊 Testing dashboard API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/delivery-agent/dashboard/", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                data = result['data']
                
                print("✅ Dashboard API successful!")
                print("\n📈 Dashboard Data:")
                print("="*50)
                
                # Agent Info
                agent_info = data.get('agent_info', {})
                print(f"Agent ID: {agent_info.get('agent_id')}")
                print(f"Name: {agent_info.get('full_name')}")
                print(f"Status: {agent_info.get('application_status')}")
                print(f"Employment: {agent_info.get('employment_status')}")
                print(f"Online: {agent_info.get('is_online')}")
                print(f"Clocked In: {agent_info.get('is_clocked_in')}")
                print(f"Rating: {agent_info.get('rating')}")
                
                # Today's Stats
                today_stats = data.get('today_stats', {})
                print(f"\n📅 Today's Statistics:")
                print(f"Deliveries Completed: {today_stats.get('deliveries_completed', 0)}")
                print(f"Earnings: AFN {today_stats.get('earnings', 0.00)}")
                print(f"Hours Worked: {today_stats.get('hours_worked', 0)}")
                print(f"Completion Rate: {today_stats.get('completion_rate', 0)}%")
                print(f"Assigned Orders: {today_stats.get('assigned_orders', 0)}")
                
                # Overall Stats
                overall_stats = data.get('overall_stats', {})
                print(f"\n📊 Overall Statistics:")
                print(f"Total Deliveries: {overall_stats.get('total_deliveries', 0)}")
                print(f"Successful Deliveries: {overall_stats.get('successful_deliveries', 0)}")
                print(f"Total Earnings: AFN {overall_stats.get('total_earnings', 0.00)}")
                print(f"Average Rating: {overall_stats.get('average_rating', 0.0)}")
                
                return True
            else:
                print(f"❌ Dashboard API failed: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard API error: {str(e)}")
        return False

def test_assigned_orders_api(token):
    """Test the assigned orders API"""
    print("\n📦 Testing assigned orders API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/delivery-agent/assigned-orders/", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                data = result['data']
                orders = data.get('orders', [])
                stats = data.get('statistics', {})
                
                print("✅ Assigned orders API successful!")
                print(f"\n📦 Orders: {len(orders)} orders")
                print(f"📊 Statistics:")
                print(f"   Total Assigned: {stats.get('total_assigned', 0)}")
                print(f"   Pending Pickup: {stats.get('pending_pickup', 0)}")
                print(f"   In Delivery: {stats.get('in_delivery', 0)}")
                print(f"   Completed Today: {stats.get('completed_today', 0)}")
                print(f"   Total Completed: {stats.get('total_completed', 0)}")
                
                return True
            else:
                print(f"❌ Assigned orders API failed: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Assigned orders API error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Dynamic Delivery Agent Dashboard")
    print("="*60)
    
    # Test login
    token = test_delivery_agent_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        sys.exit(1)
    
    # Test dashboard API
    dashboard_success = test_dashboard_api(token)
    
    # Test assigned orders API
    orders_success = test_assigned_orders_api(token)
    
    # Summary
    print("\n" + "="*60)
    print("🏁 Test Summary:")
    print(f"   Dashboard API: {'✅ PASS' if dashboard_success else '❌ FAIL'}")
    print(f"   Assigned Orders API: {'✅ PASS' if orders_success else '❌ FAIL'}")
    
    if dashboard_success and orders_success:
        print("\n🎉 All tests passed! Dashboard is showing dynamic data.")
    else:
        print("\n⚠️ Some tests failed. Check the API responses above.")

if __name__ == '__main__':
    main()
