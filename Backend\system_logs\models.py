from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class SystemLog(models.Model):
    """
    Model to store system logs for monitoring and debugging
    """
    LOG_LEVELS = [
        ('debug', 'Debug'),
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('critical', 'Critical'),
    ]

    LOG_CATEGORIES = [
        ('authentication', 'Authentication'),
        ('authorization', 'Authorization'),
        ('database', 'Database'),
        ('api', 'API'),
        ('payments', 'Payments'),
        ('orders', 'Orders'),
        ('restaurants', 'Restaurants'),
        ('delivery', 'Delivery'),
        ('system', 'System'),
        ('security', 'Security'),
        ('performance', 'Performance'),
        ('backup', 'Backup'),
        ('email', 'Email'),
        ('websocket', 'WebSocket'),
        ('file_upload', 'File Upload'),
        ('admin', 'Admin'),
        ('user_management', 'User Management'),
        ('other', 'Other'),
    ]

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    timestamp = models.DateTimeField(default=timezone.now, db_index=True)
    level = models.CharField(max_length=20, choices=LOG_LEVELS, db_index=True)
    category = models.CharField(max_length=50, choices=LOG_CATEGORIES, db_index=True)

    # Message content
    message = models.TextField()
    details = models.TextField(blank=True, null=True)

    # Context information
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='system_logs'
    )
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True, null=True)

    # Technical details
    source = models.CharField(max_length=200, blank=True, null=True)  # File:line or module
    request_id = models.CharField(max_length=100, blank=True, null=True)  # For tracing requests
    session_id = models.CharField(max_length=100, blank=True, null=True)

    # Additional metadata
    extra_data = models.JSONField(default=dict, blank=True)

    # Performance tracking
    response_time = models.FloatField(null=True, blank=True)  # In milliseconds

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['timestamp', 'level']),
            models.Index(fields=['category', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['level', 'category']),
        ]
        verbose_name = 'System Log'
        verbose_name_plural = 'System Logs'

    def __str__(self):
        return f"[{self.level.upper()}] {self.category} - {self.message[:50]}..."

    @property
    def formatted_timestamp(self):
        """Return formatted timestamp for display"""
        return self.timestamp.strftime('%Y-%m-%d %H:%M:%S')

    @property
    def user_display(self):
        """Return user display name or 'system' if no user"""
        if self.user:
            return getattr(self.user, 'user_name', str(self.user))
        return 'system'


class LogStatistics(models.Model):
    """
    Model to store aggregated log statistics for performance
    """
    date = models.DateField(unique=True, db_index=True)

    # Counts by level
    debug_count = models.IntegerField(default=0)
    info_count = models.IntegerField(default=0)
    warning_count = models.IntegerField(default=0)
    error_count = models.IntegerField(default=0)
    critical_count = models.IntegerField(default=0)

    # Counts by category
    authentication_count = models.IntegerField(default=0)
    database_count = models.IntegerField(default=0)
    api_count = models.IntegerField(default=0)
    payments_count = models.IntegerField(default=0)
    orders_count = models.IntegerField(default=0)
    system_count = models.IntegerField(default=0)
    other_count = models.IntegerField(default=0)

    # Total counts
    total_logs = models.IntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date']
        verbose_name = 'Log Statistics'
        verbose_name_plural = 'Log Statistics'

    def __str__(self):
        return f"Log Stats for {self.date} - {self.total_logs} logs"

    @property
    def total_count(self):
        """Calculate total count from all levels"""
        return (self.debug_count + self.info_count + self.warning_count +
                self.error_count + self.critical_count)
