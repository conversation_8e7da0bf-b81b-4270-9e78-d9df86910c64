from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # Notification management
    path('', views.NotificationListView.as_view(), name='notification_list'),
    path('<uuid:id>/', views.NotificationDetailView.as_view(), name='notification_detail'),
    path('send/', views.send_notification_view, name='send_notification'),
    path('stats/', views.notification_stats_view, name='notification_stats'),
    path('history/', views.notification_history_view, name='notification_history'),
    path('test/', views.test_notification_view, name='test_notification'),
    path('bulk-action/', views.bulk_notification_action_view, name='bulk_notification_action'),
    
    # Settings
    path('settings/', views.notification_settings_view, name='notification_settings'),
    
    # Templates
    path('templates/', views.NotificationTemplateListView.as_view(), name='template_list'),
    path('templates/<uuid:id>/', views.NotificationTemplateDetailView.as_view(), name='template_detail'),
    path('templates/create/', views.create_template_view, name='create_template'),
    path('templates/<uuid:template_id>/update/', views.update_template_view, name='update_template'),
    path('templates/<uuid:template_id>/delete/', views.delete_template_view, name='delete_template'),

    # Dynamic data endpoints
    path('users/search/', views.search_users_view, name='search_users'),
    path('roles/', views.get_user_roles_view, name='get_user_roles'),
    path('types/', views.get_notification_types_view, name='get_notification_types'),
]
