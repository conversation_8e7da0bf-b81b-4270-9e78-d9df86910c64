import React, { useState, useEffect } from "react";
import {
  FileText,
  Search,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  Info,
  XCircle,
  Eye,
  Trash2,
  Settings,
  Activity,
  Server,
  Database,
  Shield,
  Bug,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";
import { adminApi } from "../../services/adminApi";

const SystemLogs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLevel, setSelectedLevel] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [dateRange, setDateRange] = useState("today");
  const [stats, setStats] = useState({});
  const [error, setError] = useState(null);

  const logLevels = [
    { id: "all", label: "All Levels", count: stats.all || 0, color: "gray" },
    { id: "error", label: "Error", count: stats.error || 0, color: "red" },
    {
      id: "warning",
      label: "Warning",
      count: stats.warning || 0,
      color: "yellow",
    },
    { id: "info", label: "Info", count: stats.info || 0, color: "blue" },
    { id: "debug", label: "Debug", count: stats.debug || 0, color: "purple" },
  ];

  const logCategories = [
    { id: "all", label: "All Categories" },
    { id: "authentication", label: "Authentication" },
    { id: "database", label: "Database" },
    { id: "api", label: "API Requests" },
    { id: "orders", label: "Orders" },
    { id: "payments", label: "Payments" },
    { id: "system", label: "System" },
    { id: "security", label: "Security" },
  ];

  const mockLogs = [
    {
      id: 1,
      timestamp: "2024-01-15 14:32:15",
      level: "error",
      category: "database",
      message: "Connection timeout to database server",
      details: "Failed to connect to PostgreSQL server after 30 seconds",
      user: "system",
      ip: "*************",
      source: "DatabaseManager.js:45",
    },
    {
      id: 2,
      timestamp: "2024-01-15 14:31:42",
      level: "warning",
      category: "authentication",
      message: "Multiple failed login attempts detected",
      details:
        "User '<EMAIL>' failed to login 5 times in 10 minutes",
      user: "<EMAIL>",
      ip: "************",
      source: "AuthController.js:123",
    },
    {
      id: 3,
      timestamp: "2024-01-15 14:30:18",
      level: "info",
      category: "orders",
      message: "New order created successfully",
      details: "Order #ORD-2024-001234 created by customer ID 567",
      user: "customer_567",
      ip: "*************",
      source: "OrderService.js:89",
    },
    {
      id: 4,
      timestamp: "2024-01-15 14:29:55",
      level: "debug",
      category: "api",
      message: "API request processed",
      details: "GET /api/restaurants - Response time: 245ms",
      user: "api_user",
      ip: "************",
      source: "ApiMiddleware.js:67",
    },
    {
      id: 5,
      timestamp: "2024-01-15 14:28:33",
      level: "error",
      category: "payments",
      message: "Payment processing failed",
      details:
        "Stripe payment failed for order #ORD-2024-001233 - Card declined",
      user: "customer_445",
      ip: "************",
      source: "PaymentService.js:156",
    },
  ];

  useEffect(() => {
    loadLogs();
    loadStats();
  }, [selectedLevel, selectedCategory, dateRange]);

  const loadLogs = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await adminApi.getSystemLogs({
        level: selectedLevel,
        category: selectedCategory,
        date_range: dateRange,
        search: searchTerm,
      });

      if (result.success) {
        setLogs(result.data.logs || []);
      } else {
        setError(result.error?.message || "Failed to load logs");
        // Fallback to mock data
        setLogs(mockLogs);
      }
    } catch (error) {
      console.error("Error loading logs:", error);
      setError("Error loading logs");
      // Fallback to mock data
      setLogs(mockLogs);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const result = await adminApi.getLogStats();

      if (result.success) {
        setStats(result.data);
      } else {
        // Fallback to mock stats
        setStats({
          all: 1247,
          error: 23,
          warning: 156,
          info: 892,
          debug: 176,
        });
      }
    } catch (error) {
      console.error("Error loading stats:", error);
      // Fallback to mock stats
      setStats({
        all: 1247,
        error: 23,
        warning: 156,
        info: 892,
        debug: 176,
      });
    }
  };

  const filteredLogs = logs.filter((log) => {
    const matchesSearch =
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = selectedLevel === "all" || log.level === selectedLevel;
    const matchesCategory =
      selectedCategory === "all" || log.category === selectedCategory;
    return matchesSearch && matchesLevel && matchesCategory;
  });

  const getLevelIcon = (level) => {
    switch (level) {
      case "error":
        return <XCircle size={16} className='text-red-600' />;
      case "warning":
        return <AlertCircle size={16} className='text-yellow-600' />;
      case "info":
        return <Info size={16} className='text-blue-600' />;
      case "debug":
        return <Bug size={16} className='text-purple-600' />;
      default:
        return <CheckCircle size={16} className='text-gray-600' />;
    }
  };

  const getLevelBadgeVariant = (level) => {
    switch (level) {
      case "error":
        return "danger";
      case "warning":
        return "warning";
      case "info":
        return "info";
      case "debug":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case "authentication":
        return <Shield size={16} />;
      case "database":
        return <Database size={16} />;
      case "api":
        return <Server size={16} />;
      case "system":
        return <Activity size={16} />;
      default:
        return <FileText size={16} />;
    }
  };

  const exportLogs = async (format = "csv") => {
    try {
      const result = await adminApi.exportLogs(
        {
          level: selectedLevel,
          category: selectedCategory,
          date_range: dateRange,
          search: searchTerm,
        },
        format
      );

      if (result.success) {
        const url = window.URL.createObjectURL(result.data);
        const a = document.createElement("a");
        a.href = url;
        a.download = `system_logs_${
          new Date().toISOString().split("T")[0]
        }.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert("Failed to export logs");
      }
    } catch (error) {
      console.error("Error exporting logs:", error);
      alert("Error exporting logs");
    }
  };

  return (
    <div className='p-6 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>System Logs</h1>
          <p className='text-gray-600 mt-1'>
            Monitor and analyze system activity and errors
          </p>
        </div>
        <div className='flex items-center space-x-3 mt-4 sm:mt-0'>
          <Button
            variant='outline'
            icon={<RefreshCw size={16} />}
            onClick={() => window.location.reload()}
          >
            Refresh
          </Button>
          <Button
            variant='outline'
            icon={<Download size={16} />}
            onClick={exportLogs}
          >
            Export
          </Button>
          <Button variant='outline' icon={<Settings size={16} />}>
            Settings
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4'>
        {logLevels.map((level) => (
          <Card key={level.id}>
            <div className='p-4'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-gray-600'>{level.label}</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {level.count}
                  </p>
                </div>
                <div className={`p-2 bg-${level.color}-100 rounded-lg`}>
                  {getLevelIcon(level.id)}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <div className='p-6'>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='relative'>
              <Search
                size={16}
                className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              />
              <Input
                placeholder='Search logs...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>
            <select
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
              className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            >
              {logLevels.map((level) => (
                <option key={level.id} value={level.id}>
                  {level.label} ({level.count})
                </option>
              ))}
            </select>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            >
              {logCategories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.label}
                </option>
              ))}
            </select>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            >
              <option value='today'>Today</option>
              <option value='yesterday'>Yesterday</option>
              <option value='week'>Last 7 Days</option>
              <option value='month'>Last 30 Days</option>
              <option value='custom'>Custom Range</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Logs Table */}
      <Card>
        <div className='p-6'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold'>
              System Logs ({filteredLogs.length} entries)
            </h3>
            <div className='flex items-center space-x-2'>
              <Button
                size='small'
                variant='outline'
                icon={<Filter size={14} />}
              >
                Advanced Filters
              </Button>
              <Button
                size='small'
                variant='outline'
                icon={<Trash2 size={14} />}
              >
                Clear Old Logs
              </Button>
            </div>
          </div>

          <div className='overflow-x-auto'>
            <table className='w-full'>
              <thead>
                <tr className='border-b border-gray-200'>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Timestamp
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Level
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Category
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Message
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    User
                  </th>
                  <th className='text-left py-3 px-4 font-medium text-gray-700'>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredLogs.map((log) => (
                  <tr
                    key={log.id}
                    className='border-b border-gray-100 hover:bg-gray-50'
                  >
                    <td className='py-3 px-4'>
                      <div className='flex items-center space-x-2'>
                        <Clock size={14} className='text-gray-400' />
                        <span className='text-sm font-mono'>
                          {log.timestamp}
                        </span>
                      </div>
                    </td>
                    <td className='py-3 px-4'>
                      <div className='flex items-center space-x-2'>
                        {getLevelIcon(log.level)}
                        <Badge
                          variant={getLevelBadgeVariant(log.level)}
                          size='small'
                        >
                          {log.level.toUpperCase()}
                        </Badge>
                      </div>
                    </td>
                    <td className='py-3 px-4'>
                      <div className='flex items-center space-x-2'>
                        {getCategoryIcon(log.category)}
                        <span className='text-sm capitalize'>
                          {log.category}
                        </span>
                      </div>
                    </td>
                    <td className='py-3 px-4'>
                      <div>
                        <p className='text-sm font-medium text-gray-900'>
                          {log.message}
                        </p>
                        <p className='text-xs text-gray-500 mt-1 truncate max-w-xs'>
                          {log.details}
                        </p>
                      </div>
                    </td>
                    <td className='py-3 px-4'>
                      <div>
                        <p className='text-sm text-gray-900'>{log.user}</p>
                        <p className='text-xs text-gray-500'>{log.ip}</p>
                      </div>
                    </td>
                    <td className='py-3 px-4'>
                      <Button
                        size='small'
                        variant='outline'
                        icon={<Eye size={14} />}
                        onClick={() =>
                          alert(`Viewing details for log ${log.id}`)
                        }
                      >
                        Details
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredLogs.length === 0 && (
            <div className='text-center py-8'>
              <FileText size={48} className='mx-auto text-gray-300 mb-4' />
              <p className='text-gray-500'>
                No logs found matching your criteria
              </p>
              <p className='text-sm text-gray-400 mt-1'>
                Try adjusting your search terms or filters
              </p>
            </div>
          )}
        </div>
      </Card>

      {/* Log Details Modal would go here */}
    </div>
  );
};

export default SystemLogs;
