class WebSocketService {
  constructor() {
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.listeners = new Map();
    this.isConnected = false;
    this.userId = null;
    this.userRole = null;
    this.shouldReconnect = true;
    console.log("WebSocketService initialized for real-time notifications");
  }

  // Initialize WebSocket connection - ENABLED
  connect(userId, userRole) {
    console.log("WebSocket connecting for user:", userId, "role:", userRole);
    this.userId = userId;
    this.userRole = userRole;

    try {
      // Use WebSocket for real-time communication
      // In production, this would be wss:// and proper WebSocket server
      const wsUrl = `ws://localhost:8000/ws/delivery-agent/${userId}/`;

      this.socket = new WebSocket(wsUrl);

      this.socket.onopen = this.onOpen.bind(this);
      this.socket.onmessage = this.onMessage.bind(this);
      this.socket.onclose = this.onClose.bind(this);
      this.socket.onerror = this.onError.bind(this);
    } catch (error) {
      console.error("WebSocket connection error:", error);
      this.scheduleReconnect();
    }
  }

  // Handle connection open
  onOpen(event) {
    console.log("WebSocket connected");
    this.isConnected = true;
    this.reconnectAttempts = 0;

    // Send authentication message
    this.send({
      type: "authenticate",
      user_id: this.userId,
      user_role: this.userRole,
      timestamp: new Date().toISOString(),
    });

    // Notify listeners
    this.emit("connected", { connected: true });
  }

  // Handle incoming messages
  onMessage(event) {
    try {
      const data = JSON.parse(event.data);
      console.log("WebSocket message received:", data);

      // Handle different message types
      switch (data.type) {
        case "notification_status_update":
          this.emit("notificationStatusUpdate", data.payload);
          break;
        case "notification_sent":
          this.emit("notificationSent", data.payload);
          break;
        case "stats_update":
          this.emit("statsUpdate", data.payload);
          break;
        case "delivery_update":
          this.emit("deliveryUpdate", data.payload);
          break;
        case "system_alert":
          this.emit("systemAlert", data.payload);
          break;
        default:
          // Emit generic event for backward compatibility
          this.emit(data.type, data);
      }
    } catch (error) {
      console.error("Error parsing WebSocket message:", error);
    }
  }

  // Handle connection close
  onClose(event) {
    console.log("WebSocket disconnected:", event.code, event.reason);
    this.isConnected = false;

    // Notify listeners
    this.emit("disconnected", { connected: false });

    // Attempt to reconnect if not intentionally closed
    if (event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  // Handle connection error
  onError(error) {
    console.error("WebSocket error:", error);
    this.emit("error", { error });
  }

  // Schedule reconnection
  scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`
      );

      setTimeout(() => {
        if (!this.isConnected) {
          this.connect(this.userId, this.userRole);
        }
      }, this.reconnectInterval * this.reconnectAttempts);
    } else {
      console.error("Max reconnection attempts reached");
      this.emit("max_reconnect_attempts", { attempts: this.reconnectAttempts });
    }
  }

  // Send message through WebSocket
  send(message) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn("WebSocket not connected, message not sent:", message);
    }
  }

  // Add event listener
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remove event listener
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Emit event to listeners
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error("Error in WebSocket event listener:", error);
        }
      });
    }
  }

  // Disconnect WebSocket
  disconnect() {
    if (this.socket) {
      this.socket.close(1000, "Client disconnect");
      this.socket = null;
    }
    this.isConnected = false;
    this.listeners.clear();
  }

  // Send location update
  sendLocationUpdate(latitude, longitude, address = "", activityType = "idle") {
    this.send({
      type: "location_update",
      data: {
        latitude,
        longitude,
        address,
        activity_type: activityType,
        timestamp: new Date().toISOString(),
      },
    });
  }

  // Send status update
  sendStatusUpdate(availability, isOnline) {
    this.send({
      type: "status_update",
      data: {
        availability,
        is_online: isOnline,
        timestamp: new Date().toISOString(),
      },
    });
  }

  // Send order status update
  sendOrderStatusUpdate(orderId, status, notes = "") {
    this.send({
      type: "order_status_update",
      data: {
        order_id: orderId,
        status,
        notes,
        timestamp: new Date().toISOString(),
      },
    });
  }

  // Request available orders
  requestAvailableOrders() {
    this.send({
      type: "request_available_orders",
      timestamp: new Date().toISOString(),
    });
  }

  // Accept order
  acceptOrder(orderId) {
    this.send({
      type: "accept_order",
      data: {
        order_id: orderId,
        timestamp: new Date().toISOString(),
      },
    });
  }

  // Send heartbeat to keep connection alive
  sendHeartbeat() {
    this.send({
      type: "heartbeat",
      timestamp: new Date().toISOString(),
    });
  }

  // Start heartbeat interval
  startHeartbeat(interval = 30000) {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.sendHeartbeat();
      }
    }, interval);
  }

  // Stop heartbeat interval
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Get connection status
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      userId: this.userId,
      userRole: this.userRole,
    };
  }

  // Notification-specific methods
  subscribeToNotifications() {
    this.send({
      type: "subscribe_notifications",
      timestamp: new Date().toISOString(),
    });
  }

  unsubscribeFromNotifications() {
    this.send({
      type: "unsubscribe_notifications",
      timestamp: new Date().toISOString(),
    });
  }

  subscribeToNotificationStats() {
    this.send({
      type: "subscribe_notification_stats",
      timestamp: new Date().toISOString(),
    });
  }

  unsubscribeFromNotificationStats() {
    this.send({
      type: "unsubscribe_notification_stats",
      timestamp: new Date().toISOString(),
    });
  }

  subscribeToSpecificNotification(notificationId) {
    this.send({
      type: "subscribe_notification",
      notification_id: notificationId,
      timestamp: new Date().toISOString(),
    });
  }

  unsubscribeFromSpecificNotification(notificationId) {
    this.send({
      type: "unsubscribe_notification",
      notification_id: notificationId,
      timestamp: new Date().toISOString(),
    });
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;

// Export specific methods for easier use
export const {
  connect,
  disconnect,
  on,
  off,
  send,
  sendLocationUpdate,
  sendStatusUpdate,
  sendOrderStatusUpdate,
  requestAvailableOrders,
  acceptOrder,
  getConnectionStatus,
} = websocketService;
