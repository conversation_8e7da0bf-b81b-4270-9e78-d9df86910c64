import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from django.template.loader import render_to_string

from .models import (
    Notification, NotificationTemplate, NotificationDelivery,
    NotificationSettings, NotificationStatistics
)

User = get_user_model()
logger = logging.getLogger(__name__)


class NotificationService:
    """
    Service for managing and sending notifications
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.settings = NotificationSettings.get_settings()
    
    def send_notification(self, created_by: User, title: str, message: str,
                         notification_type: str, recipient_type: str,
                         channels: List[str] = None, **kwargs) -> Notification:
        """
        Create and send a notification
        
        Args:
            created_by: User creating the notification
            title: Notification title
            message: Notification message
            notification_type: Type of notification
            recipient_type: Type of recipients
            channels: List of delivery channels
            **kwargs: Additional notification parameters
        
        Returns:
            Notification: Created notification instance
        """
        try:
            with transaction.atomic():
                # Create notification
                notification = Notification.objects.create(
                    title=title,
                    message=message,
                    notification_type=notification_type,
                    recipient_type=recipient_type,
                    channels=channels or ['email'],
                    created_by=created_by,
                    status='draft',
                    **{k: v for k, v in kwargs.items() if k in [
                        'recipient_roles', 'recipient_emails', 'template',
                        'scheduled_at', 'context_data', 'priority'
                    ]}
                )
                
                # Add recipient users if specified
                if kwargs.get('recipient_users'):
                    users = User.objects.filter(id__in=kwargs['recipient_users'])
                    notification.recipient_users.set(users)
                
                # Process notification immediately if not scheduled
                if not notification.scheduled_at or notification.scheduled_at <= timezone.now():
                    self.process_notification(notification)
                else:
                    notification.status = 'scheduled'
                    notification.save()
                
                return notification
                
        except Exception as e:
            self.logger.error(f"Failed to send notification: {e}")
            raise
    
    def process_notification(self, notification: Notification) -> bool:
        """
        Process and send a notification to all recipients
        
        Args:
            notification: Notification to process
            
        Returns:
            bool: True if processing was successful
        """
        try:
            # Update status to sending
            notification.status = 'sending'
            notification.save()
            
            # Get recipients
            recipients = self._get_recipients(notification)
            notification.total_recipients = len(recipients)
            notification.save()
            
            if not recipients:
                notification.status = 'failed'
                notification.save()
                self.logger.warning(f"No recipients found for notification {notification.id}")
                return False
            
            # Send to each recipient via each channel
            successful_deliveries = 0
            failed_deliveries = 0
            
            for recipient in recipients:
                for channel in notification.channels:
                    try:
                        delivery = self._create_delivery(notification, recipient, channel)
                        if self._send_delivery(delivery):
                            successful_deliveries += 1
                        else:
                            failed_deliveries += 1
                    except Exception as e:
                        self.logger.error(f"Failed to send to {recipient['email']} via {channel}: {e}")
                        failed_deliveries += 1
            
            # Update notification status
            notification.successful_deliveries = successful_deliveries
            notification.failed_deliveries = failed_deliveries
            notification.sent_at = timezone.now()
            
            if successful_deliveries > 0:
                notification.status = 'sent'
            else:
                notification.status = 'failed'
            
            notification.save()
            
            # Update daily statistics
            self._update_statistics(notification)
            
            return True
            
        except Exception as e:
            notification.status = 'failed'
            notification.save()
            self.logger.error(f"Failed to process notification {notification.id}: {e}")
            return False
    
    def _get_recipients(self, notification: Notification) -> List[Dict[str, Any]]:
        """
        Get list of recipients for a notification
        
        Args:
            notification: Notification instance
            
        Returns:
            List of recipient dictionaries with email, phone, user info
        """
        recipients = []
        
        if notification.recipient_type == 'all':
            # All users
            users = User.objects.filter(is_verified=True)
            for user in users:
                recipients.append({
                    'user': user,
                    'email': user.email,
                    'phone': getattr(user, 'phone', None),
                    'name': user.name
                })
                
        elif notification.recipient_type == 'role':
            # Users with specific roles
            if notification.recipient_roles:
                users = User.objects.filter(
                    role__in=notification.recipient_roles,
                    is_verified=True
                )
                for user in users:
                    recipients.append({
                        'user': user,
                        'email': user.email,
                        'phone': getattr(user, 'phone', None),
                        'name': user.name
                    })
                    
        elif notification.recipient_type == 'user':
            # Specific users
            users = notification.recipient_users.filter(is_verified=True)
            for user in users:
                recipients.append({
                    'user': user,
                    'email': user.email,
                    'phone': getattr(user, 'phone', None),
                    'name': user.name
                })
                
        elif notification.recipient_type == 'custom':
            # Custom email list
            if notification.recipient_emails:
                for email in notification.recipient_emails:
                    recipients.append({
                        'user': None,
                        'email': email,
                        'phone': None,
                        'name': email.split('@')[0]
                    })
        
        return recipients
    
    def _create_delivery(self, notification: Notification, recipient: Dict[str, Any], 
                        channel: str) -> NotificationDelivery:
        """
        Create a delivery record for tracking
        
        Args:
            notification: Notification instance
            recipient: Recipient information
            channel: Delivery channel
            
        Returns:
            NotificationDelivery: Created delivery instance
        """
        return NotificationDelivery.objects.create(
            notification=notification,
            recipient_user=recipient.get('user'),
            recipient_email=recipient['email'],
            recipient_phone=recipient.get('phone'),
            channel=channel,
            status='pending'
        )
    
    def _send_delivery(self, delivery: NotificationDelivery) -> bool:
        """
        Send a single delivery via the specified channel
        
        Args:
            delivery: NotificationDelivery instance
            
        Returns:
            bool: True if delivery was successful
        """
        try:
            if delivery.channel == 'email':
                return self._send_email(delivery)
            elif delivery.channel == 'sms':
                return self._send_sms(delivery)
            elif delivery.channel == 'push':
                return self._send_push(delivery)
            elif delivery.channel == 'in_app':
                return self._send_in_app(delivery)
            else:
                self.logger.warning(f"Unknown channel: {delivery.channel}")
                return False
                
        except Exception as e:
            delivery.status = 'failed'
            delivery.error_message = str(e)
            delivery.save()
            self.logger.error(f"Failed to send delivery {delivery.id}: {e}")
            return False
    
    def _send_email(self, delivery: NotificationDelivery) -> bool:
        """Send email notification"""
        try:
            if not self.settings.email_enabled:
                self.logger.info("Email notifications are disabled")
                delivery.status = 'failed'
                delivery.error_message = 'Email notifications are disabled'
                delivery.save()
                return False

            notification = delivery.notification

            # Prepare email content
            subject = notification.title
            message = notification.message

            # Use template if available
            if notification.template and notification.template.supports_email:
                context = notification.context_data or {}
                context.update({
                    'recipient_name': delivery.recipient_user.name if delivery.recipient_user else 'User',
                    'notification_title': notification.title,
                    'notification_message': notification.message,
                })

                subject = notification.template.render_title(context)
                message = notification.template.render_message(context)

            # For development, just simulate email sending
            # TODO: Uncomment this for production email sending
            # send_mail(
            #     subject=subject,
            #     message=message,
            #     from_email=f"{self.settings.email_from_name} <{self.settings.email_from_address}>",
            #     recipient_list=[delivery.recipient_email],
            #     fail_silently=False,
            # )

            # Simulate successful email sending
            self.logger.info(f"Email simulated for {delivery.recipient_email}: {subject}")

            # Update delivery status
            delivery.status = 'sent'
            delivery.sent_at = timezone.now()
            delivery.save()

            return True

        except Exception as e:
            delivery.status = 'failed'
            delivery.error_message = str(e)
            delivery.save()
            self.logger.error(f"Failed to send email to {delivery.recipient_email}: {e}")
            return False
    
    def _send_sms(self, delivery: NotificationDelivery) -> bool:
        """Send SMS notification (placeholder)"""
        # TODO: Implement SMS sending with Twilio or other provider
        self.logger.info(f"SMS sending not implemented yet for {delivery.recipient_phone}")
        delivery.status = 'failed'
        delivery.error_message = 'SMS sending not implemented'
        delivery.save()
        return False
    
    def _send_push(self, delivery: NotificationDelivery) -> bool:
        """Send push notification (placeholder)"""
        # TODO: Implement push notifications with Firebase or other provider
        self.logger.info(f"Push notification sending not implemented yet")
        delivery.status = 'failed'
        delivery.error_message = 'Push notifications not implemented'
        delivery.save()
        return False
    
    def _send_in_app(self, delivery: NotificationDelivery) -> bool:
        """Send in-app notification (placeholder)"""
        # TODO: Implement in-app notifications with WebSocket or database storage
        self.logger.info(f"In-app notification sending not implemented yet")
        delivery.status = 'sent'
        delivery.sent_at = timezone.now()
        delivery.save()
        return True
    
    def _update_statistics(self, notification: Notification):
        """Update daily notification statistics"""
        try:
            today = timezone.now().date()
            stats, created = NotificationStatistics.objects.get_or_create(
                date=today,
                defaults={}
            )
            
            # Update type counts
            type_field = f"{notification.notification_type}_notifications"
            if hasattr(stats, type_field):
                setattr(stats, type_field, getattr(stats, type_field) + 1)
            
            # Update channel counts
            for channel in notification.channels:
                sent_field = f"{channel}_sent"
                delivered_field = f"{channel}_delivered"
                failed_field = f"{channel}_failed"
                
                if hasattr(stats, sent_field):
                    setattr(stats, sent_field, getattr(stats, sent_field) + notification.total_recipients)
                    setattr(stats, delivered_field, getattr(stats, delivered_field) + notification.successful_deliveries)
                    setattr(stats, failed_field, getattr(stats, failed_field) + notification.failed_deliveries)
            
            # Update totals
            stats.total_sent += notification.total_recipients
            stats.total_delivered += notification.successful_deliveries
            stats.total_failed += notification.failed_deliveries
            
            stats.save()
            
        except Exception as e:
            self.logger.error(f"Failed to update statistics: {e}")


class NotificationTemplateService:
    """
    Service for managing notification templates
    """
    
    @staticmethod
    def create_default_templates():
        """Create default notification templates"""
        # Get admin user
        admin_user = User.objects.filter(role='admin').first()
        if not admin_user:
            logger.warning("No admin user found for creating templates")
            return

        templates = [
            {
                'name': 'Order Confirmation',
                'template_type': 'order',
                'title_template': 'Order Confirmed - #{order_id}',
                'message_template': 'Your order #{order_id} has been confirmed and is being prepared.',
                'email_subject_template': 'Order Confirmation - Afghan Sofra',
            },
            {
                'name': 'Welcome Message',
                'template_type': 'user',
                'title_template': 'Welcome to Afghan Sofra!',
                'message_template': 'Welcome {recipient_name}! Thank you for joining Afghan Sofra.',
                'email_subject_template': 'Welcome to Afghan Sofra',
            },
            {
                'name': 'Restaurant Approved',
                'template_type': 'restaurant',
                'title_template': 'Restaurant Application Approved',
                'message_template': 'Congratulations! Your restaurant application has been approved.',
                'email_subject_template': 'Restaurant Application Approved - Afghan Sofra',
            },
        ]

        for template_data in templates:
            NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    **template_data,
                    'created_by': admin_user,
                }
            )
