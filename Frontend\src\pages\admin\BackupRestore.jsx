import React, { useState, useEffect } from "react";
import {
  Database,
  Download,
  Upload,
  RefreshCw,
  Clock,
  CheckCircle,
  AlertCircle,
  HardDrive,
  Cloud,
  Shield,
  Calendar,
  FileText,
  Settings,
  Play,
  Pause,
  Trash2,
  Eye,
  Archive,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { adminApi } from "../../services/adminApi";

const BackupRestore = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [backups, setBackups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({});

  // Ensure backups is always an array
  const safeBackups = Array.isArray(backups) ? backups : [];

  const tabs = [
    { id: "overview", label: "Overview", icon: <Database size={16} /> },
    { id: "create", label: "Create Backup", icon: <Archive size={16} /> },
    { id: "restore", label: "Restore", icon: <RefreshCw size={16} /> },
    { id: "schedule", label: "Schedule", icon: <Calendar size={16} /> },
    { id: "settings", label: "Settings", icon: <Settings size={16} /> },
  ];

  const mockBackups = [
    {
      id: 1,
      name: "Full System Backup",
      type: "full",
      size: "2.4 GB",
      date: "2024-01-15 02:00:00",
      status: "completed",
      location: "cloud",
      retention: "30 days",
    },
    {
      id: 2,
      name: "Database Backup",
      type: "database",
      size: "156 MB",
      date: "2024-01-14 02:00:00",
      status: "completed",
      location: "local",
      retention: "7 days",
    },
    {
      id: 3,
      name: "User Data Backup",
      type: "partial",
      size: "89 MB",
      date: "2024-01-13 02:00:00",
      status: "completed",
      location: "cloud",
      retention: "14 days",
    },
    {
      id: 4,
      name: "Emergency Backup",
      type: "full",
      size: "2.1 GB",
      date: "2024-01-12 15:30:00",
      status: "failed",
      location: "local",
      retention: "30 days",
    },
  ];

  useEffect(() => {
    // Debug authentication state
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
    const token = user.access_token || localStorage.getItem("access_token");
    console.log("🔍 Component mounted - Auth state:", {
      hasUser: !!user.access_token,
      hasToken: !!token,
      userRole: user.role,
      userName: user.name,
      fullUser: user,
    });

    // Force load with delay to ensure component is ready
    setTimeout(() => {
      console.log("🔍 Starting delayed load...");
      loadBackups();
      loadStats();
    }, 100);
  }, []);

  const loadBackups = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log("🔍 Starting loadBackups...");
      const result = await adminApi.getBackups();
      console.log("🔍 Raw API response:", JSON.stringify(result, null, 2));

      if (result.success) {
        // Handle the nested API response structure
        // API returns: { success: true, data: { success: true, data: { results: [...] } } }
        const apiData = result.data?.data || result.data || {};
        const backupsData = apiData.results || apiData || [];

        console.log("🔍 API data structure:", result.data);
        console.log("🔍 Extracted API data:", apiData);
        console.log("🔍 Extracted backups data:", backupsData);
        console.log("🔍 Backups data type:", typeof backupsData);
        console.log("🔍 Is backups data array?", Array.isArray(backupsData));
        console.log("🔍 Backups data length:", backupsData.length);

        // Ensure backupsData is always an array
        const backupsArray = Array.isArray(backupsData) ? backupsData : [];
        console.log("🔍 Final backups array:", backupsArray);
        console.log("🔍 Final backups array length:", backupsArray.length);

        setBackups(backupsArray);

        // Force a re-render check
        setTimeout(() => {
          console.log("🔍 Backups state after setState:", backups.length);
          // Force component update
          setLoading(false);
          setLoading(true);
          setLoading(false);
        }, 100);
      } else {
        console.error("❌ API returned error:", result.error);
        setError(result.error?.message || "Failed to load backups");
        // Don't fallback to mock data, show the actual error
        setBackups([]);
      }
    } catch (error) {
      console.error("❌ Error loading backups:", error);
      console.error("❌ Error details:", error.response?.data);
      setError("Error loading backups: " + error.message);
      // Don't fallback to mock data, show the actual error
      setBackups([]);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      console.log("🔍 Loading stats...");
      const result = await adminApi.getBackupStats();
      console.log("🔍 Stats API result:", result);

      if (result.success) {
        // Handle nested response structure for stats too
        const apiStatsData = result.data?.data || result.data || {};
        const statsData = {
          total_backups: apiStatsData.total_backups || 0,
          successful: apiStatsData.successful_backups || 0,
          storage_used: apiStatsData.total_storage_used || "0 B",
          last_backup: apiStatsData.last_backup_date
            ? new Date(apiStatsData.last_backup_date).toLocaleString()
            : "Never",
        };
        console.log("🔍 API stats data:", apiStatsData);
        console.log("🔍 Setting stats:", statsData);
        setStats(statsData);

        // Force a re-render
        setTimeout(() => {
          console.log("🔍 Stats state after update:", stats);
        }, 100);
      } else {
        console.error("❌ Stats API failed:", result.error);
        // Fallback to default stats
        setStats({
          total_backups: 0,
          successful: 0,
          storage_used: "0 B",
          last_backup: "Never",
        });
      }
    } catch (error) {
      console.error("❌ Error loading stats:", error);
      // Fallback to default stats
      setStats({
        total_backups: 0,
        successful: 0,
        storage_used: "0 B",
        last_backup: "Never",
      });
    }
  };

  const createBackup = async (type) => {
    // Check if user is authenticated
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
    const token = user.access_token || localStorage.getItem("access_token");

    if (!token) {
      alert("❌ Please log in to create backups");
      return;
    }

    setIsBackingUp(true);
    setBackupProgress(0);
    setError(null);

    try {
      const result = await adminApi.createBackup(type);
      console.log("🔍 Backup creation result:", result);

      if (result.success) {
        const backupId = result.data?.data?.id || result.data?.id;
        console.log("🔍 Starting backup polling for ID:", backupId);

        if (!backupId) {
          console.error("❌ No backup ID found in response:", result.data);
          setIsBackingUp(false);
          setError("Failed to get backup ID");
          return;
        }

        // Poll for backup progress
        const pollProgress = setInterval(async () => {
          try {
            const statusResult = await adminApi.getBackupStatus(backupId);
            console.log("🔍 Backup status poll result:", statusResult);

            if (statusResult.success) {
              const statusData = statusResult.data?.data || statusResult.data;
              const progress = statusData?.progress || 0;
              const status = statusData?.status || "pending";

              console.log(
                `📊 Backup progress: ${progress}%, Status: ${status}`
              );
              setBackupProgress(progress);

              if (progress >= 100 || status === "completed") {
                clearInterval(pollProgress);
                setIsBackingUp(false);
                setBackupProgress(100);
                loadBackups(); // Refresh backup list
                loadStats(); // Refresh stats

                // Show success message
                setTimeout(() => {
                  alert("✅ Backup completed successfully!");
                }, 500);
              } else if (status === "failed") {
                clearInterval(pollProgress);
                setIsBackingUp(false);
                const errorMsg = statusData?.error_message || "Backup failed";
                setError(errorMsg);
                alert(`❌ Backup failed: ${errorMsg}`);
              }
            } else {
              console.error("❌ Status poll failed:", statusResult.error);
            }
          } catch (error) {
            console.error("❌ Error polling backup status:", error);
          }
        }, 1000);

        // Fallback timeout
        setTimeout(() => {
          clearInterval(pollProgress);
          if (isBackingUp) {
            setIsBackingUp(false);
            setBackupProgress(100);
            loadBackups();
          }
        }, 30000);
      } else {
        const errorMsg =
          result.error?.message || result.error || "Failed to create backup";
        setError(errorMsg);
        setIsBackingUp(false);
        alert(`❌ Failed to create backup: ${errorMsg}`);
      }
    } catch (error) {
      console.error("❌ Error creating backup:", error);
      const errorMsg =
        error.response?.data?.message ||
        error.message ||
        "Network error creating backup";
      setError(errorMsg);
      setIsBackingUp(false);
      alert(`❌ Error creating backup: ${errorMsg}`);
    }
  };

  const renderOverview = () => (
    <div className='space-y-6'>
      {/* Stats Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card>
          <div className='p-6'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-blue-100 rounded-lg'>
                <Database size={20} className='text-blue-600' />
              </div>
              <div>
                <p className='text-sm text-gray-600'>Total Backups</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.total_backups || 0}
                </p>
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <div className='p-6'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-green-100 rounded-lg'>
                <CheckCircle size={20} className='text-green-600' />
              </div>
              <div>
                <p className='text-sm text-gray-600'>Successful</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.successful || 0}
                </p>
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <div className='p-6'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-yellow-100 rounded-lg'>
                <HardDrive size={20} className='text-yellow-600' />
              </div>
              <div>
                <p className='text-sm text-gray-600'>Storage Used</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.storage_used || "0 GB"}
                </p>
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <div className='p-6'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-purple-100 rounded-lg'>
                <Clock size={20} className='text-purple-600' />
              </div>
              <div>
                <p className='text-sm text-gray-600'>Last Backup</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.last_backup || "Never"}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Backups */}
      <Card>
        <div className='p-6'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold'>Recent Backups</h3>
            <Button
              variant='primary'
              icon={<Archive size={16} />}
              onClick={() => setActiveTab("create")}
            >
              Create Backup
            </Button>
          </div>
          <div className='space-y-3'>
            {loading ? (
              <div className='text-center py-8'>
                <div className='mx-auto mb-4 border-primary-500 border-t-4 border-b-4 rounded-full w-8 h-8 animate-spin'></div>
                <p className='text-gray-500'>Loading backups...</p>
              </div>
            ) : safeBackups.length > 0 ? (
              safeBackups.map((backup) => (
                <div
                  key={backup.id}
                  className='flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50'
                >
                  <div className='flex items-center space-x-4'>
                    <div className='p-2 bg-primary-100 rounded-lg'>
                      {backup.storage_location === "cloud" ? (
                        <Cloud size={16} className='text-primary-600' />
                      ) : (
                        <HardDrive size={16} className='text-primary-600' />
                      )}
                    </div>
                    <div>
                      <h4 className='font-medium text-gray-900'>
                        {backup.name}
                      </h4>
                      <div className='flex items-center space-x-4 text-sm text-gray-600'>
                        <span>
                          {backup.file_size_formatted || backup.size || "0 B"}
                        </span>
                        <span>
                          {backup.created_at
                            ? new Date(backup.created_at).toLocaleString()
                            : backup.date}
                        </span>
                        <span>
                          Retention:{" "}
                          {backup.retention_days
                            ? `${backup.retention_days} days`
                            : backup.retention}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Badge
                      variant={
                        backup.status === "completed"
                          ? "success"
                          : backup.status === "failed"
                          ? "danger"
                          : "warning"
                      }
                      size='small'
                    >
                      {backup.status}
                    </Badge>
                    {backup.status === "completed" && (
                      <Button
                        size='small'
                        variant='outline'
                        icon={<Download size={14} />}
                        onClick={async () => {
                          try {
                            const result = await adminApi.downloadBackup(
                              backup.id
                            );
                            if (result.success) {
                              const url = window.URL.createObjectURL(
                                result.data
                              );
                              const a = document.createElement("a");
                              a.href = url;
                              a.download = `${backup.name || "backup"}_${
                                backup.created_at
                                  ? new Date(backup.created_at)
                                      .toISOString()
                                      .split("T")[0]
                                  : backup.date
                              }`;
                              document.body.appendChild(a);
                              a.click();
                              window.URL.revokeObjectURL(url);
                              document.body.removeChild(a);
                            } else {
                              alert("Failed to download backup");
                            }
                          } catch (error) {
                            console.error("Error downloading backup:", error);
                            alert("Error downloading backup");
                          }
                        }}
                      >
                        Download
                      </Button>
                    )}
                    <Button
                      size='small'
                      variant='outline'
                      icon={<Eye size={14} />}
                    >
                      Details
                    </Button>
                    <Button
                      size='small'
                      variant='outline'
                      onClick={async () => {
                        if (
                          confirm(
                            "Are you sure you want to delete this backup?"
                          )
                        ) {
                          try {
                            const result = await adminApi.deleteBackup(
                              backup.id
                            );
                            if (result.success) {
                              loadBackups(); // Refresh the list
                            } else {
                              alert("Failed to delete backup");
                            }
                          } catch (error) {
                            console.error("Error deleting backup:", error);
                            alert("Error deleting backup");
                          }
                        }
                      }}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className='text-center py-8'>
                <p className='text-gray-500'>
                  No backups found. Create your first backup to get started.
                </p>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );

  const renderCreateBackup = () => (
    <div className='space-y-6'>
      <Card>
        <div className='p-6'>
          <h3 className='text-lg font-semibold mb-6'>Create New Backup</h3>

          {isBackingUp && (
            <div className='mb-6 p-4 bg-blue-50 rounded-lg'>
              <div className='flex items-center justify-between mb-2'>
                <span className='text-sm font-medium text-blue-900'>
                  Creating backup...
                </span>
                <span className='text-sm text-blue-700'>{backupProgress}%</span>
              </div>
              <div className='w-full bg-blue-200 rounded-full h-2'>
                <div
                  className='bg-blue-600 h-2 rounded-full transition-all duration-300'
                  style={{ width: `${backupProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            <Card>
              <div className='p-6 text-center'>
                <Database size={48} className='mx-auto text-blue-600 mb-4' />
                <h4 className='font-semibold mb-2'>Full System Backup</h4>
                <p className='text-sm text-gray-600 mb-4'>
                  Complete backup including database, files, and configurations
                </p>
                <Button
                  variant='primary'
                  onClick={() => createBackup("full")}
                  disabled={isBackingUp}
                  className='w-full'
                >
                  Create Full Backup
                </Button>
              </div>
            </Card>

            <Card>
              <div className='p-6 text-center'>
                <HardDrive size={48} className='mx-auto text-green-600 mb-4' />
                <h4 className='font-semibold mb-2'>Database Only</h4>
                <p className='text-sm text-gray-600 mb-4'>
                  Backup only the database with all user data and settings
                </p>
                <Button
                  variant='outline'
                  onClick={() => createBackup("database")}
                  disabled={isBackingUp}
                  className='w-full'
                >
                  Backup Database
                </Button>
              </div>
            </Card>

            <Card>
              <div className='p-6 text-center'>
                <FileText size={48} className='mx-auto text-purple-600 mb-4' />
                <h4 className='font-semibold mb-2'>Custom Backup</h4>
                <p className='text-sm text-gray-600 mb-4'>
                  Select specific components to include in the backup
                </p>
                <Button
                  variant='outline'
                  onClick={() => createBackup("custom")}
                  disabled={isBackingUp}
                  className='w-full'
                >
                  Custom Backup
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </Card>

      {/* Backup Options */}
      <Card>
        <div className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Backup Options</h3>
          <div className='space-y-4'>
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Storage Location
              </label>
              <select className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'>
                <option>Local Storage</option>
                <option>Cloud Storage (AWS S3)</option>
                <option>External Drive</option>
              </select>
            </div>
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Compression Level
              </label>
              <select className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'>
                <option>None (Fastest)</option>
                <option>Low</option>
                <option>Medium (Recommended)</option>
                <option>High (Smallest Size)</option>
              </select>
            </div>
            <div className='flex items-center'>
              <input type='checkbox' className='mr-2' defaultChecked />
              <label className='text-sm text-gray-700'>
                Encrypt backup with password
              </label>
            </div>
            <div className='flex items-center'>
              <input type='checkbox' className='mr-2' />
              <label className='text-sm text-gray-700'>
                Send notification when backup completes
              </label>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderRestore = () => (
    <Card>
      <div className='p-6'>
        <h3 className='text-lg font-semibold mb-6'>Restore from Backup</h3>
        <div className='space-y-6'>
          <div className='p-4 bg-yellow-50 border border-yellow-200 rounded-lg'>
            <div className='flex items-center'>
              <AlertCircle size={20} className='text-yellow-600 mr-3' />
              <div>
                <h4 className='font-medium text-yellow-800'>Warning</h4>
                <p className='text-sm text-yellow-700'>
                  Restoring from a backup will overwrite current data. Make sure
                  to create a backup of the current state before proceeding.
                </p>
              </div>
            </div>
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Select Backup to Restore
            </label>
            <select className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'>
              <option>Select a backup...</option>
              {backups
                .filter((backup) => backup.status === "completed")
                .map((backup) => (
                  <option key={backup.id} value={backup.id}>
                    {backup.name} - {backup.date} ({backup.size})
                  </option>
                ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Restore Options
            </label>
            <div className='space-y-2'>
              <label className='flex items-center'>
                <input type='checkbox' className='mr-2' defaultChecked />
                Database
              </label>
              <label className='flex items-center'>
                <input type='checkbox' className='mr-2' defaultChecked />
                User Files
              </label>
              <label className='flex items-center'>
                <input type='checkbox' className='mr-2' defaultChecked />
                System Configuration
              </label>
              <label className='flex items-center'>
                <input type='checkbox' className='mr-2' />
                Application Settings
              </label>
            </div>
          </div>

          <div className='flex items-center space-x-4'>
            <Button variant='primary' icon={<RefreshCw size={16} />}>
              Start Restore
            </Button>
            <Button variant='outline'>Validate Backup</Button>
          </div>
        </div>
      </div>
    </Card>
  );

  const renderSchedule = () => (
    <Card>
      <div className='p-6'>
        <h3 className='text-lg font-semibold mb-6'>Backup Schedule</h3>
        <div className='space-y-6'>
          <div className='flex items-center justify-between p-4 border border-gray-200 rounded-lg'>
            <div>
              <h4 className='font-medium'>Daily Database Backup</h4>
              <p className='text-sm text-gray-600'>Every day at 2:00 AM</p>
            </div>
            <div className='flex items-center space-x-2'>
              <Badge variant='success' size='small'>
                Active
              </Badge>
              <Button size='small' variant='outline'>
                Edit
              </Button>
            </div>
          </div>

          <div className='flex items-center justify-between p-4 border border-gray-200 rounded-lg'>
            <div>
              <h4 className='font-medium'>Weekly Full Backup</h4>
              <p className='text-sm text-gray-600'>Every Sunday at 1:00 AM</p>
            </div>
            <div className='flex items-center space-x-2'>
              <Badge variant='success' size='small'>
                Active
              </Badge>
              <Button size='small' variant='outline'>
                Edit
              </Button>
            </div>
          </div>

          <Button variant='primary' icon={<Calendar size={16} />}>
            Add New Schedule
          </Button>
        </div>
      </div>
    </Card>
  );

  return (
    <div className='p-6 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Backup & Restore</h1>
          <p className='text-gray-600 mt-1'>
            Manage system backups and data recovery
          </p>
        </div>
        <div className='flex items-center space-x-3 mt-4 sm:mt-0'>
          <Button
            variant='outline'
            icon={<RefreshCw size={16} />}
            onClick={async () => {
              console.log("🔍 Manual refresh triggered");
              setLoading(true);
              setError(null);

              try {
                await loadBackups();
                await loadStats();

                // Force component re-render
                setTimeout(() => {
                  setLoading(false);
                  console.log("🔍 Manual refresh completed");
                }, 500);
              } catch (error) {
                console.error("❌ Manual refresh error:", error);
                setLoading(false);
              }
            }}
            disabled={loading}
          >
            Force Refresh
          </Button>
          <Button
            variant='primary'
            icon={<Archive size={16} />}
            onClick={() => createBackup("database")}
            disabled={isBackingUp}
          >
            Quick Backup
          </Button>
          <Button
            variant='outline'
            onClick={() => {
              // Set a valid admin token for testing (fresh token)
              const adminToken =
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzU0MTM4NzU4LCJpYXQiOjE3NTQxMzUxNTgsImp0aSI6IjgzNTExNzUzMGNhNjQ2ODFiZWI0MDI3YTA4Y2MxNDdhIiwidXNlcl9pZCI6MTI1fQ.SybvGp1JSQpx7pidinpKpXKV396X96Wtj4nA9ZS0V94";

              localStorage.setItem(
                "afghanSofraUser",
                JSON.stringify({
                  access_token: adminToken,
                  role: "admin",
                  name: "Frontend Admin",
                  username: "frontend_admin",
                })
              );

              alert("✅ Admin token set! Refresh the page to see backups.");
            }}
          >
            Set Admin Token
          </Button>
          <Button
            variant='outline'
            onClick={async () => {
              const user = JSON.parse(
                localStorage.getItem("afghanSofraUser") || "{}"
              );
              const token =
                user.access_token || localStorage.getItem("access_token");
              console.log("🔍 Auth check:", {
                user: !!user.access_token,
                token: !!token,
                userRole: user.role,
                userName: user.name,
              });

              try {
                // Test backup list API
                const backupList = await adminApi.getBackups();
                console.log("🔍 Backup list test:", backupList);

                const stats = await adminApi.getBackupStats();
                console.log("🔍 Stats test:", stats);

                // Handle nested API response structure
                const apiData = backupList.data?.data || backupList.data || {};
                const backupResults = apiData.results || [];
                const backupCount = backupList.success
                  ? backupResults.length
                  : 0;

                // Force update the state with API data
                if (backupList.success && backupResults.length > 0) {
                  console.log("🔍 Forcing state update with API data...");
                  console.log("🔍 Backup results:", backupResults);
                  setBackups(backupResults);
                }

                alert(
                  `🔍 API Test Results:\n` +
                    `Auth: ${token ? "✅" : "❌"}\n` +
                    `Backup List: ${backupList.success ? "✅" : "❌"}\n` +
                    `Stats: ${stats.success ? "✅" : "❌"}\n` +
                    `Backups Found: ${backupCount}\n` +
                    `State Updated: ${backupList.success ? "✅" : "❌"}`
                );
              } catch (error) {
                console.error("🔍 API test error:", error);
                alert(`❌ API Test Failed: ${error.message}`);
              }
            }}
          >
            Debug & Fix
          </Button>
        </div>
      </div>

      {/* Debug Info */}
      <div className='mb-4 p-3 bg-gray-50 border rounded-lg text-sm'>
        <strong>Debug Info:</strong>
        <span className='ml-2'>Backups in state: {safeBackups.length}</span>
        <span className='ml-4'>Loading: {loading ? "Yes" : "No"}</span>
        <span className='ml-4'>Error: {error ? "Yes" : "No"}</span>
      </div>

      {/* Error Display */}
      {error && (
        <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
          <div className='flex items-center'>
            <AlertCircle size={20} className='text-red-600 mr-3' />
            <div>
              <h4 className='font-medium text-red-800'>Error</h4>
              <p className='text-sm text-red-700'>{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className='border-b border-gray-200'>
        <nav className='-mb-px flex space-x-8'>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? "border-primary-500 text-primary-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "overview" && renderOverview()}
      {activeTab === "create" && renderCreateBackup()}
      {activeTab === "restore" && renderRestore()}
      {activeTab === "schedule" && renderSchedule()}
      {activeTab === "settings" && (
        <Card>
          <div className='p-6'>
            <h3 className='text-lg font-semibold mb-4'>Backup Settings</h3>
            <div className='space-y-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Default Storage Location
                </label>
                <select className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'>
                  <option>Local Storage</option>
                  <option>Cloud Storage</option>
                </select>
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Retention Period (days)
                </label>
                <input
                  type='number'
                  defaultValue='30'
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                />
              </div>
              <div className='flex items-center'>
                <input type='checkbox' className='mr-2' defaultChecked />
                <label className='text-sm text-gray-700'>
                  Auto-delete old backups
                </label>
              </div>
              <Button variant='primary'>Save Settings</Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default BackupRestore;
