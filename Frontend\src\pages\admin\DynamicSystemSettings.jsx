import React, { useState, useEffect } from "react";
import {
  Settings,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Edit3,
  Eye,
  EyeOff,
  Globe,
  Truck,
  DollarSign,
  Bell,
  Shield,
  Palette,
  Database,
  Mail,
  Phone,
  Clock,
  MapPin,
  CreditCard,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";

const DynamicSystemSettings = () => {
  const { config, loading, error, refreshConfig } = useConfig();
  const [editingSettings, setEditingSettings] = useState({});
  const [saving, setSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState(null);

  const categoryIcons = {
    general: Globe,
    delivery: Truck,
    business: DollarSign,
    notification: Bell,
    security: Shield,
    appearance: Palette,
  };

  // Group settings by category
  const groupedSettings =
    config.settings?.reduce((groups, setting) => {
      const category = setting.category || "general";
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(setting);
      return groups;
    }, {}) || {};

  const handleEditSetting = (settingId, currentValue) => {
    setEditingSettings((prev) => ({
      ...prev,
      [settingId]: currentValue,
    }));
  };

  const handleCancelEdit = (settingId) => {
    setEditingSettings((prev) => {
      const newState = { ...prev };
      delete newState[settingId];
      return newState;
    });
  };

  const handleSaveSetting = async (settingId) => {
    const newValue = editingSettings[settingId];
    if (newValue === undefined) return;

    setSaving(true);
    setSaveStatus(null);

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${API_BASE_URL}/config/settings/${settingId}/`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ value: newValue }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setSaveStatus({
        type: "success",
        message: "Setting updated successfully!",
      });
      handleCancelEdit(settingId);

      // Refresh the config after a short delay
      setTimeout(() => {
        refreshConfig();
        setSaveStatus(null);
      }, 2000);
    } catch (error) {
      console.error("Error saving setting:", error);
      setSaveStatus({
        type: "error",
        message: "Failed to save setting. Please try again.",
      });
    } finally {
      setSaving(false);
    }
  };

  const renderSettingValue = (setting) => {
    const isEditing = editingSettings.hasOwnProperty(setting.id);
    const currentValue = isEditing
      ? editingSettings[setting.id]
      : setting.typed_value;

    if (isEditing) {
      return (
        <div className='flex items-center space-x-2 flex-wrap'>
          {setting.setting_type === "boolean" ? (
            <select
              value={currentValue.toString()}
              onChange={(e) =>
                setEditingSettings((prev) => ({
                  ...prev,
                  [setting.id]: e.target.value === "true",
                }))
              }
              className='px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary-500'
            >
              <option value='true'>True</option>
              <option value='false'>False</option>
            </select>
          ) : setting.setting_type === "text" ? (
            <textarea
              value={currentValue}
              onChange={(e) =>
                setEditingSettings((prev) => ({
                  ...prev,
                  [setting.id]: e.target.value,
                }))
              }
              className='px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 min-w-48'
              rows={2}
            />
          ) : (
            <input
              type={
                setting.setting_type === "integer" ||
                setting.setting_type === "decimal"
                  ? "number"
                  : "text"
              }
              value={currentValue}
              onChange={(e) =>
                setEditingSettings((prev) => ({
                  ...prev,
                  [setting.id]: e.target.value,
                }))
              }
              className='px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 min-w-48'
              step={setting.setting_type === "decimal" ? "0.01" : "1"}
            />
          )}
          <Button
            size='small'
            onClick={() => handleSaveSetting(setting.id)}
            disabled={saving}
          >
            {saving ? (
              <RefreshCw size={14} className='animate-spin' />
            ) : (
              <Save size={14} />
            )}
          </Button>
          <Button
            size='small'
            variant='outline'
            onClick={() => handleCancelEdit(setting.id)}
            disabled={saving}
          >
            Cancel
          </Button>
        </div>
      );
    }

    return (
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-2'>
          <span className='font-medium'>
            {setting.setting_type === "boolean"
              ? currentValue
                ? "Enabled"
                : "Disabled"
              : currentValue?.toString()}
          </span>
          <Badge
            variant={setting.is_public ? "success" : "secondary"}
            size='small'
          >
            {setting.is_public ? (
              <>
                <Eye size={10} className='mr-1' />
                Public
              </>
            ) : (
              <>
                <EyeOff size={10} className='mr-1' />
                Private
              </>
            )}
          </Badge>
        </div>
        <Button
          size='small'
          variant='outline'
          onClick={() => handleEditSetting(setting.id, currentValue)}
        >
          <Edit3 size={14} />
        </Button>
      </div>
    );
  };

  if (loading) {
    return (
      <div className='p-6'>
        <div className='flex items-center justify-center'>
          <RefreshCw className='animate-spin mr-2' size={20} />
          <span>Loading system settings...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='p-6'>
        <Card className='p-6 border-red-200 bg-red-50'>
          <div className='flex items-center mb-4'>
            <AlertCircle className='text-red-500 mr-2' size={20} />
            <h3 className='text-red-700 font-semibold'>
              Error Loading Settings
            </h3>
          </div>
          <p className='text-red-600 mb-4'>{error}</p>
          <Button onClick={refreshConfig} variant='outline' size='small'>
            <RefreshCw size={16} className='mr-2' />
            Retry
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className='p-6 space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center'>
          <Settings className='text-primary-500 mr-3' size={28} />
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>
              Dynamic System Settings
            </h1>
            <p className='text-gray-600'>
              Manage system-wide configuration and settings
            </p>
          </div>
        </div>
        <div className='flex items-center space-x-3'>
          {saveStatus && (
            <div
              className={`flex items-center px-3 py-2 rounded-lg ${
                saveStatus.type === "success"
                  ? "bg-green-100 text-green-700"
                  : "bg-red-100 text-red-700"
              }`}
            >
              {saveStatus.type === "success" ? (
                <CheckCircle size={16} className='mr-2' />
              ) : (
                <AlertCircle size={16} className='mr-2' />
              )}
              {saveStatus.message}
            </div>
          )}
          <Button onClick={refreshConfig} variant='outline'>
            <RefreshCw size={16} className='mr-2' />
            Refresh
          </Button>
        </div>
      </div>

      {/* Settings by Category */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {Object.entries(groupedSettings).map(([category, settings]) => {
          const IconComponent = categoryIcons[category] || Settings;

          return (
            <Card key={category} className='p-6'>
              <div className='flex items-center mb-4'>
                <div className='p-2 rounded-lg bg-blue-100 mr-3'>
                  <IconComponent className='text-blue-600' size={20} />
                </div>
                <div>
                  <h2 className='text-lg font-semibold capitalize'>
                    {category}
                  </h2>
                  <p className='text-sm text-gray-600'>
                    {settings.length} settings
                  </p>
                </div>
              </div>

              <div className='space-y-4'>
                {settings.map((setting) => (
                  <div
                    key={setting.id}
                    className='border-b border-gray-100 pb-4 last:border-b-0 last:pb-0'
                  >
                    <div className='mb-2'>
                      <h3 className='font-medium text-gray-900'>
                        {setting.name}
                      </h3>
                      {setting.description && (
                        <p className='text-sm text-gray-600'>
                          {setting.description}
                        </p>
                      )}
                    </div>
                    {renderSettingValue(setting)}
                  </div>
                ))}
              </div>
            </Card>
          );
        })}
      </div>

      {/* Summary */}
      <Card className='p-6 bg-blue-50'>
        <h3 className='font-semibold text-blue-800 mb-4'>Settings Summary</h3>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
          <div className='text-center'>
            <div className='text-2xl font-bold text-blue-600'>
              {config.settings?.length || 0}
            </div>
            <div className='text-sm text-blue-700'>Total Settings</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-green-600'>
              {config.settings?.filter((s) => s.is_public).length || 0}
            </div>
            <div className='text-sm text-green-700'>Public Settings</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-purple-600'>
              {Object.keys(groupedSettings).length}
            </div>
            <div className='text-sm text-purple-700'>Categories</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-orange-600'>
              {Object.keys(config.choiceOptions || {}).length}
            </div>
            <div className='text-sm text-orange-700'>Choice Types</div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DynamicSystemSettings;
