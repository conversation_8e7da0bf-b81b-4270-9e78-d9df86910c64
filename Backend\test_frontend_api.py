#!/usr/bin/env python3
"""
Test script to verify frontend API communication
"""

import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

def test_frontend_api():
    """Test API endpoints that frontend uses"""
    
    base_url = "http://127.0.0.1:8000"
    
    # Test login first
    print("🔍 Testing admin login...")
    login_data = {
        'user_name': 'admin_test',
        'password': 'admin123'
    }
    
    login_response = requests.post(f"{base_url}/api/auth/login/", json=login_data)
    print(f"Login status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.text}")
        return
    
    login_result = login_response.json()
    print(f"✅ Login successful: {login_result}")
    
    # Extract token
    access_token = login_result.get('data', {}).get('access_token')
    if not access_token:
        print("❌ No access token found")
        return
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Test backup stats
    print("\n🔍 Testing backup stats...")
    stats_response = requests.get(f"{base_url}/api/admin/backup/stats/", headers=headers)
    print(f"Stats status: {stats_response.status_code}")
    if stats_response.status_code == 200:
        print(f"✅ Stats: {stats_response.json()}")
    else:
        print(f"❌ Stats failed: {stats_response.text}")
    
    # Test backup list
    print("\n🔍 Testing backup list...")
    list_response = requests.get(f"{base_url}/api/admin/backup/backups/", headers=headers)
    print(f"List status: {list_response.status_code}")
    if list_response.status_code == 200:
        list_result = list_response.json()
        print(f"✅ Backup list: {len(list_result.get('data', {}).get('results', []))} backups found")
        print(f"Response structure: {list(list_result.keys())}")
        print(f"Data structure: {list(list_result.get('data', {}).keys())}")

        # Show first backup details
        results = list_result.get('data', {}).get('results', [])
        if results:
            print(f"First backup sample: {results[0]}")
        else:
            print("❌ No results found in data.results")
            print(f"Full response: {list_result}")
    else:
        print(f"❌ List failed: {list_response.text}")
    
    # Test backup creation
    print("\n🔍 Testing backup creation...")
    create_data = {
        'name': 'Frontend Test Backup',
        'backup_type': 'database',
        'storage_location': 'local',
        'retention_days': 7
    }
    
    create_response = requests.post(f"{base_url}/api/admin/backup/backups/", 
                                   json=create_data, headers=headers)
    print(f"Create status: {create_response.status_code}")
    
    if create_response.status_code == 201:
        create_result = create_response.json()
        print(f"✅ Backup created: {create_result}")
        
        # Extract backup ID
        backup_id = create_result.get('data', {}).get('id')
        if backup_id:
            print(f"\n🔍 Testing backup status for ID: {backup_id}")
            
            # Test status endpoint
            status_response = requests.get(f"{base_url}/api/admin/backup/backups/{backup_id}/status/", 
                                         headers=headers)
            print(f"Status check: {status_response.status_code}")
            if status_response.status_code == 200:
                status_result = status_response.json()
                print(f"✅ Status: {status_result}")
            else:
                print(f"❌ Status failed: {status_response.text}")
    else:
        print(f"❌ Create failed: {create_response.text}")

if __name__ == '__main__':
    test_frontend_api()
