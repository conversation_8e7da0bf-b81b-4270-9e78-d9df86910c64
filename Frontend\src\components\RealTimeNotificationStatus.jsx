import React, { useState, useEffect } from 'react';
import { Bell, CheckCircle, Clock, AlertCircle, XCircle, Wifi, WifiOff } from 'lucide-react';
import websocketService from '../services/websocketService';

const RealTimeNotificationStatus = ({ notificationId, onStatusUpdate }) => {
  const [status, setStatus] = useState('pending');
  const [deliveryStats, setDeliveryStats] = useState({
    total: 0,
    sent: 0,
    delivered: 0,
    failed: 0
  });
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    // Check initial connection status
    setIsConnected(websocketService.getConnectionStatus().connected);

    // Set up event listeners
    const handleConnectionChange = (connected) => {
      setIsConnected(connected);
    };

    const handleNotificationUpdate = (data) => {
      if (data.notification_id === notificationId) {
        setStatus(data.status);
        setDeliveryStats(data.delivery_stats || deliveryStats);
        setLastUpdate(new Date());
        
        // Notify parent component
        if (onStatusUpdate) {
          onStatusUpdate(data);
        }
      }
    };

    const handleDeliveryUpdate = (data) => {
      if (data.notification_id === notificationId) {
        setDeliveryStats(prev => ({
          ...prev,
          ...data.stats
        }));
        setLastUpdate(new Date());
      }
    };

    // Subscribe to events
    websocketService.on('connected', () => handleConnectionChange(true));
    websocketService.on('disconnected', () => handleConnectionChange(false));
    websocketService.on('notificationStatusUpdate', handleNotificationUpdate);
    websocketService.on('deliveryUpdate', handleDeliveryUpdate);

    // Subscribe to specific notification updates
    if (notificationId && isConnected) {
      websocketService.subscribeToSpecificNotification(notificationId);
    }

    // Cleanup
    return () => {
      websocketService.off('connected', () => handleConnectionChange(true));
      websocketService.off('disconnected', () => handleConnectionChange(false));
      websocketService.off('notificationStatusUpdate', handleNotificationUpdate);
      websocketService.off('deliveryUpdate', handleDeliveryUpdate);
      
      if (notificationId) {
        websocketService.unsubscribeFromSpecificNotification(notificationId);
      }
    };
  }, [notificationId, isConnected]);

  const getStatusIcon = () => {
    switch (status) {
      case 'sent':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'delivered':
        return <CheckCircle size={16} className="text-green-700" />;
      case 'failed':
        return <XCircle size={16} className="text-red-600" />;
      case 'sending':
        return <Clock size={16} className="text-yellow-600 animate-pulse" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'sending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getDeliveryRate = () => {
    if (deliveryStats.total === 0) return 0;
    return Math.round((deliveryStats.delivered / deliveryStats.total) * 100);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bell size={16} className="text-blue-600" />
          <span className="font-medium text-gray-900">Real-time Status</span>
        </div>
        <div className="flex items-center gap-2">
          {isConnected ? (
            <>
              <Wifi size={14} className="text-green-600" />
              <span className="text-xs text-green-600">Connected</span>
            </>
          ) : (
            <>
              <WifiOff size={14} className="text-red-600" />
              <span className="text-xs text-red-600">Disconnected</span>
            </>
          )}
        </div>
      </div>

      {/* Status Badge */}
      <div className="flex items-center gap-3">
        <div className={`flex items-center gap-2 px-3 py-1 rounded-full border ${getStatusColor()}`}>
          {getStatusIcon()}
          <span className="text-sm font-medium capitalize">{status}</span>
        </div>
        {lastUpdate && (
          <span className="text-xs text-gray-500">
            Updated {lastUpdate.toLocaleTimeString()}
          </span>
        )}
      </div>

      {/* Delivery Statistics */}
      {deliveryStats.total > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Delivery Progress</span>
            <span className="font-medium">{getDeliveryRate()}%</span>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getDeliveryRate()}%` }}
            />
          </div>

          {/* Detailed Stats */}
          <div className="grid grid-cols-4 gap-2 text-center">
            <div className="bg-gray-50 rounded p-2">
              <div className="text-lg font-semibold text-gray-900">{deliveryStats.total}</div>
              <div className="text-xs text-gray-600">Total</div>
            </div>
            <div className="bg-blue-50 rounded p-2">
              <div className="text-lg font-semibold text-blue-900">{deliveryStats.sent}</div>
              <div className="text-xs text-blue-600">Sent</div>
            </div>
            <div className="bg-green-50 rounded p-2">
              <div className="text-lg font-semibold text-green-900">{deliveryStats.delivered}</div>
              <div className="text-xs text-green-600">Delivered</div>
            </div>
            <div className="bg-red-50 rounded p-2">
              <div className="text-lg font-semibold text-red-900">{deliveryStats.failed}</div>
              <div className="text-xs text-red-600">Failed</div>
            </div>
          </div>
        </div>
      )}

      {/* Real-time Indicator */}
      {isConnected && (
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Live updates enabled</span>
        </div>
      )}
    </div>
  );
};

export default RealTimeNotificationStatus;
