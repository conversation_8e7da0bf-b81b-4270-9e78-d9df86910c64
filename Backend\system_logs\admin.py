from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import SystemLog, LogStatistics


@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = [
        'formatted_timestamp', 'level_badge', 'category_badge',
        'message_preview', 'user_display', 'ip_address', 'source'
    ]
    list_filter = [
        'level', 'category', 'timestamp', 'user'
    ]
    search_fields = [
        'message', 'details', 'user__user_name', 'user__email',
        'ip_address', 'source'
    ]
    readonly_fields = [
        'id', 'timestamp', 'formatted_timestamp', 'request_id',
        'session_id', 'response_time'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    list_per_page = 50

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'timestamp', 'level', 'category')
        }),
        ('Message Content', {
            'fields': ('message', 'details')
        }),
        ('Context', {
            'fields': ('user', 'ip_address', 'user_agent', 'source')
        }),
        ('Technical Details', {
            'fields': ('request_id', 'session_id', 'response_time', 'extra_data'),
            'classes': ('collapse',)
        }),
    )

    def level_badge(self, obj):
        """Display level with color coding"""
        colors = {
            'debug': '#6c757d',
            'info': '#17a2b8',
            'warning': '#ffc107',
            'error': '#dc3545',
            'critical': '#6f42c1'
        }
        color = colors.get(obj.level, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; '
            'border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.level.upper()
        )
    level_badge.short_description = 'Level'

    def category_badge(self, obj):
        """Display category with styling"""
        return format_html(
            '<span style="background-color: #e9ecef; color: #495057; padding: 2px 6px; '
            'border-radius: 3px; font-size: 11px;">{}</span>',
            obj.get_category_display()
        )
    category_badge.short_description = 'Category'

    def message_preview(self, obj):
        """Display truncated message"""
        if len(obj.message) > 80:
            return obj.message[:80] + '...'
        return obj.message
    message_preview.short_description = 'Message'

    def has_add_permission(self, request):
        """Disable adding logs through admin"""
        return False

    def has_change_permission(self, request, obj=None):
        """Disable editing logs through admin"""
        return False


@admin.register(LogStatistics)
class LogStatisticsAdmin(admin.ModelAdmin):
    list_display = [
        'date', 'total_logs', 'error_count', 'warning_count',
        'info_count', 'debug_count'
    ]
    list_filter = ['date']
    readonly_fields = [
        'date', 'debug_count', 'info_count', 'warning_count',
        'error_count', 'critical_count', 'total_logs', 'created_at', 'updated_at'
    ]
    ordering = ['-date']

    def has_add_permission(self, request):
        """Disable adding statistics through admin"""
        return False

    def has_change_permission(self, request, obj=None):
        """Disable editing statistics through admin"""
        return False
