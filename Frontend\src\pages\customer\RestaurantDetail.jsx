import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  Star,
  Clock,
  DollarSign,
  MapPin,
  Info,
  Plus,
  Minus,
  ShoppingBag,
  AlertCircle,
  Heart,
  X,
  Check,
  ShoppingCart,
  Edit3,
} from "lucide-react";
import { API_BASE_URL } from "../../config/api";
import { useCart } from "../../context/CartContext";
import { useFavorites } from "../../context/FavoritesContext";
import { useSocial } from "../../context/SocialContext";
import { useAuth } from "../../context/AuthContext";
import { useOrder } from "../../context/OrderContext";
import { getCategoryName } from "../../utils/categoryUtils";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import FavoriteButton from "../../components/common/FavoriteButton";
import ShareButton from "../../components/social/ShareButton";
import RestaurantRating from "../../components/rating/RestaurantRating";
import RatingModal from "../../components/rating/RatingModal";

const RestaurantDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { orders } = useOrder();
  const { cart, addToCart, clearCart } = useCart();
  const { toggleFavorite, isFavorite } = useFavorites();
  const [restaurant, setRestaurant] = useState(null);
  const [menuCategories, setMenuCategories] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [activeCategory, setActiveCategory] = useState("All");
  const [selectedItem, setSelectedItem] = useState(null);
  const [itemQuantity, setItemQuantity] = useState(1);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isHeaderFixed, setIsHeaderFixed] = useState(false);
  const [addedToCart, setAddedToCart] = useState(null);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [hasOrderedFromRestaurant, setHasOrderedFromRestaurant] =
    useState(false);
  const [deliveredOrders, setDeliveredOrders] = useState([]);
  const [quickAddQuantity, setQuickAddQuantity] = useState({});

  useEffect(() => {
    const fetchRestaurantData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch restaurant details
        const restaurantResponse = await fetch(
          `${API_BASE_URL}/restaurant/restaurants/${id}/`
        );
        if (!restaurantResponse.ok) {
          throw new Error("Restaurant not found");
        }
        const restaurantData = await restaurantResponse.json();
        setRestaurant(restaurantData);

        // Fetch menu categories for this restaurant
        const categoriesResponse = await fetch(
          `${API_BASE_URL}/restaurant/menu-categories/?restaurant_id=${id}`
        );
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setMenuCategories(categoriesData);

          // Fetch menu items for each category
          const allMenuItems = [];
          for (const category of categoriesData) {
            const itemsResponse = await fetch(
              `${API_BASE_URL}/restaurant/menu-items/?category_id=${category.id}`
            );
            if (itemsResponse.ok) {
              const itemsData = await itemsResponse.json();
              // Add category info to each item
              const itemsWithCategory = itemsData.map((item) => ({
                ...item,
                category: category.name,
                categoryId: category.id,
              }));
              allMenuItems.push(...itemsWithCategory);
            }
          }
          setMenuItems(allMenuItems);
        }
      } catch (error) {
        console.error("Error fetching restaurant data:", error);
        setError(error.message);
        // Navigate back to restaurants list if restaurant not found
        if (error.message === "Restaurant not found") {
          navigate("/restaurants");
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchRestaurantData();
    }
  }, [id, navigate]);

  useEffect(() => {
    const handleScroll = () => {
      setIsHeaderFixed(window.scrollY > 350);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Check if customer has ordered from this restaurant
  useEffect(() => {
    if (user && user.role === "customer" && orders && restaurant) {
      const restaurantOrders = orders.filter(
        (order) =>
          order.restaurant?.id === parseInt(id) ||
          order.restaurant_id === parseInt(id)
      );

      const delivered = restaurantOrders.filter(
        (order) => order.status === "delivered"
      );

      setDeliveredOrders(delivered);
      setHasOrderedFromRestaurant(delivered.length > 0);
    }
  }, [user, orders, restaurant, id]);

  const filteredMenuItems =
    activeCategory === "All"
      ? menuItems
      : menuItems.filter((item) => item.category === activeCategory);

  const handleSelectItem = (item) => {
    setSelectedItem(item);
    setItemQuantity(1);
    setError(null);
  };

  const handleCloseItemModal = () => {
    setSelectedItem(null);
    setItemQuantity(1);
    setError(null);
  };

  const handleAddToCart = () => {
    if (!restaurant || !selectedItem) return;

    const result = addToCart(
      restaurant.id,
      restaurant.name,
      selectedItem,
      itemQuantity
    );

    if (result.success) {
      handleCloseItemModal();
    } else {
      setError(result.error);
    }
  };

  const handleQuickAddToCart = (item, event) => {
    // Prevent the card click event from triggering
    event.stopPropagation();

    // Get quantity for this item (default to 1 if not set)
    const quantity = quickAddQuantity[item.id] || 1;

    // Add item to cart
    const result = addToCart(restaurant.id, restaurant.name, item, quantity);

    if (result.success) {
      // Show success message
      setAddedToCart(item.id);

      // Clear success message after 2 seconds
      setTimeout(() => {
        setAddedToCart(null);
      }, 2000);

      // Reset quantity
      setQuickAddQuantity((prev) => ({
        ...prev,
        [item.id]: 1,
      }));
    } else {
      // Show error in modal
      setSelectedItem(item);
      setError(result.error);
    }
  };

  const incrementQuickAddQuantity = (itemId, event) => {
    event.stopPropagation();
    setQuickAddQuantity((prev) => ({
      ...prev,
      [itemId]: (prev[itemId] || 1) + 1,
    }));
  };

  const decrementQuickAddQuantity = (itemId, event) => {
    event.stopPropagation();
    setQuickAddQuantity((prev) => ({
      ...prev,
      [itemId]: Math.max(1, (prev[itemId] || 1) - 1),
    }));
  };

  const hasItemsInCart =
    cart.restaurantId === restaurant?.id && cart.items.length > 0;

  const handleToggleFavorite = () => {
    if (restaurant) {
      toggleFavorite(restaurant);
    }
  };

  const handleWriteReview = () => {
    if (deliveredOrders.length > 0) {
      // Use the most recent delivered order for rating
      const mostRecentOrder = deliveredOrders.sort(
        (a, b) =>
          new Date(b.created_at || b.createdAt) -
          new Date(a.created_at || a.createdAt)
      )[0];
      setSelectedItem(null); // Clear any selected menu item
      setShowRatingModal(true);
    }
  };

  const handleRatingSubmitted = () => {
    setShowRatingModal(false);
    // Optionally refresh restaurant rating data
    window.location.reload(); // Simple refresh to update ratings
  };

  if (loading || !restaurant) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='border-primary-500 border-t-2 border-b-2 rounded-full w-12 h-12 animate-spin'></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='text-center'>
          <AlertCircle className='mx-auto mb-4 w-12 h-12 text-red-500' />
          <p className='text-gray-600'>{error}</p>
          <Button onClick={() => navigate("/restaurants")} className='mt-4'>
            Back to Restaurants
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='animate-fade-in'>
      {/* Restaurant Cover Image */}
      <div
        className='relative bg-cover bg-center h-64 sm:h-80 md:h-96'
        style={{
          backgroundImage: `url('${
            restaurant.banner || "/placeholder-restaurant.jpg"
          }')`,
        }}
      >
        <div className='absolute inset-0 bg-black bg-opacity-30'></div>
      </div>

      {/* Restaurant Info Card */}
      <div className='mx-auto px-4 container'>
        <div className='z-10 relative -mt-20 mb-8'>
          <Card className='flex md:flex-row flex-col md:items-center p-6'>
            <div className='bg-white shadow-lg md:mr-6 mb-4 md:mb-0 rounded-lg w-24 md:w-32 h-24 md:h-32 overflow-hidden'>
              <img
                src={restaurant.logo || "/placeholder-restaurant.jpg"}
                alt={restaurant.name}
                className='w-full h-full object-cover'
              />
            </div>

            <div className='flex-grow'>
              <div className='flex justify-between items-center'>
                <h1 className='font-poppins font-bold text-2xl md:text-3xl'>
                  {restaurant.name}
                </h1>
                <div className='flex items-center space-x-2'>
                  <ShareButton
                    type='restaurant'
                    data={restaurant}
                    variant='outline'
                    size='small'
                    showLabel={false}
                  />
                  <FavoriteButton
                    restaurant={restaurant}
                    size='large'
                    variant='ghost'
                  />
                </div>
              </div>

              <div className='flex flex-wrap items-center gap-x-4 gap-y-2 mt-2 text-text-secondary text-sm'>
                <RestaurantRating
                  restaurantId={restaurant.id}
                  showDetails={false}
                />

                <div className='flex items-center'>
                  <Clock size={18} className='mr-1' />
                  <span>{restaurant.average_preparation_time || 30} min</span>
                </div>

                <div className='flex items-center'>
                  <DollarSign size={18} className='mr-1' />
                  <span>
                    Min. order ${restaurant.min_order_amount || "0.00"}
                  </span>
                </div>

                <div className='flex items-center'>
                  <MapPin size={18} className='mr-1' />
                  <span>2.5 km away</span>
                </div>
              </div>

              <div className='flex flex-wrap gap-2 mt-3'>
                {restaurant.cuisine_types &&
                  restaurant.cuisine_types.map((cuisine) => (
                    <Badge key={cuisine.id || cuisine.name || cuisine} variant='secondary' size='small'>
                      {getCategoryName(cuisine)}
                    </Badge>
                  ))}

                {restaurant.is_active ? (
                  <Badge variant='success' size='small'>
                    Open Now
                  </Badge>
                ) : (
                  <Badge variant='danger' size='small'>
                    Closed
                  </Badge>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Sticky Category Menu */}
      <div
        className={`bg-white py-3 transition-all duration-300 ${
          isHeaderFixed ? "sticky top-0 shadow-nav z-20" : ""
        }`}
      >
        <div className='mx-auto px-4 container'>
          <div className='flex py-2 overflow-x-auto hide-scrollbar'>
            <div className='flex space-x-4'>
              <button
                className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                  activeCategory === "All"
                    ? "bg-primary-500 text-white"
                    : "bg-background-light text-text-primary hover:bg-gray-200"
                }`}
                onClick={() => setActiveCategory("All")}
              >
                All
              </button>
              {menuCategories.map((category) => (
                <button
                  key={category.id}
                  className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                    activeCategory === category.name
                      ? "bg-primary-500 text-white"
                      : "bg-background-light text-text-primary hover:bg-gray-200"
                  }`}
                  onClick={() => setActiveCategory(category.name)}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div className='mx-auto px-4 py-8 container'>
        <h2 className='mb-6 font-poppins font-semibold text-2xl'>
          {activeCategory === "All" ? "Menu" : activeCategory}
        </h2>

        <div className='gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
          {filteredMenuItems.map((item) => (
            <Card
              key={item.id}
              className='hover:shadow-lg transition-shadow cursor-pointer'
              onClick={() => handleSelectItem(item)}
            >
              <div className='flex flex-col h-full'>
                <div className='flex'>
                  <div className='flex-grow pr-4'>
                    <h3 className='font-medium text-lg'>{item.name}</h3>
                    <p className='mt-1 mb-2 text-text-secondary text-sm line-clamp-2'>
                      {item.description}
                    </p>
                    <div className='flex justify-between items-center mt-auto'>
                      <span className='font-semibold'>
                        ${parseFloat(item.price || 0).toFixed(2)}
                      </span>
                      {item.isPopular && (
                        <Badge variant='primary' size='small'>
                          Popular
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className='flex-shrink-0 bg-gray-100 rounded-md w-24 h-24 overflow-hidden'>
                    <img
                      src={item.image}
                      alt={item.name}
                      className='w-full h-full object-cover'
                    />
                  </div>
                </div>

                {/* Quick Add to Cart */}
                <div
                  className='mt-4 pt-3 border-gray-100 border-t'
                  onClick={(e) => e.stopPropagation()}
                >
                  {addedToCart === item.id ? (
                    <div className='flex justify-center items-center bg-green-50 px-3 py-2 rounded-md text-green-700 text-sm'>
                      <Check size={16} className='mr-1' />
                      Added to cart!
                    </div>
                  ) : (
                    <div className='flex justify-between items-center'>
                      <div className='flex items-center border border-gray-200 rounded-md'>
                        <button
                          className='px-2 py-1 text-text-primary disabled:text-gray-300'
                          onClick={(e) => decrementQuickAddQuantity(item.id, e)}
                          disabled={(quickAddQuantity[item.id] || 1) <= 1}
                        >
                          <Minus size={16} />
                        </button>
                        <span className='px-3 py-1 font-medium text-sm'>
                          {quickAddQuantity[item.id] || 1}
                        </span>
                        <button
                          className='px-2 py-1 text-text-primary'
                          onClick={(e) => incrementQuickAddQuantity(item.id, e)}
                        >
                          <Plus size={16} />
                        </button>
                      </div>

                      <Button
                        variant='primary'
                        size='small'
                        icon={<ShoppingCart size={16} />}
                        onClick={(e) => handleQuickAddToCart(item, e)}
                      >
                        Add
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredMenuItems.length === 0 && (
          <div className='py-12 text-center'>
            <Info size={48} className='mx-auto mb-4 text-text-secondary' />
            <h3 className='mb-2 font-medium text-xl'>No Menu Items Found</h3>
            <p className='text-text-secondary'>
              This category doesn't have any items yet.
            </p>
          </div>
        )}
      </div>

      {/* Reviews Section */}
      <div className='mx-auto px-4 py-8 border-gray-100 border-t container'>
        <div className='flex justify-between items-center mb-6'>
          <h2 className='font-poppins font-semibold text-2xl'>
            Reviews & Ratings
          </h2>

          {/* Write Review Button - Only show for customers who have ordered */}
          {user && user.role === "customer" && hasOrderedFromRestaurant && (
            <Button
              variant='outline'
              size='small'
              icon={<Edit3 size={16} />}
              onClick={handleWriteReview}
              className='hover:bg-blue-50 border-blue-600 text-blue-600'
            >
              Write Review
            </Button>
          )}
        </div>

        {/* Rating Summary */}
        <Card className='mb-6'>
          <RestaurantRating restaurantId={restaurant.id} showDetails={true} />
        </Card>

        {/* Individual Reviews */}
        <div className='space-y-4'>
          {[
            {
              id: 1,
              user: "Sarah Johnson",
              rating: 5,
              date: "2 days ago",
              comment:
                "Amazing food quality and fast delivery! The biryani was perfectly spiced and the portions were generous. Will definitely order again.",
              helpful: 12,
            },
            {
              id: 2,
              user: "Mike Chen",
              rating: 4,
              date: "1 week ago",
              comment:
                "Good food overall. The kebabs were delicious but the naan could have been warmer. Delivery was on time.",
              helpful: 8,
            },
            {
              id: 3,
              user: "Emily Davis",
              rating: 5,
              date: "2 weeks ago",
              comment:
                "Excellent authentic Afghan cuisine! The lamb karahi was outstanding and the customer service was very friendly. Highly recommended!",
              helpful: 15,
            },
            {
              id: 4,
              user: "Ahmed Ali",
              rating: 4,
              date: "3 weeks ago",
              comment:
                "Fresh ingredients and good taste. The portion sizes are reasonable for the price. Packaging was also very good.",
              helpful: 6,
            },
          ].map((review) => (
            <Card key={review.id} className='p-4'>
              <div className='flex justify-between items-start mb-3'>
                <div className='flex items-center'>
                  <div className='flex justify-center items-center bg-primary-100 mr-3 rounded-full w-10 h-10'>
                    <span className='font-medium text-primary-600'>
                      {review.user
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </span>
                  </div>
                  <div>
                    <h4 className='font-medium'>{review.user}</h4>
                    <div className='flex items-center'>
                      <div className='flex items-center mr-2'>
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            size={14}
                            className={`${
                              star <= review.rating
                                ? "text-yellow-500 fill-current"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <span className='text-text-secondary text-sm'>
                        {review.date}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <p className='mb-3 text-text-secondary leading-relaxed'>
                {review.comment}
              </p>

              <div className='flex justify-between items-center'>
                <button className='flex items-center text-text-secondary hover:text-primary-500 text-sm'>
                  <Heart size={14} className='mr-1' />
                  Helpful ({review.helpful})
                </button>
              </div>
            </Card>
          ))}
        </div>

        {/* Load More Reviews */}
        <div className='mt-6 text-center'>
          <Button variant='secondary'>Load More Reviews</Button>
        </div>
      </div>

      {/* Item Detail Modal */}
      {selectedItem && (
        <div className='z-50 fixed inset-0 flex justify-center items-center bg-black bg-opacity-50 p-4'>
          <div className='bg-white shadow-lg rounded-lg w-full max-w-2xl max-h-[90vh] overflow-auto animate-fade-in'>
            <div className='relative bg-gray-100 h-64'>
              <img
                src={selectedItem.image}
                alt={selectedItem.name}
                className='w-full h-full object-cover'
              />
              <button
                className='top-4 right-4 absolute bg-white shadow-md p-2 rounded-full'
                onClick={handleCloseItemModal}
              >
                <X size={20} />
              </button>
            </div>

            <div className='p-6'>
              <h3 className='mb-2 font-semibold text-2xl'>
                {selectedItem.name}
              </h3>
              <p className='mb-4 text-text-secondary'>
                {selectedItem.description}
              </p>
              <div className='mb-6 font-bold text-xl'>
                ${parseFloat(selectedItem.price || 0).toFixed(2)}
              </div>

              {error && (
                <div className='flex items-start bg-red-50 mb-6 p-4 border-accent-red border-l-4 rounded-md'>
                  <AlertCircle
                    size={18}
                    className='flex-shrink-0 mt-0.5 mr-2 text-accent-red'
                  />
                  <div>
                    <p className='font-medium text-sm text-accent-red'>
                      {error}
                    </p>
                    <p className='mt-1 text-text-secondary text-sm'>
                      Would you like to clear your cart and add items from this
                      restaurant instead?
                    </p>
                    <div className='mt-3'>
                      <Button
                        variant='danger'
                        size='small'
                        onClick={async () => {
                          await clearCart();
                          setTimeout(() => handleAddToCart(), 0);
                        }}
                      >
                        Clear Cart & Add
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              <div className='flex justify-between items-center'>
                <div className='flex items-center border border-gray-200 rounded-md'>
                  <button
                    className='px-3 py-2 text-text-primary disabled:text-gray-300'
                    onClick={() =>
                      setItemQuantity((prev) => Math.max(1, prev - 1))
                    }
                    disabled={itemQuantity <= 1}
                  >
                    <Minus size={18} />
                  </button>
                  <span className='px-4 py-2 font-medium'>{itemQuantity}</span>
                  <button
                    className='px-3 py-2 text-text-primary'
                    onClick={() => setItemQuantity((prev) => prev + 1)}
                  >
                    <Plus size={18} />
                  </button>
                </div>

                <Button
                  variant='primary'
                  onClick={handleAddToCart}
                  icon={<ShoppingBag size={18} />}
                >
                  Add to Cart - $
                  {(parseFloat(selectedItem.price || 0) * itemQuantity).toFixed(
                    2
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Floating Cart Button */}
      {hasItemsInCart && (
        <div className='right-0 bottom-6 left-0 z-30 fixed flex justify-center'>
          <Button
            variant='primary'
            className='shadow-lg px-6 py-3 rounded-full'
            to='/cart'
            icon={<ShoppingBag size={20} />}
          >
            <div className='flex items-center'>
              <span className='flex justify-center items-center bg-white mr-2 rounded-full w-6 h-6 font-bold text-primary-500'>
                {cart.items.reduce((total, item) => total + item.quantity, 0)}
              </span>
              <span>View Cart - ${cart.total.toFixed(2)}</span>
            </div>
          </Button>
        </div>
      )}

      {/* Rating Modal */}
      {showRatingModal && deliveredOrders.length > 0 && (
        <RatingModal
          isOpen={showRatingModal}
          onClose={() => setShowRatingModal(false)}
          order={
            deliveredOrders.sort(
              (a, b) =>
                new Date(b.created_at || b.createdAt) -
                new Date(a.created_at || a.createdAt)
            )[0]
          }
          onRatingSubmitted={handleRatingSubmitted}
        />
      )}
    </div>
  );
};

export default RestaurantDetail;
