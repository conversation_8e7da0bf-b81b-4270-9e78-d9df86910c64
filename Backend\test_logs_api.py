#!/usr/bin/env python3
"""
Test script for system logs API endpoints
"""

import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse

User = get_user_model()

def test_logs_api():
    """Test the logs API endpoints"""
    print("🧪 Testing System Logs API Endpoints...")
    print("=" * 60)
    
    # Use existing admin user
    admin_user = User.objects.filter(role='admin').first()
    if not admin_user:
        print("❌ No admin user found in database")
        return

    print(f"✅ Using existing admin user: {admin_user.user_name}")

    # Set a known password for testing
    admin_user.set_password('testpass123')
    admin_user.save()
    
    # Create a test client
    client = Client()
    
    # Login the admin user
    login_data = {
        'user_name': admin_user.user_name,
        'password': 'testpass123'
    }
    print(f"🔍 Login request data: {login_data}")

    login_response = client.post('/api/auth/login/',
                                json.dumps(login_data),
                                content_type='application/json')
    
    print(f"Login response status: {login_response.status_code}")
    print(f"Login response content: {login_response.content}")

    if login_response.status_code == 200:
        login_data = login_response.json()
        # Access token is nested in data field
        access_token = login_data.get('data', {}).get('access_token')
        if access_token:
            print(f"✅ Admin login successful, token: {access_token[:20]}...")
        else:
            print(f"❌ No access token in response: {login_data}")
            return
        
        # Set authorization header
        headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
        
        # Test 1: Get system logs
        print("\n🔍 Testing GET /api/admin/logs/")
        logs_response = client.get('/api/admin/logs/', **headers)
        print(f"Status: {logs_response.status_code}")
        
        if logs_response.status_code == 200:
            logs_data = logs_response.json()
            print(f"✅ Logs retrieved successfully")
            print(f"Response structure: {list(logs_data.keys())}")
            if 'results' in logs_data:
                print(f"Total logs: {logs_data.get('count', 'N/A')}")
                print(f"Logs in response: {len(logs_data['results'])}")
            elif 'data' in logs_data and 'logs' in logs_data['data']:
                print(f"Logs in response: {len(logs_data['data']['logs'])}")
        else:
            print(f"❌ Failed to get logs: {logs_response.content}")
        
        # Test 2: Get log statistics
        print("\n📊 Testing GET /api/admin/logs/stats/")
        stats_response = client.get('/api/admin/logs/stats/', **headers)
        print(f"Status: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print(f"✅ Stats retrieved successfully")
            print(f"Stats data: {json.dumps(stats_data, indent=2)}")
        else:
            print(f"❌ Failed to get stats: {stats_response.content}")
        
        # Test 3: Test filtering
        print("\n🔍 Testing filtered logs (level=error)")
        filtered_response = client.get('/api/admin/logs/?level=error', **headers)
        print(f"Status: {filtered_response.status_code}")
        
        if filtered_response.status_code == 200:
            filtered_data = filtered_response.json()
            print(f"✅ Filtered logs retrieved successfully")
            if 'results' in filtered_data:
                print(f"Error logs count: {len(filtered_data['results'])}")
            elif 'data' in filtered_data and 'logs' in filtered_data['data']:
                print(f"Error logs count: {len(filtered_data['data']['logs'])}")
        else:
            print(f"❌ Failed to get filtered logs: {filtered_response.content}")
        
        # Test 4: Test export (CSV)
        print("\n📄 Testing CSV export")
        export_response = client.get('/api/admin/logs/export/?format=csv', **headers)
        print(f"Status: {export_response.status_code}")
        
        if export_response.status_code == 200:
            print(f"✅ CSV export successful")
            print(f"Content type: {export_response.get('Content-Type', 'N/A')}")
            print(f"Content length: {len(export_response.content)} bytes")
        else:
            print(f"❌ Failed to export CSV: {export_response.content}")
        
    else:
        print(f"❌ Admin login failed: {login_response.content}")
    
    print("\n" + "=" * 60)
    print("🏁 API Testing Complete!")


if __name__ == '__main__':
    test_logs_api()
