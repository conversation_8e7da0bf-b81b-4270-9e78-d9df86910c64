from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Notification, NotificationTemplate, NotificationDelivery,
    NotificationSettings, NotificationStatistics
)

User = get_user_model()


class NotificationTemplateSerializer(serializers.ModelSerializer):
    """Serializer for NotificationTemplate model"""
    
    class Meta:
        model = NotificationTemplate
        fields = [
            'id', 'name', 'template_type', 'title_template', 'message_template',
            'supports_email', 'supports_sms', 'supports_push', 'supports_in_app',
            'email_subject_template', 'email_html_template', 'is_active',
            'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']


class NotificationDeliverySerializer(serializers.ModelSerializer):
    """Serializer for NotificationDelivery model"""
    
    class Meta:
        model = NotificationDelivery
        fields = [
            'id', 'recipient_email', 'recipient_phone', 'channel', 'status',
            'sent_at', 'delivered_at', 'opened_at', 'clicked_at',
            'error_message', 'retry_count', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class NotificationSerializer(serializers.ModelSerializer):
    """Serializer for Notification model"""
    recipients_display = serializers.ReadOnlyField(source='get_recipients_display')
    delivery_rate = serializers.ReadOnlyField()
    is_scheduled = serializers.ReadOnlyField()
    deliveries = NotificationDeliverySerializer(many=True, read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'message', 'notification_type', 'recipient_type',
            'recipient_roles', 'recipient_emails', 'channels', 'template',
            'status', 'scheduled_at', 'sent_at', 'context_data', 'priority',
            'total_recipients', 'successful_deliveries', 'failed_deliveries',
            'recipients_display', 'delivery_rate', 'is_scheduled',
            'created_by', 'created_at', 'updated_at', 'deliveries'
        ]
        read_only_fields = [
            'id', 'total_recipients', 'successful_deliveries', 'failed_deliveries',
            'sent_at', 'created_at', 'updated_at'
        ]


class NotificationListSerializer(serializers.ModelSerializer):
    """Simplified serializer for notification lists"""
    recipients_display = serializers.ReadOnlyField(source='get_recipients_display')
    delivery_rate = serializers.ReadOnlyField()
    created_by_name = serializers.CharField(source='created_by.user_name', read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'message', 'notification_type', 'recipient_type',
            'channels', 'status', 'scheduled_at', 'sent_at', 'priority',
            'total_recipients', 'successful_deliveries', 'failed_deliveries',
            'recipients_display', 'delivery_rate', 'created_by_name', 'created_at'
        ]


class SendNotificationSerializer(serializers.Serializer):
    """Serializer for sending notifications"""
    title = serializers.CharField(max_length=200)
    message = serializers.CharField()
    notification_type = serializers.ChoiceField(choices=Notification.NOTIFICATION_TYPES)
    recipient_type = serializers.ChoiceField(choices=Notification.RECIPIENT_TYPES)
    
    # Optional fields based on recipient type
    recipient_users = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True
    )
    recipient_roles = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    recipient_emails = serializers.ListField(
        child=serializers.EmailField(),
        required=False,
        allow_empty=True
    )
    
    channels = serializers.ListField(
        child=serializers.ChoiceField(choices=[choice[0] for choice in Notification.CHANNELS]),
        required=False,
        default=['email']
    )
    
    template_id = serializers.UUIDField(required=False, allow_null=True)
    context_data = serializers.JSONField(required=False, default=dict)
    priority = serializers.IntegerField(min_value=1, max_value=4, default=1)
    scheduled_at = serializers.DateTimeField(required=False, allow_null=True)
    
    def validate(self, data):
        """Validate notification data based on recipient type"""
        recipient_type = data.get('recipient_type')
        
        if recipient_type == 'user' and not data.get('recipient_users'):
            raise serializers.ValidationError({
                'recipient_users': 'Required when recipient_type is "user"'
            })
        
        if recipient_type == 'role' and not data.get('recipient_roles'):
            raise serializers.ValidationError({
                'recipient_roles': 'Required when recipient_type is "role"'
            })
        
        if recipient_type == 'custom' and not data.get('recipient_emails'):
            raise serializers.ValidationError({
                'recipient_emails': 'Required when recipient_type is "custom"'
            })
        
        return data


class NotificationSettingsSerializer(serializers.ModelSerializer):
    """Serializer for NotificationSettings model"""
    
    class Meta:
        model = NotificationSettings
        fields = [
            'email_enabled', 'email_from_name', 'email_from_address', 'email_reply_to',
            'sms_enabled', 'sms_provider', 'sms_from_number',
            'push_enabled', 'push_provider',
            'max_emails_per_hour', 'max_sms_per_hour', 'max_push_per_hour',
            'max_retry_attempts', 'retry_delay_minutes',
            'default_email_template', 'updated_at'
        ]
        read_only_fields = ['updated_at']
        
        # Hide sensitive fields
        extra_kwargs = {
            'sms_api_key': {'write_only': True},
            'sms_api_secret': {'write_only': True},
            'push_api_key': {'write_only': True},
            'push_server_key': {'write_only': True},
        }


class NotificationStatisticsSerializer(serializers.ModelSerializer):
    """Serializer for NotificationStatistics model"""
    delivery_rate = serializers.ReadOnlyField()
    email_delivery_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = NotificationStatistics
        fields = [
            'date', 'order_notifications', 'user_notifications', 'restaurant_notifications',
            'delivery_notifications', 'system_notifications', 'email_sent', 'email_delivered',
            'email_failed', 'sms_sent', 'sms_delivered', 'sms_failed', 'push_sent',
            'push_delivered', 'push_failed', 'total_sent', 'total_delivered', 'total_failed',
            'delivery_rate', 'email_delivery_rate', 'created_at', 'updated_at'
        ]


class NotificationStatsSerializer(serializers.Serializer):
    """Serializer for notification statistics summary"""
    sent_today = serializers.IntegerField()
    delivery_rate = serializers.FloatField()
    pending = serializers.IntegerField()
    failed = serializers.IntegerField()
    
    # Channel breakdown
    email_sent = serializers.IntegerField()
    email_delivered = serializers.IntegerField()
    email_failed = serializers.IntegerField()
    
    sms_sent = serializers.IntegerField()
    sms_delivered = serializers.IntegerField()
    sms_failed = serializers.IntegerField()
    
    push_sent = serializers.IntegerField()
    push_delivered = serializers.IntegerField()
    push_failed = serializers.IntegerField()
    
    # Type breakdown
    order_notifications = serializers.IntegerField()
    user_notifications = serializers.IntegerField()
    restaurant_notifications = serializers.IntegerField()
    delivery_notifications = serializers.IntegerField()
    system_notifications = serializers.IntegerField()


class BulkNotificationSerializer(serializers.Serializer):
    """Serializer for bulk notification operations"""
    notification_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1
    )
    action = serializers.ChoiceField(choices=['send', 'cancel', 'delete'])
    
    def validate_notification_ids(self, value):
        """Validate that all notification IDs exist"""
        existing_ids = set(
            Notification.objects.filter(id__in=value).values_list('id', flat=True)
        )
        missing_ids = set(value) - existing_ids
        
        if missing_ids:
            raise serializers.ValidationError(
                f"Notifications not found: {list(missing_ids)}"
            )
        
        return value
