import React, { useState, useEffect } from "react";
import {
  Bell,
  Settings,
  Send,
  Users,
  Store,
  Truck,
  AlertCircle,
  CheckCircle,
  Clock,
  Filter,
  Search,
  Plus,
  Edit3,
  Trash2,
  Eye,
  Volume2,
  VolumeX,
  Mail,
  MessageSquare,
  Smartphone,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";
import UserSelector from "../../components/UserSelector";
import RoleSelector from "../../components/RoleSelector";
import NotificationTypeSelector from "../../components/NotificationTypeSelector";
import TemplateManager from "../../components/TemplateManager";
import RealTimeStatsDashboard from "../../components/RealTimeStatsDashboard";
import RealTimeNotificationStatus from "../../components/RealTimeNotificationStatus";
import DynamicSettingsManager from "../../components/DynamicSettingsManager";
import DynamicNotificationFilter from "../../components/DynamicNotificationFilter";
import DynamicScheduler from "../../components/DynamicScheduler";
import websocketService from "../../services/websocketService";
import { adminApi } from "../../services/adminApi";

const Notifications = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState("all");
  const [stats, setStats] = useState({});
  const [error, setError] = useState(null);
  const [sending, setSending] = useState(false);

  // Form state for sending notifications
  const [formData, setFormData] = useState({
    title: "",
    message: "",
    notification_type: "general",
    recipient_type: "all",
    recipient_users: [],
    recipient_roles: [],
    recipient_emails: [],
    channels: ["email"],
    priority: 1,
    schedule: {
      send_immediately: true,
      scheduled_date: "",
      scheduled_time: "",
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      is_recurring: false,
      recurring_type: "daily",
      recurring_interval: 1,
      recurring_end_date: "",
      recurring_days: [],
      recurring_day_of_month: 1,
    },
  });

  // Dynamic data state
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [customEmails, setCustomEmails] = useState("");
  const [currentFilters, setCurrentFilters] = useState({});

  const tabs = [
    { id: "overview", label: "Overview", icon: <Bell size={16} /> },
    { id: "send", label: "Send Notification", icon: <Send size={16} /> },
    { id: "templates", label: "Templates", icon: <MessageSquare size={16} /> },
    { id: "settings", label: "Settings", icon: <Settings size={16} /> },
  ];

  const notificationTypes = [
    { id: "all", label: "All Notifications", count: 45 },
    { id: "order", label: "Order Updates", count: 23 },
    { id: "user", label: "User Notifications", count: 12 },
    { id: "restaurant", label: "Restaurant Alerts", count: 8 },
    { id: "system", label: "System Alerts", count: 2 },
  ];

  const mockNotifications = [
    {
      id: 1,
      title: "New Order Received",
      message: "Order #1234 has been placed by John Doe",
      type: "order",
      recipients: "Restaurant: Afghan Delights",
      status: "delivered",
      timestamp: "2024-01-15 14:30",
      channel: ["push", "email"],
    },
    {
      id: 2,
      title: "Payment Processed",
      message: "Payment of $45.99 has been successfully processed",
      type: "user",
      recipients: "Customer: John Doe",
      status: "delivered",
      timestamp: "2024-01-15 14:25",
      channel: ["push", "sms"],
    },
    {
      id: 3,
      title: "Restaurant Verification",
      message: "New restaurant application requires approval",
      type: "restaurant",
      recipients: "Admin Team",
      status: "pending",
      timestamp: "2024-01-15 13:45",
      channel: ["email"],
    },
  ];

  useEffect(() => {
    loadNotifications();
    loadStats();

    // Initialize WebSocket connection for real-time updates
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    if (user.id && user.role === "admin") {
      websocketService.connect(user.id, user.role);
      websocketService.subscribeToNotifications();
    }

    // Cleanup on unmount
    return () => {
      websocketService.unsubscribeFromNotifications();
    };
  }, [selectedType]);

  const loadNotifications = async (filters = {}) => {
    setLoading(true);
    setError(null);

    try {
      // Merge current filters with new filters
      const activeFilters = { ...currentFilters, ...filters };

      const result = await adminApi.getNotifications({
        type: activeFilters.type || selectedType,
        search: activeFilters.search || searchTerm,
        status: activeFilters.status,
        channel: activeFilters.channel,
        priority: activeFilters.priority,
        date_from: activeFilters.date_from,
        date_to: activeFilters.date_to,
        recipient_type: activeFilters.recipient_type,
      });

      if (result.success) {
        setNotifications(result.data.notifications || []);
      } else {
        setError(result.error?.message || "Failed to load notifications");
        // Fallback to mock data
        setNotifications(mockNotifications);
      }
    } catch (error) {
      console.error("Error loading notifications:", error);
      setError("Error loading notifications");
      // Fallback to mock data
      setNotifications(mockNotifications);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const result = await adminApi.getNotificationStats();

      if (result.success) {
        setStats(result.data);
      } else {
        // Fallback to mock stats
        setStats({
          sent_today: 127,
          delivery_rate: 98.5,
          pending: 8,
          failed: 3,
        });
      }
    } catch (error) {
      console.error("Error loading stats:", error);
      // Fallback to mock stats
      setStats({
        sent_today: 127,
        delivery_rate: 98.5,
        pending: 8,
        failed: 3,
      });
    }
  };

  const filteredNotifications = notifications.filter((notification) => {
    const matchesSearch = notification.title
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesType =
      selectedType === "all" || notification.type === selectedType;
    return matchesSearch && matchesType;
  });

  const sendNotification = async (notificationData) => {
    setSending(true);
    setError(null);

    try {
      const result = await adminApi.sendNotification(notificationData);

      if (result.success) {
        // Refresh notifications and stats
        loadNotifications();
        loadStats();
        // Reset form
        setFormData({
          title: "",
          message: "",
          notification_type: "general",
          recipient_type: "all",
          recipient_users: [],
          recipient_roles: [],
          recipient_emails: [],
          channels: ["email"],
          priority: 1,
          schedule: {
            send_immediately: true,
            scheduled_date: "",
            scheduled_time: "",
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            is_recurring: false,
            recurring_type: "daily",
            recurring_interval: 1,
            recurring_end_date: "",
            recurring_days: [],
            recurring_day_of_month: 1,
          },
        });
        // Reset dynamic state
        setSelectedUsers([]);
        setSelectedRoles([]);
        setCustomEmails("");
        return { success: true };
      } else {
        setError(result.error?.message || "Failed to send notification");
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("Error sending notification:", error);
      setError("Error sending notification");
      return { success: false, error: error.message };
    } finally {
      setSending(false);
    }
  };

  const handleFormChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Reset dependent fields when recipient type changes
    if (field === "recipient_type") {
      setSelectedUsers([]);
      setSelectedRoles([]);
      setCustomEmails("");
      setFormData((prev) => ({
        ...prev,
        recipient_users: [],
        recipient_roles: [],
        recipient_emails: [],
      }));
    }
  };

  // Handle dynamic user selection
  const handleUsersChange = (users) => {
    setSelectedUsers(users);
    setFormData((prev) => ({
      ...prev,
      recipient_users: users.map((u) => u.id),
    }));
  };

  // Handle dynamic role selection
  const handleRolesChange = (roles) => {
    setSelectedRoles(roles);
    setFormData((prev) => ({
      ...prev,
      recipient_roles: roles,
    }));
  };

  // Handle custom emails
  const handleCustomEmailsChange = (emails) => {
    setCustomEmails(emails);
    const emailArray = emails
      .split(",")
      .map((email) => email.trim())
      .filter((email) => email);
    setFormData((prev) => ({
      ...prev,
      recipient_emails: emailArray,
    }));
  };

  // Handle filter changes
  const handleFilterChange = (filters) => {
    setCurrentFilters(filters);
    loadNotifications(filters);
  };

  // Handle schedule changes
  const handleScheduleChange = (schedule) => {
    setFormData((prev) => ({
      ...prev,
      schedule,
    }));
  };

  const renderOverview = () => (
    <div className='space-y-6'>
      {/* Real-time Stats Dashboard */}
      <RealTimeStatsDashboard initialStats={stats} />

      {/* Dynamic Filters */}
      <DynamicNotificationFilter
        onFilterChange={handleFilterChange}
        initialFilters={currentFilters}
        showSavedFilters={true}
      />

      {/* Quick Actions */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card>
          <div className='p-6'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-blue-100 rounded-lg'>
                <Send size={20} className='text-blue-600' />
              </div>
              <div>
                <p className='text-sm text-gray-600'>Sent Today</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.sent_today || 0}
                </p>
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <div className='p-6'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-green-100 rounded-lg'>
                <CheckCircle size={20} className='text-green-600' />
              </div>
              <div>
                <p className='text-sm text-gray-600'>Delivered</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.delivery_rate || 0}%
                </p>
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <div className='p-6'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-yellow-100 rounded-lg'>
                <Clock size={20} className='text-yellow-600' />
              </div>
              <div>
                <p className='text-sm text-gray-600'>Pending</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.pending || 0}
                </p>
              </div>
            </div>
          </div>
        </Card>
        <Card>
          <div className='p-6'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-red-100 rounded-lg'>
                <AlertCircle size={20} className='text-red-600' />
              </div>
              <div>
                <p className='text-sm text-gray-600'>Failed</p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.failed || 0}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0'>
        <div className='flex items-center space-x-4'>
          <div className='relative'>
            <Search
              size={16}
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
            />
            <Input
              placeholder='Search notifications...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-10 w-64'
            />
          </div>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
          >
            {notificationTypes.map((type) => (
              <option key={type.id} value={type.id}>
                {type.label} ({type.count})
              </option>
            ))}
          </select>
        </div>
        <Button
          variant='primary'
          icon={<Plus size={16} />}
          onClick={() => setActiveTab("send")}
        >
          New Notification
        </Button>
      </div>

      {/* Notifications List */}
      <Card>
        <div className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Recent Notifications</h3>
          <div className='space-y-3'>
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className='flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50'
              >
                <div className='flex items-center space-x-4'>
                  <div className='p-2 bg-primary-100 rounded-lg'>
                    <Bell size={16} className='text-primary-600' />
                  </div>
                  <div>
                    <h4 className='font-medium text-gray-900'>
                      {notification.title}
                    </h4>
                    <p className='text-sm text-gray-600'>
                      {notification.message}
                    </p>
                    <div className='flex items-center space-x-4 mt-1'>
                      <span className='text-xs text-gray-500'>
                        To: {notification.recipients}
                      </span>
                      <span className='text-xs text-gray-500'>
                        {notification.timestamp}
                      </span>
                      <div className='flex items-center space-x-1'>
                        {notification.channel.map((channel) => (
                          <Badge key={channel} variant='secondary' size='small'>
                            {channel}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  <Badge
                    variant={
                      notification.status === "delivered"
                        ? "success"
                        : notification.status === "pending"
                        ? "warning"
                        : "danger"
                    }
                    size='small'
                  >
                    {notification.status}
                  </Badge>
                  <Button
                    size='small'
                    variant='outline'
                    icon={<Eye size={14} />}
                  >
                    View
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );

  const renderSendNotification = () => (
    <Card>
      <div className='p-6'>
        <h3 className='text-lg font-semibold mb-6'>Send New Notification</h3>

        {error && (
          <div className='mb-4 p-4 bg-red-50 border border-red-200 rounded-lg'>
            <p className='text-red-600 text-sm'>{error}</p>
          </div>
        )}

        <div className='space-y-6'>
          {/* Dynamic Notification Type Selection */}
          <div>
            <NotificationTypeSelector
              selectedType={formData.notification_type}
              onTypeChange={(type) =>
                handleFormChange("notification_type", type)
              }
              showCounts={true}
            />
          </div>

          {/* Recipient Type Selection */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-4'>
              Select Recipients
            </label>
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3'>
              {[
                {
                  value: "all",
                  label: "All Users",
                  icon: "👥",
                  description: "Send to all verified users",
                },
                {
                  value: "role",
                  label: "By Role",
                  icon: "🎭",
                  description: "Send to specific user roles",
                },
                {
                  value: "user",
                  label: "Specific Users",
                  icon: "👤",
                  description: "Send to selected users",
                },
                {
                  value: "custom",
                  label: "Custom Emails",
                  icon: "📧",
                  description: "Send to email addresses",
                },
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() =>
                    handleFormChange("recipient_type", option.value)
                  }
                  className={`p-4 border-2 rounded-lg text-left transition-all ${
                    formData.recipient_type === option.value
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <div className='text-2xl mb-2'>{option.icon}</div>
                  <div className='font-medium text-sm mb-1'>{option.label}</div>
                  <div className='text-xs text-gray-500'>
                    {option.description}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Dynamic Recipient Selection Components */}
          {formData.recipient_type === "role" && (
            <div>
              <RoleSelector
                selectedRoles={selectedRoles}
                onRolesChange={handleRolesChange}
                allowMultiple={true}
                showCounts={true}
              />
            </div>
          )}

          {formData.recipient_type === "user" && (
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-4'>
                Select Specific Users
              </label>
              <UserSelector
                selectedUsers={selectedUsers}
                onUsersChange={handleUsersChange}
                placeholder='Search users by name, email, or username...'
                maxSelections={null}
              />
            </div>
          )}

          {formData.recipient_type === "custom" && (
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Custom Email Addresses
              </label>
              <textarea
                value={customEmails}
                onChange={(e) => handleCustomEmailsChange(e.target.value)}
                rows={4}
                placeholder='Enter email addresses separated by commas&#10;Example: <EMAIL>, <EMAIL>, <EMAIL>'
                className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none'
              />
              <div className='mt-2 text-sm text-gray-500'>
                {formData.recipient_emails.length > 0 && (
                  <span>
                    ✅ {formData.recipient_emails.length} email addresses ready
                  </span>
                )}
              </div>
            </div>
          )}

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Title
            </label>
            <Input
              value={formData.title}
              onChange={(e) => handleFormChange("title", e.target.value)}
              placeholder='Enter notification title...'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Message
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => handleFormChange("message", e.target.value)}
              rows={4}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              placeholder='Enter your message...'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Delivery Channels
            </label>
            <div className='flex items-center space-x-6'>
              <label className='flex items-center'>
                <input
                  type='checkbox'
                  checked={formData.channels.includes("email")}
                  onChange={(e) => {
                    const channels = e.target.checked
                      ? [...formData.channels, "email"]
                      : formData.channels.filter((c) => c !== "email");
                    handleFormChange("channels", channels);
                  }}
                  className='mr-2'
                />
                <Mail size={16} className='mr-1' />
                Email
              </label>
              <label className='flex items-center'>
                <input
                  type='checkbox'
                  checked={formData.channels.includes("sms")}
                  onChange={(e) => {
                    const channels = e.target.checked
                      ? [...formData.channels, "sms"]
                      : formData.channels.filter((c) => c !== "sms");
                    handleFormChange("channels", channels);
                  }}
                  className='mr-2'
                />
                <MessageSquare size={16} className='mr-1' />
                SMS
              </label>
              <label className='flex items-center'>
                <input
                  type='checkbox'
                  checked={formData.channels.includes("push")}
                  onChange={(e) => {
                    const channels = e.target.checked
                      ? [...formData.channels, "push"]
                      : formData.channels.filter((c) => c !== "push");
                    handleFormChange("channels", channels);
                  }}
                  className='mr-2'
                />
                <Smartphone size={16} className='mr-1' />
                Push Notification
              </label>
            </div>
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Priority
            </label>
            <select
              value={formData.priority}
              onChange={(e) =>
                handleFormChange("priority", parseInt(e.target.value))
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            >
              <option value={1}>Low</option>
              <option value={2}>Medium</option>
              <option value={3}>High</option>
              <option value={4}>Urgent</option>
            </select>
          </div>

          {/* Dynamic Scheduler */}
          <DynamicScheduler
            onScheduleChange={handleScheduleChange}
            initialSchedule={formData.schedule}
            showRecurring={true}
          />

          <div className='flex items-center space-x-4'>
            <Button
              variant='primary'
              icon={<Send size={16} />}
              disabled={sending || !formData.title || !formData.message}
              onClick={async (e) => {
                e.preventDefault();

                // Validate form
                if (!formData.title || !formData.message) {
                  setError("Please fill in title and message");
                  return;
                }

                if (formData.channels.length === 0) {
                  setError("Please select at least one delivery channel");
                  return;
                }

                const result = await sendNotification(formData);
                if (result.success) {
                  alert("Notification sent successfully!");
                  // Form will be reset by sendNotification function
                }
              }}
            >
              {sending ? "Sending..." : "Send Notification"}
            </Button>
            <Button variant='outline'>Save as Draft</Button>
            <Button variant='outline'>Preview</Button>
          </div>
        </div>
      </div>
    </Card>
  );

  const renderSettings = () => <DynamicSettingsManager />;

  return (
    <div className='p-6 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Notifications</h1>
          <p className='text-gray-600 mt-1'>
            Manage and send notifications to users
          </p>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
          <div className='flex items-center'>
            <AlertCircle size={20} className='text-red-600 mr-3' />
            <div>
              <h4 className='font-medium text-red-800'>Error</h4>
              <p className='text-sm text-red-700'>{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className='border-b border-gray-200'>
        <nav className='-mb-px flex space-x-8'>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? "border-primary-500 text-primary-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "overview" && renderOverview()}
      {activeTab === "send" && renderSendNotification()}
      {activeTab === "settings" && renderSettings()}
      {activeTab === "templates" && <TemplateManager />}
    </div>
  );
};

export default Notifications;
