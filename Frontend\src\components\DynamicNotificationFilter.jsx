import React, { useState, useEffect } from 'react';
import { Filter, Search, X, Calendar, Users, Bell, Save, Trash2, Star } from 'lucide-react';
import Button from './common/Button';
import Input from './common/Input';

const DynamicNotificationFilter = ({ 
  onFilterChange, 
  initialFilters = {},
  showSavedFilters = true 
}) => {
  const [filters, setFilters] = useState({
    search: '',
    type: 'all',
    status: 'all',
    channel: 'all',
    priority: 'all',
    date_from: '',
    date_to: '',
    recipient_type: 'all',
    ...initialFilters
  });

  const [isExpanded, setIsExpanded] = useState(false);
  const [savedFilters, setSavedFilters] = useState([]);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [filterName, setFilterName] = useState('');
  const [notificationTypes, setNotificationTypes] = useState([]);
  const [userRoles, setUserRoles] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDynamicData();
    loadSavedFilters();
  }, []);

  useEffect(() => {
    // Debounce filter changes
    const debounceTimer = setTimeout(() => {
      onFilterChange(filters);
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [filters, onFilterChange]);

  const loadDynamicData = async () => {
    try {
      setLoading(true);

      // Load notification types
      const typesResponse = await fetch('/api/admin/notifications/types/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (typesResponse.ok) {
        const typesData = await typesResponse.json();
        setNotificationTypes(typesData.data?.types || []);
      }

      // Load user roles
      const rolesResponse = await fetch('/api/admin/notifications/roles/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json();
        setUserRoles(rolesData.data?.roles || []);
      }

    } catch (error) {
      console.error('Error loading dynamic filter data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSavedFilters = () => {
    try {
      const saved = localStorage.getItem('notification_saved_filters');
      if (saved) {
        setSavedFilters(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading saved filters:', error);
    }
  };

  const saveCurrentFilter = () => {
    if (!filterName.trim()) return;

    const newFilter = {
      id: Date.now().toString(),
      name: filterName.trim(),
      filters: { ...filters },
      created_at: new Date().toISOString()
    };

    const updatedFilters = [...savedFilters, newFilter];
    setSavedFilters(updatedFilters);
    localStorage.setItem('notification_saved_filters', JSON.stringify(updatedFilters));
    
    setShowSaveModal(false);
    setFilterName('');
  };

  const applySavedFilter = (savedFilter) => {
    setFilters(savedFilter.filters);
  };

  const deleteSavedFilter = (filterId) => {
    const updatedFilters = savedFilters.filter(f => f.id !== filterId);
    setSavedFilters(updatedFilters);
    localStorage.setItem('notification_saved_filters', JSON.stringify(updatedFilters));
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      search: '',
      type: 'all',
      status: 'all',
      channel: 'all',
      priority: 'all',
      date_from: '',
      date_to: '',
      recipient_type: 'all'
    };
    setFilters(clearedFilters);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const getActiveFilterCount = () => {
    return Object.entries(filters).filter(([key, value]) => {
      if (key === 'search') return value.trim() !== '';
      return value !== 'all' && value !== '';
    }).length;
  };

  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'draft', label: 'Draft' },
    { value: 'sending', label: 'Sending' },
    { value: 'sent', label: 'Sent' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'failed', label: 'Failed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const channelOptions = [
    { value: 'all', label: 'All Channels' },
    { value: 'email', label: 'Email' },
    { value: 'sms', label: 'SMS' },
    { value: 'push', label: 'Push Notification' },
    { value: 'in_app', label: 'In-App' }
  ];

  const priorityOptions = [
    { value: 'all', label: 'All Priorities' },
    { value: '1', label: 'Low Priority' },
    { value: '2', label: 'Medium Priority' },
    { value: '3', label: 'High Priority' },
    { value: '4', label: 'Urgent Priority' }
  ];

  const recipientTypeOptions = [
    { value: 'all', label: 'All Recipients' },
    { value: 'user', label: 'Specific Users' },
    { value: 'role', label: 'By Role' },
    { value: 'custom', label: 'Custom Emails' }
  ];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Filter size={20} className="text-gray-600" />
          <h3 className="font-semibold text-gray-900">Filters</h3>
          {getActiveFilterCount() > 0 && (
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              {getActiveFilterCount()} active
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {getActiveFilterCount() > 0 && (
            <Button
              size="small"
              variant="outline"
              icon={<X size={14} />}
              onClick={clearAllFilters}
            >
              Clear
            </Button>
          )}
          <Button
            size="small"
            variant="outline"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Less' : 'More'} Filters
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <Input
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
          placeholder="Search notifications by title, message, or recipient..."
          className="pl-10"
        />
      </div>

      {/* Basic Filters */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Notification Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Type
          </label>
          <select
            value={filters.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          >
            <option value="all">All Types</option>
            {notificationTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label} ({type.count})
              </option>
            ))}
          </select>
        </div>

        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Channel */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Channel
          </label>
          <select
            value={filters.channel}
            onChange={(e) => handleFilterChange('channel', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          >
            {channelOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Priority */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Priority
          </label>
          <select
            value={filters.priority}
            onChange={(e) => handleFilterChange('priority', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          >
            {priorityOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="space-y-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                value={filters.date_from}
                onChange={(e) => handleFilterChange('date_from', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                value={filters.date_to}
                onChange={(e) => handleFilterChange('date_to', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>

            {/* Recipient Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Recipient Type
              </label>
              <select
                value={filters.recipient_type}
                onChange={(e) => handleFilterChange('recipient_type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              >
                {recipientTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Save Filter */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center gap-2">
              <Button
                size="small"
                variant="outline"
                icon={<Save size={14} />}
                onClick={() => setShowSaveModal(true)}
                disabled={getActiveFilterCount() === 0}
              >
                Save Filter
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Saved Filters */}
      {showSavedFilters && savedFilters.length > 0 && (
        <div className="pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2 mb-3">
            <Star size={16} className="text-yellow-500" />
            <span className="text-sm font-medium text-gray-700">Saved Filters</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {savedFilters.map(savedFilter => (
              <div
                key={savedFilter.id}
                className="flex items-center gap-2 bg-gray-100 rounded-lg px-3 py-1"
              >
                <button
                  onClick={() => applySavedFilter(savedFilter)}
                  className="text-sm text-gray-700 hover:text-gray-900"
                >
                  {savedFilter.name}
                </button>
                <button
                  onClick={() => deleteSavedFilter(savedFilter.id)}
                  className="text-gray-400 hover:text-red-600"
                >
                  <X size={12} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Save Filter Modal */}
      {showSaveModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Save Filter</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filter Name
                </label>
                <Input
                  value={filterName}
                  onChange={(e) => setFilterName(e.target.value)}
                  placeholder="Enter filter name..."
                  autoFocus
                />
              </div>
              <div className="flex items-center justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowSaveModal(false);
                    setFilterName('');
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={saveCurrentFilter}
                  disabled={!filterName.trim()}
                >
                  Save Filter
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DynamicNotificationFilter;
