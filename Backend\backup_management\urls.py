from django.urls import path
from . import views

app_name = 'backup_management'

urlpatterns = [
    # Backup Jobs
    path('backups/', views.BackupJobListCreateView.as_view(), name='backup_list_create'),
    path('backups/create/', views.BackupJobListCreateView.as_view(), name='backup_create'),
    path('backups/<uuid:job_id>/', views.BackupJobDetailView.as_view(), name='backup_detail'),
    path('backups/<uuid:job_id>/status/', views.BackupJobStatusView.as_view(), name='backup_status'),
    path('backups/<uuid:job_id>/download/', views.BackupJobDownloadView.as_view(), name='backup_download'),
    
    # Backup Statistics
    path('stats/', views.BackupStatsView.as_view(), name='backup_stats'),
    
    # Backup Settings
    path('settings/', views.BackupSettingsView.as_view(), name='backup_settings'),
    
    # Backup Schedules
    path('schedules/', views.BackupScheduleListCreateView.as_view(), name='schedule_list_create'),
    path('schedules/<int:schedule_id>/', views.BackupScheduleDetailView.as_view(), name='schedule_detail'),
]
