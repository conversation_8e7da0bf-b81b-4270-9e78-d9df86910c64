import axios from "axios";

const API_BASE_URL = "http://127.0.0.1:8000/api";

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  // Try to get token from the main auth system first
  const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
  const token = user.access_token || localStorage.getItem("access_token");

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token refresh on 401
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Try to refresh token
      const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
      const refreshToken =
        user.refresh_token || localStorage.getItem("refresh_token");

      if (refreshToken) {
        try {
          const response = await axios.post(
            "http://127.0.0.1:8000/api/auth/token/refresh/",
            {
              refresh: refreshToken,
            }
          );
          const newToken = response.data.access;

          // Update both storage methods
          localStorage.setItem("access_token", newToken);
          const updatedUser = { ...user, access_token: newToken };
          localStorage.setItem("afghanSofraUser", JSON.stringify(updatedUser));

          // Retry original request
          error.config.headers.Authorization = `Bearer ${newToken}`;
          return apiClient.request(error.config);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem("access_token");
          localStorage.removeItem("refresh_token");
          localStorage.removeItem("afghanSofraUser");
          window.location.href = "/login";
        }
      } else {
        // No refresh token, redirect to login
        localStorage.removeItem("afghanSofraUser");
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

export const adminApi = {
  // Dashboard Statistics
  async getDashboardStats() {
    try {
      const response = await apiClient.get("/admin/dashboard-stats/");
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Employee Management (New System)
  async getDeliveryEmployees(filters = {}) {
    try {
      const params = new URLSearchParams();
      if (filters.employment_status && filters.employment_status !== "all") {
        params.append("employment_status", filters.employment_status);
      }
      if (filters.availability && filters.availability !== "all") {
        params.append("availability", filters.availability);
      }

      const response = await apiClient.get(
        `/delivery-agent/admin/employees/?${params}`
      );
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async createDeliveryEmployee(employeeData) {
    try {
      const response = await apiClient.post(
        "/delivery-agent/admin/employees/create/",
        employeeData
      );

      // Handle different response formats
      if (response.data.status === "success") {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message,
        };
      } else {
        return {
          success: false,
          error: { message: response.data.message || "Unknown error" },
        };
      }
    } catch (error) {
      console.error("API Error:", error);
      return {
        success: false,
        error: {
          message:
            error.response?.data?.message || error.message || "Network error",
        },
      };
    }
  },

  async deleteDeliveryEmployee(agentId) {
    try {
      const response = await apiClient.delete(
        `/delivery-agent/admin/employees/${agentId}/`
      );

      if (response.data.status === "success") {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message,
        };
      } else {
        return {
          success: false,
          error: { message: response.data.message || "Unknown error" },
        };
      }
    } catch (error) {
      console.error("Delete Employee API Error:", error);
      return {
        success: false,
        error: {
          message:
            error.response?.data?.message || error.message || "Network error",
        },
      };
    }
  },

  async getDeliveryEmployeeDetails(agentId) {
    try {
      const response = await apiClient.get(
        `/delivery-agent/admin/employees/${agentId}/`
      );

      if (response.data.status === "success") {
        return {
          success: true,
          data: response.data.data,
        };
      } else {
        return {
          success: false,
          error: { message: response.data.message || "Unknown error" },
        };
      }
    } catch (error) {
      console.error("Get Employee Details API Error:", error);
      return {
        success: false,
        error: {
          message:
            error.response?.data?.message || error.message || "Network error",
        },
      };
    }
  },

  async updateDeliveryEmployee(agentId, employeeData) {
    try {
      const response = await apiClient.put(
        `/delivery-agent/admin/employees/${agentId}/`,
        employeeData
      );

      if (response.data.status === "success") {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message,
        };
      } else {
        return {
          success: false,
          error: { message: response.data.message || "Unknown error" },
        };
      }
    } catch (error) {
      console.error("Update Employee API Error:", error);
      return {
        success: false,
        error: {
          message:
            error.response?.data?.message || error.message || "Network error",
        },
      };
    }
  },

  async getDeliveryEmployeeDetail(agentId) {
    try {
      const response = await apiClient.get(
        `/delivery-agent/admin/employees/${agentId}/`
      );
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Manual Order Assignment (New System)
  async getOrderAssignmentData() {
    try {
      // Use the manual assignment API that includes proper filtering
      const response = await apiClient.get(
        "/delivery-agent/admin/assignments/"
      );

      if (response.data?.status === "success") {
        return {
          success: true,
          data: response.data.data,
        };
      } else {
        return {
          success: false,
          error: response.data?.message || "Failed to load assignment data",
        };
      }
    } catch (error) {
      console.error("Error fetching assignment data:", error);
      console.error("Error response:", error.response);
      console.error("Error status:", error.response?.status);
      console.error("Error data:", error.response?.data);
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async assignOrderToAgent(assignmentData) {
    try {
      const response = await apiClient.post(
        "/delivery-agent/admin/assignments/",
        assignmentData
      );
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async reassignOrder(reassignmentData) {
    try {
      const response = await apiClient.post(
        "/delivery-agent/admin/assignments/reassign/",
        reassignmentData
      );
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getActiveDeliveries() {
    try {
      const response = await apiClient.get(
        "/delivery-agent/admin/active-deliveries/"
      );
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // LEGACY: Delivery Agent Management (To be removed)
  async getDeliveryAgents() {
    try {
      const response = await apiClient.get("/delivery-agent/admin/agents/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getDeliveryAgentStats() {
    try {
      const response = await apiClient.get("/delivery-agent/admin/agents/");
      if (response.data && response.data.data) {
        const agents = response.data.data;
        const stats = {
          total: agents.length,
          pending: agents.filter((agent) => agent.status === "pending").length,
          active: agents.filter((agent) => agent.status === "active").length,
          suspended: agents.filter((agent) => agent.status === "suspended")
            .length,
          blocked: agents.filter((agent) => agent.status === "blocked").length,
        };
        return { success: true, data: stats };
      }
      return { success: false, error: "No data received" };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async verifyDeliveryAgent(agentId, action, notes = "") {
    try {
      const response = await apiClient.post(
        "/delivery-agent/admin/verify-agent/",
        {
          agent_id: agentId,
          action: action, // 'approve' or 'reject'
          notes: notes,
        }
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // System Settings
  async getSystemSettings() {
    try {
      const response = await apiClient.get("/config/settings/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async updateSystemSetting(settingId, value) {
    try {
      const response = await apiClient.patch(`/config/settings/${settingId}/`, {
        value: value,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async resetSystemSetting(settingId) {
    try {
      const response = await apiClient.post(
        `/config/settings/${settingId}/reset_to_default/`
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Reports & Analytics
  async generateReport(reportType, dateRange, filters = {}) {
    try {
      const response = await apiClient.post("/admin/reports/generate/", {
        report_type: reportType,
        date_range: dateRange,
        filters: filters,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getReportHistory() {
    try {
      const response = await apiClient.get("/admin/reports/history/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async downloadReport(reportId, format = "pdf") {
    try {
      const response = await apiClient.get(
        `/admin/reports/${reportId}/download/`,
        {
          params: { format },
          responseType: "blob",
        }
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Notifications
  async getNotifications(filters = {}) {
    try {
      const params = new URLSearchParams();
      if (filters.type && filters.type !== "all") {
        params.append("type", filters.type);
      }
      if (filters.status && filters.status !== "all") {
        params.append("status", filters.status);
      }
      if (filters.date_range) {
        params.append("date_range", filters.date_range);
      }

      const response = await apiClient.get(`/admin/notifications/?${params}`);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async sendNotification(notificationData) {
    try {
      const response = await apiClient.post(
        "/admin/notifications/send/",
        notificationData
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getNotificationStats() {
    try {
      const response = await apiClient.get("/admin/notifications/stats/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async updateNotificationSettings(settings) {
    try {
      const response = await apiClient.patch(
        "/admin/notifications/settings/",
        settings
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Dynamic data endpoints
  async searchUsers(query, roleFilter = null, limit = 20) {
    try {
      const params = new URLSearchParams({
        q: query,
        limit: limit.toString(),
      });

      if (roleFilter) {
        params.append("role", roleFilter);
      }

      const response = await apiClient.get(
        `/admin/notifications/users/search/?${params}`
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getUserRoles() {
    try {
      const response = await apiClient.get("/admin/notifications/roles/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getNotificationTypes() {
    try {
      const response = await apiClient.get("/admin/notifications/types/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Template management
  async createTemplate(templateData) {
    try {
      const response = await apiClient.post(
        "/admin/notifications/templates/create/",
        templateData
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async updateTemplate(templateId, templateData) {
    try {
      const response = await apiClient.patch(
        `/admin/notifications/templates/${templateId}/update/`,
        templateData
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async deleteTemplate(templateId) {
    try {
      const response = await apiClient.delete(
        `/admin/notifications/templates/${templateId}/delete/`
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // System Logs
  async getSystemLogs(filters = {}) {
    try {
      const params = new URLSearchParams();
      if (filters.level && filters.level !== "all") {
        params.append("level", filters.level);
      }
      if (filters.category && filters.category !== "all") {
        params.append("category", filters.category);
      }
      if (filters.date_range) {
        params.append("date_range", filters.date_range);
      }
      if (filters.search) {
        params.append("search", filters.search);
      }

      const response = await apiClient.get(`/admin/logs/?${params}`);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getLogStats() {
    try {
      const response = await apiClient.get("/admin/logs/stats/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async exportLogs(filters = {}, format = "csv") {
    try {
      const params = new URLSearchParams();
      Object.keys(filters).forEach((key) => {
        if (filters[key] && filters[key] !== "all") {
          params.append(key, filters[key]);
        }
      });
      params.append("format", format);

      const response = await apiClient.get(`/admin/logs/export/?${params}`, {
        responseType: "blob",
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async clearOldLogs(daysOld = 30) {
    try {
      const response = await apiClient.delete(`/admin/logs/clear/`, {
        data: { days_old: daysOld },
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Backup & Restore
  async getBackups() {
    try {
      const response = await apiClient.get("/admin/backup/backups/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async createBackup(backupType, options = {}) {
    try {
      const response = await apiClient.post("/admin/backup/backups/", {
        name: `${
          backupType.charAt(0).toUpperCase() + backupType.slice(1)
        } Backup - ${new Date().toLocaleString()}`,
        backup_type: backupType,
        options: options,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getBackupStatus(backupId) {
    try {
      const response = await apiClient.get(
        `/admin/backup/backups/${backupId}/status/`
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async downloadBackup(backupId) {
    try {
      const response = await apiClient.get(
        `/admin/backup/backups/${backupId}/download/`,
        {
          responseType: "blob",
        }
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async restoreBackup(backupId, options = {}) {
    try {
      const response = await apiClient.post(
        `/admin/backup/backups/${backupId}/restore/`,
        {
          options: options,
        }
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async deleteBackup(backupId) {
    try {
      const response = await apiClient.delete(
        `/admin/backup/backups/${backupId}/`
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getBackupSettings() {
    try {
      const response = await apiClient.get("/admin/backup/settings/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async updateBackupSettings(settings) {
    try {
      const response = await apiClient.put("/admin/backup/settings/", settings);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getBackupStats() {
    try {
      const response = await apiClient.get("/admin/backup/stats/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Restaurant Management (existing)
  async getRestaurants() {
    try {
      const response = await apiClient.get("/restaurants/admin/restaurants/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async approveRestaurant(restaurantId) {
    try {
      const response = await apiClient.post("/restaurants/admin/approve/", {
        restaurant_id: restaurantId,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async rejectRestaurant(restaurantId, reason = "") {
    try {
      const response = await apiClient.post("/restaurants/admin/reject/", {
        restaurant_id: restaurantId,
        reason: reason,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },
};

export default adminApi;
