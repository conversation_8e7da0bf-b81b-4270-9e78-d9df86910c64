import React, { useState, useEffect } from 'react';
import { Bell, AlertTriangle, ShoppingCart, User, Building, Truck, CreditCard, Settings, Megaphone } from 'lucide-react';

const NotificationTypeSelector = ({ 
  selectedType, 
  onTypeChange, 
  showCounts = true 
}) => {
  const [types, setTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadNotificationTypes();
  }, []);

  const loadNotificationTypes = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/notifications/types/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTypes(data.data?.types || []);
      } else {
        setError('Failed to load notification types');
      }
    } catch (err) {
      setError('Error loading notification types');
      console.error('Notification types loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type) => {
    const icons = {
      order: ShoppingCart,
      user: User,
      restaurant: Building,
      delivery: Truck,
      payment: CreditCard,
      system: Settings,
      marketing: Megaphone,
      general: Bell
    };
    return icons[type] || Bell;
  };

  const getTypeColor = (type) => {
    const colors = {
      order: 'border-green-200 bg-green-50 text-green-700 hover:bg-green-100',
      user: 'border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100',
      restaurant: 'border-purple-200 bg-purple-50 text-purple-700 hover:bg-purple-100',
      delivery: 'border-yellow-200 bg-yellow-50 text-yellow-700 hover:bg-yellow-100',
      payment: 'border-emerald-200 bg-emerald-50 text-emerald-700 hover:bg-emerald-100',
      system: 'border-red-200 bg-red-50 text-red-700 hover:bg-red-100',
      marketing: 'border-pink-200 bg-pink-50 text-pink-700 hover:bg-pink-100',
      general: 'border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100'
    };
    return colors[type] || 'border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100';
  };

  const getSelectedTypeColor = (type) => {
    const colors = {
      order: 'border-green-500 bg-green-100 text-green-800 ring-2 ring-green-200',
      user: 'border-blue-500 bg-blue-100 text-blue-800 ring-2 ring-blue-200',
      restaurant: 'border-purple-500 bg-purple-100 text-purple-800 ring-2 ring-purple-200',
      delivery: 'border-yellow-500 bg-yellow-100 text-yellow-800 ring-2 ring-yellow-200',
      payment: 'border-emerald-500 bg-emerald-100 text-emerald-800 ring-2 ring-emerald-200',
      system: 'border-red-500 bg-red-100 text-red-800 ring-2 ring-red-200',
      marketing: 'border-pink-500 bg-pink-100 text-pink-800 ring-2 ring-pink-200',
      general: 'border-gray-500 bg-gray-100 text-gray-800 ring-2 ring-gray-200'
    };
    return colors[type] || 'border-gray-500 bg-gray-100 text-gray-800 ring-2 ring-gray-200';
  };

  const getTypeDescription = (type) => {
    const descriptions = {
      order: 'Order updates, confirmations, and status changes',
      user: 'User account notifications and personal messages',
      restaurant: 'Restaurant-related notifications and updates',
      delivery: 'Delivery assignments and status updates',
      payment: 'Payment confirmations and transaction alerts',
      system: 'System alerts, maintenance, and technical notifications',
      marketing: 'Promotional messages and marketing campaigns',
      general: 'General announcements and miscellaneous notifications'
    };
    return descriptions[type] || 'General notification type';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-6">
        <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        <span className="ml-3 text-gray-600">Loading notification types...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-600 text-sm">{error}</p>
        <button 
          onClick={loadNotificationTypes}
          className="mt-2 text-red-700 hover:text-red-800 text-sm underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
        <Bell size={16} />
        <span>Select Notification Type</span>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {types.map((type) => {
          const isSelected = selectedType === type.value;
          const Icon = getTypeIcon(type.value);
          const colorClass = isSelected ? getSelectedTypeColor(type.value) : getTypeColor(type.value);

          return (
            <button
              key={type.value}
              onClick={() => onTypeChange(type.value)}
              className={`relative p-4 border-2 rounded-lg transition-all duration-200 hover:shadow-md text-left ${colorClass}`}
            >
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <Icon size={24} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-base mb-1">
                    {type.label}
                  </div>
                  <div className="text-sm opacity-75 mb-2">
                    {getTypeDescription(type.value)}
                  </div>
                  {showCounts && (
                    <div className="text-xs opacity-60">
                      {type.count} notifications sent
                    </div>
                  )}
                </div>
              </div>

              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-6 bg-current rounded-full flex items-center justify-center">
                    <Bell size={12} className="text-white" />
                  </div>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Selected type summary */}
      {selectedType && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-3">
            {(() => {
              const Icon = getTypeIcon(selectedType);
              const selectedTypeData = types.find(t => t.value === selectedType);
              return (
                <>
                  <Icon size={20} className="text-blue-600" />
                  <div>
                    <div className="font-medium text-blue-800">
                      {selectedTypeData?.label || selectedType}
                    </div>
                    <div className="text-sm text-blue-600">
                      {getTypeDescription(selectedType)}
                    </div>
                    {showCounts && selectedTypeData && (
                      <div className="text-xs text-blue-500 mt-1">
                        {selectedTypeData.count} notifications of this type have been sent
                      </div>
                    )}
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationTypeSelector;
