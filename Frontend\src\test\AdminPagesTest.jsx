import React from "react";
import { Link } from "react-router-dom";
import Card from "../components/common/Card";
import Button from "../components/common/Button";
import { 
  FileText, 
  Bell, 
  Database, 
  Settings, 
  BarChart3,
  CheckCircle,
  ExternalLink 
} from "lucide-react";

const AdminPagesTest = () => {
  const adminPages = [
    {
      path: "/admin/reports",
      name: "Reports",
      description: "Generate comprehensive business reports and analytics",
      icon: <FileText size={24} />,
      color: "blue",
    },
    {
      path: "/admin/notifications",
      name: "Notifications",
      description: "Manage and send notifications to users",
      icon: <Bell size={24} />,
      color: "green",
    },
    {
      path: "/admin/backup",
      name: "Backup & Restore",
      description: "Manage system backups and data recovery",
      icon: <Database size={24} />,
      color: "purple",
    },
    {
      path: "/admin/logs",
      name: "System Logs",
      description: "Monitor and analyze system activity and errors",
      icon: <FileText size={24} />,
      color: "orange",
    },
    {
      path: "/admin/settings",
      name: "System Settings",
      description: "Configure system-wide settings and preferences",
      icon: <Settings size={24} />,
      color: "indigo",
    },
    {
      path: "/admin/analytics",
      name: "Analytics",
      description: "View detailed analytics and performance metrics",
      icon: <BarChart3 size={24} />,
      color: "emerald",
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Admin Pages Test
        </h1>
        <p className="text-gray-600">
          Test all admin pages to ensure they're working correctly
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adminPages.map((page) => (
          <Card key={page.path}>
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className={`p-3 bg-${page.color}-100 rounded-lg`}>
                  {page.icon}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {page.name}
                  </h3>
                </div>
              </div>
              
              <p className="text-gray-600 mb-4 text-sm">
                {page.description}
              </p>
              
              <div className="space-y-2">
                <Link to={page.path}>
                  <Button 
                    variant="primary" 
                    icon={<ExternalLink size={16} />}
                    className="w-full"
                  >
                    Test {page.name}
                  </Button>
                </Link>
                
                <div className="flex items-center justify-center space-x-2 text-sm text-green-600">
                  <CheckCircle size={16} />
                  <span>Component Created</span>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">Test Results</h3>
          <div className="space-y-3">
            {adminPages.map((page) => (
              <div 
                key={page.path}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  {page.icon}
                  <span className="font-medium">{page.name}</span>
                  <span className="text-sm text-gray-500">({page.path})</span>
                </div>
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle size={16} />
                  <span className="text-sm">Ready</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>

      <div className="text-center">
        <p className="text-sm text-gray-500">
          All admin pages have been created and routes have been configured.
          Click on any "Test" button above to navigate to the respective page.
        </p>
      </div>
    </div>
  );
};

export default AdminPagesTest;
