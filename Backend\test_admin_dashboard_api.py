#!/usr/bin/env python3
"""
Test script for Admin Dashboard API integration
"""

import requests
import json

def test_admin_dashboard_api():
    """Test the admin dashboard API endpoint"""
    
    print("🔍 Testing Admin Dashboard API Integration")
    print("=" * 60)
    
    # API endpoints
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    dashboard_url = "http://127.0.0.1:8000/api/admin/dashboard-stats/"
    
    # Admin credentials
    admin_credentials = {
        "user_name": "admin",
        "password": "admin123"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("1. Testing Admin Login...")
    try:
        login_response = requests.post(
            login_url,
            headers=headers,
            data=json.dumps(admin_credentials),
            timeout=10
        )
        
        print(f"   📡 Login Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            if login_data.get('success'):
                token = login_data['data']['access_token']
                user_info = login_data['data']['user']
                print(f"   ✅ Login successful!")
                print(f"   👤 User: {user_info['name']} ({user_info['role']})")
            else:
                print(f"   ❌ Login failed: {login_data.get('message')}")
                return False
        else:
            print(f"   ❌ Login failed with status {login_response.status_code}")
            print(f"   📄 Response: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Login exception: {e}")
        return False
    
    print("\n2. Testing Dashboard Stats API...")
    try:
        auth_headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        dashboard_response = requests.get(
            dashboard_url,
            headers=auth_headers,
            timeout=10
        )
        
        print(f"   📡 Dashboard API Status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            dashboard_data = dashboard_response.json()
            if dashboard_data.get('success'):
                stats = dashboard_data['data']
                print(f"   ✅ Dashboard API working!")
                print(f"   📊 Stats Summary:")
                print(f"      - Total Users: {stats['users']['total']}")
                print(f"      - Total Restaurants: {stats['restaurants']['total']}")
                print(f"      - Total Orders: {stats['orders']['total']}")
                print(f"      - Total Revenue: ${stats['revenue']['total']:,.2f}")
                print(f"      - Active Delivery Agents: {stats['delivery_agents']['active']}")
                print(f"      - System Status: {stats['system_status']['status']}")
                return True
            else:
                print(f"   ❌ Dashboard API failed: {dashboard_data.get('message')}")
                return False
        else:
            print(f"   ❌ Dashboard API failed with status {dashboard_response.status_code}")
            print(f"   📄 Response: {dashboard_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Dashboard API exception: {e}")
        return False

def test_frontend_integration():
    """Test if frontend can access the API"""
    
    print("\n3. Testing Frontend Integration...")
    
    # Test CORS and basic connectivity
    try:
        response = requests.options(
            "http://127.0.0.1:8000/api/admin/dashboard-stats/",
            headers={
                "Origin": "http://localhost:5173",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "authorization,content-type"
            },
            timeout=5
        )
        
        print(f"   📡 CORS Preflight Status: {response.status_code}")
        
        if response.status_code in [200, 204]:
            print(f"   ✅ CORS configured correctly!")
            cors_headers = response.headers
            print(f"   🔗 Access-Control-Allow-Origin: {cors_headers.get('Access-Control-Allow-Origin', 'Not set')}")
            print(f"   🔗 Access-Control-Allow-Methods: {cors_headers.get('Access-Control-Allow-Methods', 'Not set')}")
            return True
        else:
            print(f"   ❌ CORS issue detected")
            return False
            
    except Exception as e:
        print(f"   ❌ CORS test exception: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Admin Dashboard API Integration Test")
    print()
    
    # Test API functionality
    api_success = test_admin_dashboard_api()
    
    # Test frontend integration
    cors_success = test_frontend_integration()
    
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"   API Functionality: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"   Frontend Integration: {'✅ PASS' if cors_success else '❌ FAIL'}")
    
    if api_success and cors_success:
        print("\n🎉 All tests passed! Admin dashboard should work correctly.")
        print("   Frontend URL: http://localhost:5173/admin/dashboard")
        print("   Login with: admin / admin123")
    else:
        print("\n⚠️  Some tests failed. Check the issues above.")
    
    print("=" * 60)
