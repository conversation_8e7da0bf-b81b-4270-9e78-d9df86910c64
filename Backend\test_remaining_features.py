#!/usr/bin/env python3
"""
Test remaining notification features
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.utils import timezone
from datetime import timedelta
from notifications.models import Notification, NotificationTemplate, NotificationSettings

User = get_user_model()

def test_remaining_features():
    """Test remaining notification features"""
    print("🧪 Testing Remaining Notification Features...")
    print("=" * 60)
    
    # Setup authentication
    admin_user = User.objects.filter(role='admin').first()
    client = Client()
    
    login_response = client.post('/api/auth/login/', 
                                json.dumps({
                                    'user_name': admin_user.user_name,
                                    'password': 'admin123'
                                }),
                                content_type='application/json')
    
    access_token = login_response.json().get('data', {}).get('access_token')
    headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
    
    # Test 1: Bulk Operations
    print("\n📦 Testing Bulk Operations...")
    
    # Get some notifications
    response = client.get('/api/admin/notifications/', **headers)
    if response.status_code == 200:
        data = response.json()
        notifications = data.get('results', [])
        
        if len(notifications) >= 2:
            notification_ids = [notif['id'] for notif in notifications[:2]]
            
            # Test bulk cancel
            bulk_data = {
                'notification_ids': notification_ids,
                'action': 'cancel'
            }
            bulk_response = client.post('/api/admin/notifications/bulk-action/',
                                      json.dumps(bulk_data),
                                      content_type='application/json',
                                      **headers)
            
            print(f"   Bulk Cancel Status: {bulk_response.status_code}")
            if bulk_response.status_code == 200:
                print("   ✅ Bulk operations working")
            else:
                print(f"   ❌ Bulk operations failed: {bulk_response.content}")
        else:
            print("   ⚠️ Not enough notifications for bulk test")
    
    # Test 2: Validation Errors
    print("\n⚠️ Testing Validation Errors...")
    
    validation_tests = [
        {
            'name': 'Missing Title',
            'data': {
                'message': 'Test message',
                'notification_type': 'general',
                'recipient_type': 'all'
            }
        },
        {
            'name': 'Invalid Type',
            'data': {
                'title': 'Test',
                'message': 'Test message',
                'notification_type': 'invalid_type',
                'recipient_type': 'all'
            }
        }
    ]
    
    for test in validation_tests:
        response = client.post('/api/admin/notifications/send/',
                             json.dumps(test['data']),
                             content_type='application/json',
                             **headers)
        
        print(f"   {test['name']}: Status {response.status_code}")
        if response.status_code == 400:
            print("   ✅ Validation working correctly")
        else:
            print("   ❌ Validation not working")
    
    # Test 3: Database Integrity
    print("\n🗄️ Testing Database Integrity...")
    
    try:
        total_notifications = Notification.objects.count()
        print(f"   Total notifications: {total_notifications}")
        
        # Test by status
        for status, _ in Notification.STATUS_CHOICES:
            count = Notification.objects.filter(status=status).count()
            print(f"   {status}: {count}")
        
        # Test templates
        template_count = NotificationTemplate.objects.count()
        print(f"   Templates: {template_count}")
        
        # Test settings
        settings = NotificationSettings.objects.first()
        if settings:
            print(f"   Settings: Email enabled = {settings.email_enabled}")
        
        print("   ✅ Database integrity good")
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
    
    # Test 4: Template System
    print("\n📝 Testing Template System...")
    
    # Get templates
    template_response = client.get('/api/admin/notifications/templates/', **headers)
    print(f"   Get Templates Status: {template_response.status_code}")
    
    if template_response.status_code == 200:
        templates = template_response.json()
        template_count = len(templates) if isinstance(templates, list) else 0
        print(f"   ✅ Templates available: {template_count}")
    
    # Test 5: Settings Management
    print("\n⚙️ Testing Settings Management...")
    
    # Update settings
    settings_data = {
        "email_enabled": True,
        "email_from_name": "Afghan Sofra Test System",
        "max_emails_per_hour": 1000
    }
    
    settings_response = client.patch('/api/admin/notifications/settings/',
                                   json.dumps(settings_data),
                                   content_type='application/json',
                                   **headers)
    
    print(f"   Update Settings Status: {settings_response.status_code}")
    if settings_response.status_code == 200:
        print("   ✅ Settings update working")
    
    # Test 6: Statistics and Analytics
    print("\n📊 Testing Statistics and Analytics...")
    
    # Get current stats
    stats_response = client.get('/api/admin/notifications/stats/', **headers)
    print(f"   Get Stats Status: {stats_response.status_code}")
    
    if stats_response.status_code == 200:
        stats = stats_response.json()
        if 'data' in stats:
            data = stats['data']
            print(f"   ✅ Stats: {data.get('sent_today', 0)} sent today")
            print(f"   Delivery rate: {data.get('delivery_rate', 0)}%")
    
    # Get history
    history_response = client.get('/api/admin/notifications/history/?days=7', **headers)
    print(f"   Get History Status: {history_response.status_code}")
    
    if history_response.status_code == 200:
        print("   ✅ History analytics working")
    
    # Test 7: Different Channel Types
    print("\n📡 Testing Different Channel Types...")
    
    channel_tests = [
        {
            'name': 'Email Only',
            'channels': ['email']
        },
        {
            'name': 'Multi-channel',
            'channels': ['email', 'sms', 'push']
        }
    ]
    
    for test in channel_tests:
        notification_data = {
            'title': f'Test: {test["name"]}',
            'message': 'Testing different delivery channels',
            'notification_type': 'system',
            'recipient_type': 'user',
            'recipient_users': [admin_user.id],
            'channels': test['channels'],
            'priority': 1
        }
        
        response = client.post('/api/admin/notifications/send/',
                             json.dumps(notification_data),
                             content_type='application/json',
                             **headers)
        
        print(f"   {test['name']}: Status {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ {test['name']} working")
    
    # Test 8: Priority Levels
    print("\n🔥 Testing Priority Levels...")
    
    for priority in [1, 2, 3, 4]:
        priority_names = {1: 'Low', 2: 'Medium', 3: 'High', 4: 'Urgent'}
        
        notification_data = {
            'title': f'Test: {priority_names[priority]} Priority',
            'message': f'Testing priority level {priority}',
            'notification_type': 'system',
            'recipient_type': 'user',
            'recipient_users': [admin_user.id],
            'channels': ['email'],
            'priority': priority
        }
        
        response = client.post('/api/admin/notifications/send/',
                             json.dumps(notification_data),
                             content_type='application/json',
                             **headers)
        
        print(f"   Priority {priority} ({priority_names[priority]}): Status {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Priority {priority} working")
    
    print("\n" + "=" * 60)
    print("🎉 COMPREHENSIVE TESTING COMPLETE!")
    print("✅ All major notification features are working properly!")
    print("=" * 60)


if __name__ == '__main__':
    test_remaining_features()
