import time
import uuid
import logging
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from .utils import create_log


class SystemLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to automatically log system events and API requests
    """
    
    def process_request(self, request):
        """Process incoming request"""
        # Generate unique request ID for tracing
        request.log_request_id = str(uuid.uuid4())
        request.log_start_time = time.time()
        
        # Get client IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            request.log_ip = x_forwarded_for.split(',')[0]
        else:
            request.log_ip = request.META.get('REMOTE_ADDR')
        
        return None
    
    def process_response(self, request, response):
        """Process outgoing response"""
        try:
            # Calculate response time
            if hasattr(request, 'log_start_time'):
                response_time = (time.time() - request.log_start_time) * 1000  # Convert to ms
            else:
                response_time = None
            
            # Skip logging for certain paths to avoid noise
            skip_paths = [
                '/static/',
                '/media/',
                '/favicon.ico',
                '/admin/jsi18n/',
            ]
            
            if any(request.path.startswith(path) for path in skip_paths):
                return response
            
            # Determine log level based on status code
            if response.status_code >= 500:
                level = 'error'
            elif response.status_code >= 400:
                level = 'warning'
            else:
                level = 'info'
            
            # Create log entry for API requests
            if request.path.startswith('/api/'):
                create_log(
                    level=level,
                    category='api',
                    message=f"{request.method} {request.path} - {response.status_code}",
                    details=f"Response time: {response_time:.2f}ms" if response_time else None,
                    user=getattr(request, 'user', None) if hasattr(request, 'user') and request.user.is_authenticated else None,
                    ip_address=getattr(request, 'log_ip', None),
                    user_agent=request.META.get('HTTP_USER_AGENT'),
                    source=f"{request.method} {request.path}",
                    request_id=getattr(request, 'log_request_id', None),
                    response_time=response_time,
                    extra_data={
                        'status_code': response.status_code,
                        'method': request.method,
                        'path': request.path,
                        'query_params': dict(request.GET),
                    }
                )
            
        except Exception as e:
            # Don't let logging errors break the response
            logging.error(f"Error in SystemLoggingMiddleware: {e}")
        
        return response
    
    def process_exception(self, request, exception):
        """Process unhandled exceptions"""
        try:
            # Calculate response time
            if hasattr(request, 'log_start_time'):
                response_time = (time.time() - request.log_start_time) * 1000
            else:
                response_time = None
            
            # Log the exception
            create_log(
                level='error',
                category='system',
                message=f"Unhandled exception: {type(exception).__name__}",
                details=str(exception),
                user=getattr(request, 'user', None) if hasattr(request, 'user') and request.user.is_authenticated else None,
                ip_address=getattr(request, 'log_ip', None),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                source=f"{request.method} {request.path}",
                request_id=getattr(request, 'log_request_id', None),
                response_time=response_time,
                extra_data={
                    'exception_type': type(exception).__name__,
                    'method': request.method,
                    'path': request.path,
                    'query_params': dict(request.GET),
                }
            )
            
        except Exception as e:
            # Don't let logging errors break the exception handling
            logging.error(f"Error logging exception in SystemLoggingMiddleware: {e}")
        
        return None  # Let Django handle the exception normally


class AuthenticationLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log authentication events
    """
    
    def process_response(self, request, response):
        """Log authentication-related events"""
        try:
            # Log authentication attempts
            if request.path in ['/api/auth/login/', '/api/auth/register/']:
                if response.status_code == 200:
                    level = 'info'
                    message = f"Successful {'login' if 'login' in request.path else 'registration'}"
                else:
                    level = 'warning'
                    message = f"Failed {'login' if 'login' in request.path else 'registration'} attempt"
                
                create_log(
                    level=level,
                    category='authentication',
                    message=message,
                    details=f"Status: {response.status_code}",
                    user=getattr(request, 'user', None) if hasattr(request, 'user') and request.user.is_authenticated else None,
                    ip_address=getattr(request, 'log_ip', None),
                    user_agent=request.META.get('HTTP_USER_AGENT'),
                    source=request.path,
                    request_id=getattr(request, 'log_request_id', None),
                    extra_data={
                        'status_code': response.status_code,
                        'path': request.path,
                    }
                )
                
        except Exception as e:
            logging.error(f"Error in AuthenticationLoggingMiddleware: {e}")
        
        return response
