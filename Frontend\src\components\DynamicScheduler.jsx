import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Repeat, Globe, Save, X, AlertCircle, CheckCircle } from 'lucide-react';
import Button from './common/Button';
import Input from './common/Input';

const DynamicScheduler = ({ 
  onScheduleChange, 
  initialSchedule = {},
  showRecurring = true 
}) => {
  const [schedule, setSchedule] = useState({
    send_immediately: true,
    scheduled_date: '',
    scheduled_time: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    is_recurring: false,
    recurring_type: 'daily',
    recurring_interval: 1,
    recurring_end_date: '',
    recurring_days: [], // For weekly recurring
    recurring_day_of_month: 1, // For monthly recurring
    ...initialSchedule
  });

  const [errors, setErrors] = useState({});
  const [timezones, setTimezones] = useState([]);

  useEffect(() => {
    loadTimezones();
  }, []);

  useEffect(() => {
    validateSchedule();
    onScheduleChange(schedule);
  }, [schedule]);

  const loadTimezones = () => {
    // Common timezones
    const commonTimezones = [
      'UTC',
      'America/New_York',
      'America/Chicago',
      'America/Denver',
      'America/Los_Angeles',
      'Europe/London',
      'Europe/Paris',
      'Europe/Berlin',
      'Asia/Tokyo',
      'Asia/Shanghai',
      'Asia/Kolkata',
      'Australia/Sydney',
      'Pacific/Auckland'
    ];

    // Get user's current timezone
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    
    // Combine and deduplicate
    const allTimezones = [...new Set([userTimezone, ...commonTimezones])];
    
    setTimezones(allTimezones.map(tz => ({
      value: tz,
      label: `${tz} (${new Date().toLocaleString('en-US', { timeZone: tz, timeZoneName: 'short' }).split(', ')[1]})`
    })));
  };

  const validateSchedule = () => {
    const newErrors = {};

    if (!schedule.send_immediately) {
      if (!schedule.scheduled_date) {
        newErrors.scheduled_date = 'Date is required for scheduled notifications';
      } else {
        const scheduledDateTime = new Date(`${schedule.scheduled_date}T${schedule.scheduled_time || '00:00'}`);
        if (scheduledDateTime <= new Date()) {
          newErrors.scheduled_date = 'Scheduled time must be in the future';
        }
      }

      if (!schedule.scheduled_time) {
        newErrors.scheduled_time = 'Time is required for scheduled notifications';
      }

      if (schedule.is_recurring) {
        if (schedule.recurring_type === 'weekly' && schedule.recurring_days.length === 0) {
          newErrors.recurring_days = 'Select at least one day for weekly recurring';
        }

        if (schedule.recurring_end_date) {
          const endDate = new Date(schedule.recurring_end_date);
          const startDate = new Date(schedule.scheduled_date);
          if (endDate <= startDate) {
            newErrors.recurring_end_date = 'End date must be after start date';
          }
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleScheduleChange = (field, value) => {
    setSchedule(prev => {
      const updated = { ...prev, [field]: value };

      // Reset dependent fields
      if (field === 'send_immediately' && value) {
        updated.scheduled_date = '';
        updated.scheduled_time = '';
        updated.is_recurring = false;
      }

      if (field === 'is_recurring' && !value) {
        updated.recurring_type = 'daily';
        updated.recurring_interval = 1;
        updated.recurring_end_date = '';
        updated.recurring_days = [];
        updated.recurring_day_of_month = 1;
      }

      if (field === 'recurring_type') {
        updated.recurring_days = [];
        updated.recurring_day_of_month = 1;
      }

      return updated;
    });
  };

  const handleRecurringDayToggle = (day) => {
    setSchedule(prev => ({
      ...prev,
      recurring_days: prev.recurring_days.includes(day)
        ? prev.recurring_days.filter(d => d !== day)
        : [...prev.recurring_days, day]
    }));
  };

  const getScheduleSummary = () => {
    if (schedule.send_immediately) {
      return 'Will be sent immediately';
    }

    let summary = `Scheduled for ${schedule.scheduled_date} at ${schedule.scheduled_time}`;
    
    if (schedule.timezone !== Intl.DateTimeFormat().resolvedOptions().timeZone) {
      summary += ` (${schedule.timezone})`;
    }

    if (schedule.is_recurring) {
      summary += ` • Repeats ${schedule.recurring_type}`;
      
      if (schedule.recurring_interval > 1) {
        summary += ` every ${schedule.recurring_interval} ${schedule.recurring_type === 'daily' ? 'days' : 
                     schedule.recurring_type === 'weekly' ? 'weeks' : 'months'}`;
      }

      if (schedule.recurring_type === 'weekly' && schedule.recurring_days.length > 0) {
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const selectedDays = schedule.recurring_days.map(d => dayNames[d]).join(', ');
        summary += ` on ${selectedDays}`;
      }

      if (schedule.recurring_end_date) {
        summary += ` until ${schedule.recurring_end_date}`;
      }
    }

    return summary;
  };

  const isValid = Object.keys(errors).length === 0;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <Calendar size={20} className="text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Schedule Notification</h3>
      </div>

      {/* Send Immediately Option */}
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-3">
          <input
            type="radio"
            id="send_immediately"
            checked={schedule.send_immediately}
            onChange={(e) => handleScheduleChange('send_immediately', e.target.checked)}
            className="text-blue-600"
          />
          <label htmlFor="send_immediately" className="font-medium text-gray-900">
            Send Immediately
          </label>
        </div>
        <div className="text-sm text-gray-600">
          Notification will be sent right away
        </div>
      </div>

      {/* Schedule for Later Option */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <input
            type="radio"
            id="schedule_later"
            checked={!schedule.send_immediately}
            onChange={(e) => handleScheduleChange('send_immediately', !e.target.checked)}
            className="text-blue-600"
          />
          <label htmlFor="schedule_later" className="font-medium text-gray-900">
            Schedule for Later
          </label>
        </div>

        {!schedule.send_immediately && (
          <div className="pl-6 space-y-4">
            {/* Date and Time */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date
                </label>
                <input
                  type="date"
                  value={schedule.scheduled_date}
                  onChange={(e) => handleScheduleChange('scheduled_date', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.scheduled_date ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {errors.scheduled_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.scheduled_date}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time
                </label>
                <input
                  type="time"
                  value={schedule.scheduled_time}
                  onChange={(e) => handleScheduleChange('scheduled_time', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.scheduled_time ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {errors.scheduled_time && (
                  <p className="mt-1 text-sm text-red-600">{errors.scheduled_time}</p>
                )}
              </div>
            </div>

            {/* Timezone */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Globe size={16} className="inline mr-1" />
                Timezone
              </label>
              <select
                value={schedule.timezone}
                onChange={(e) => handleScheduleChange('timezone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {timezones.map(tz => (
                  <option key={tz.value} value={tz.value}>
                    {tz.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Recurring Options */}
            {showRecurring && (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="is_recurring"
                    checked={schedule.is_recurring}
                    onChange={(e) => handleScheduleChange('is_recurring', e.target.checked)}
                    className="text-blue-600"
                  />
                  <label htmlFor="is_recurring" className="font-medium text-gray-900">
                    <Repeat size={16} className="inline mr-1" />
                    Make this recurring
                  </label>
                </div>

                {schedule.is_recurring && (
                  <div className="pl-6 space-y-4 border-l-2 border-blue-200">
                    {/* Recurring Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Repeat
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <select
                          value={schedule.recurring_type}
                          onChange={(e) => handleScheduleChange('recurring_type', e.target.value)}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="daily">Daily</option>
                          <option value="weekly">Weekly</option>
                          <option value="monthly">Monthly</option>
                        </select>

                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">every</span>
                          <input
                            type="number"
                            min="1"
                            max="365"
                            value={schedule.recurring_interval}
                            onChange={(e) => handleScheduleChange('recurring_interval', parseInt(e.target.value))}
                            className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                          <span className="text-sm text-gray-600">
                            {schedule.recurring_type === 'daily' ? 'day(s)' : 
                             schedule.recurring_type === 'weekly' ? 'week(s)' : 'month(s)'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Weekly Days Selection */}
                    {schedule.recurring_type === 'weekly' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Days of the week
                        </label>
                        <div className="flex flex-wrap gap-2">
                          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
                            <button
                              key={day}
                              type="button"
                              onClick={() => handleRecurringDayToggle(index)}
                              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                                schedule.recurring_days.includes(index)
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            >
                              {day}
                            </button>
                          ))}
                        </div>
                        {errors.recurring_days && (
                          <p className="mt-1 text-sm text-red-600">{errors.recurring_days}</p>
                        )}
                      </div>
                    )}

                    {/* Monthly Day Selection */}
                    {schedule.recurring_type === 'monthly' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Day of the month
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="31"
                          value={schedule.recurring_day_of_month}
                          onChange={(e) => handleScheduleChange('recurring_day_of_month', parseInt(e.target.value))}
                          className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    )}

                    {/* End Date */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        End date (optional)
                      </label>
                      <input
                        type="date"
                        value={schedule.recurring_end_date}
                        onChange={(e) => handleScheduleChange('recurring_end_date', e.target.value)}
                        min={schedule.scheduled_date}
                        className={`w-full md:w-auto px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          errors.recurring_end_date ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                      {errors.recurring_end_date && (
                        <p className="mt-1 text-sm text-red-600">{errors.recurring_end_date}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Schedule Summary */}
      <div className={`p-4 rounded-lg border-l-4 ${
        isValid ? 'bg-green-50 border-green-400' : 'bg-yellow-50 border-yellow-400'
      }`}>
        <div className="flex items-start gap-3">
          {isValid ? (
            <CheckCircle size={20} className="text-green-600 mt-0.5" />
          ) : (
            <AlertCircle size={20} className="text-yellow-600 mt-0.5" />
          )}
          <div>
            <h4 className={`font-medium ${isValid ? 'text-green-800' : 'text-yellow-800'}`}>
              Schedule Summary
            </h4>
            <p className={`text-sm ${isValid ? 'text-green-700' : 'text-yellow-700'}`}>
              {getScheduleSummary()}
            </p>
            {!isValid && (
              <p className="text-sm text-yellow-600 mt-1">
                Please fix the errors above to proceed
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DynamicScheduler;
