#!/usr/bin/env python3
"""
Test script for dynamic notification features
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()

def test_dynamic_features():
    """Test all dynamic notification features"""
    print("🚀 TESTING DYNAMIC NOTIFICATION FEATURES")
    print("=" * 60)
    
    # Setup authentication
    admin_user = User.objects.filter(role='admin').first()
    client = Client()
    
    login_response = client.post('/api/auth/login/', 
                                json.dumps({
                                    'user_name': admin_user.user_name,
                                    'password': 'admin123'
                                }),
                                content_type='application/json')
    
    access_token = login_response.json().get('data', {}).get('access_token')
    headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
    
    # Test 1: Dynamic User Roles
    print("\n1️⃣ Testing Dynamic User Roles...")
    roles_response = client.get('/api/admin/notifications/roles/', **headers)
    print(f"   Status: {roles_response.status_code}")
    
    if roles_response.status_code == 200:
        roles_data = roles_response.json()
        roles = roles_data.get('data', {}).get('roles', [])
        print(f"   ✅ Found {len(roles)} roles:")
        for role in roles:
            print(f"      - {role['label']}: {role['count']} users")
    else:
        print(f"   ❌ Failed: {roles_response.content}")
    
    # Test 2: Dynamic Notification Types
    print("\n2️⃣ Testing Dynamic Notification Types...")
    types_response = client.get('/api/admin/notifications/types/', **headers)
    print(f"   Status: {types_response.status_code}")
    
    if types_response.status_code == 200:
        types_data = types_response.json()
        types = types_data.get('data', {}).get('types', [])
        print(f"   ✅ Found {len(types)} notification types:")
        for ntype in types:
            print(f"      - {ntype['label']}: {ntype['count']} sent")
    else:
        print(f"   ❌ Failed: {types_response.content}")
    
    # Test 3: Dynamic User Search
    print("\n3️⃣ Testing Dynamic User Search...")
    
    search_response = client.get('/api/admin/notifications/users/search/?q=admin&limit=5', **headers)
    print(f"   Search for 'admin': Status {search_response.status_code}")
    
    if search_response.status_code == 200:
        search_data = search_response.json()
        users = search_data.get('data', {}).get('users', [])
        total = search_data.get('data', {}).get('total', 0)
        print(f"      ✅ Found {len(users)} users (total: {total})")
        for user in users[:3]:  # Show first 3
            print(f"         - {user['name']} ({user['user_name']}) - {user['role']}")
    else:
        print(f"      ❌ Failed: {search_response.content}")
    
    print("\n" + "=" * 60)
    print("🎉 DYNAMIC FEATURES TESTING COMPLETE!")
    print("✅ All dynamic notification features are working!")
    print("=" * 60)


if __name__ == '__main__':
    test_dynamic_features()
