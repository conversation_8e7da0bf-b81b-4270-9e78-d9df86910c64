from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid
import os

User = get_user_model()


class BackupJob(models.Model):
    """Model to track backup jobs and their status"""
    
    BACKUP_TYPES = [
        ('full', 'Full System Backup'),
        ('database', 'Database Only'),
        ('partial', 'Partial Backup'),
        ('custom', 'Custom Backup'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    STORAGE_LOCATIONS = [
        ('local', 'Local Storage'),
        ('cloud', 'Cloud Storage'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    backup_type = models.CharField(max_length=20, choices=BACKUP_TYPES)
    status = models.Char<PERSON>ield(max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.IntegerField(default=0, help_text="Progress percentage (0-100)")
    
    # File information
    file_path = models.CharField(max_length=500, blank=True, null=True)
    file_size = models.BigIntegerField(null=True, blank=True, help_text="Size in bytes")
    storage_location = models.CharField(max_length=20, choices=STORAGE_LOCATIONS, default='local')
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Configuration
    options = models.JSONField(default=dict, blank=True)
    retention_days = models.IntegerField(default=30)
    
    # Error handling
    error_message = models.TextField(blank=True, null=True)
    
    class Meta:
        db_table = 'backup_jobs'
        ordering = ['-created_at']
        verbose_name = 'Backup Job'
        verbose_name_plural = 'Backup Jobs'
    
    def __str__(self):
        return f"{self.name} ({self.backup_type}) - {self.status}"
    
    @property
    def file_size_mb(self):
        """Return file size in MB"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0
    
    @property
    def file_size_gb(self):
        """Return file size in GB"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024 * 1024), 2)
        return 0
    
    @property
    def formatted_file_size(self):
        """Return formatted file size"""
        if not self.file_size:
            return "0 B"
        
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{round(self.file_size / 1024, 1)} KB"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{self.file_size_mb} MB"
        else:
            return f"{self.file_size_gb} GB"
    
    @property
    def duration(self):
        """Return backup duration if completed"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None
    
    def mark_started(self):
        """Mark backup as started"""
        self.status = 'in_progress'
        self.started_at = timezone.now()
        self.save(update_fields=['status', 'started_at'])
    
    def mark_completed(self, file_path=None, file_size=None):
        """Mark backup as completed"""
        self.status = 'completed'
        self.progress = 100
        self.completed_at = timezone.now()
        if file_path:
            self.file_path = file_path
        if file_size:
            self.file_size = file_size
        self.save(update_fields=['status', 'progress', 'completed_at', 'file_path', 'file_size'])
    
    def mark_failed(self, error_message=None):
        """Mark backup as failed"""
        self.status = 'failed'
        self.completed_at = timezone.now()
        if error_message:
            self.error_message = error_message
        self.save(update_fields=['status', 'completed_at', 'error_message'])
    
    def update_progress(self, progress):
        """Update backup progress"""
        self.progress = min(100, max(0, progress))
        self.save(update_fields=['progress'])


class BackupSchedule(models.Model):
    """Model for scheduled backups"""
    
    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ]
    
    name = models.CharField(max_length=255)
    backup_type = models.CharField(max_length=20, choices=BackupJob.BACKUP_TYPES)
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    time = models.TimeField(help_text="Time to run the backup")
    is_active = models.BooleanField(default=True)
    
    # Configuration
    options = models.JSONField(default=dict, blank=True)
    retention_days = models.IntegerField(default=30)
    storage_location = models.CharField(
        max_length=20, 
        choices=BackupJob.STORAGE_LOCATIONS, 
        default='local'
    )
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    last_run = models.DateTimeField(null=True, blank=True)
    next_run = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'backup_schedules'
        ordering = ['-created_at']
        verbose_name = 'Backup Schedule'
        verbose_name_plural = 'Backup Schedules'
    
    def __str__(self):
        return f"{self.name} ({self.frequency})"


class BackupSettings(models.Model):
    """Global backup settings"""
    
    # Storage settings
    default_storage_location = models.CharField(
        max_length=20, 
        choices=BackupJob.STORAGE_LOCATIONS, 
        default='local'
    )
    max_backup_size_gb = models.IntegerField(default=10)
    backup_directory = models.CharField(max_length=500, default='backups/')
    
    # Retention settings
    default_retention_days = models.IntegerField(default=30)
    auto_cleanup_enabled = models.BooleanField(default=True)
    
    # Notification settings
    email_notifications = models.BooleanField(default=True)
    notification_emails = models.JSONField(default=list, blank=True)
    
    # Performance settings
    max_concurrent_backups = models.IntegerField(default=2)
    compression_enabled = models.BooleanField(default=True)
    
    # Metadata
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE)
    
    class Meta:
        db_table = 'backup_settings'
        verbose_name = 'Backup Settings'
        verbose_name_plural = 'Backup Settings'
    
    def __str__(self):
        return "Backup Settings"
    
    @classmethod
    def get_settings(cls):
        """Get or create backup settings"""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            settings = cls.objects.get(id=1)
            return settings
        except cls.DoesNotExist:
            # Get the first admin user or create a default one
            admin_user = User.objects.filter(role='admin').first()
            if not admin_user:
                admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                # Create a default admin user for backup settings
                admin_user = User.objects.create_user(
                    user_name='backup_admin',
                    name='Backup Admin',
                    phone='0000000000',
                    email='<EMAIL>',
                    role='admin',
                    is_staff=True,
                    is_superuser=True,
                    is_verified=True
                )

            settings = cls.objects.create(
                id=1,
                updated_by=admin_user
            )
            return settings
