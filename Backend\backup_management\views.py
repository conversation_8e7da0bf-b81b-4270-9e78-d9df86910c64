from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.http import FileResponse, Http404
from django.shortcuts import get_object_or_404
from django.db.models import Count, Q, Sum
from django.utils import timezone
from datetime import timedelta
import os
import mimetypes

from .models import BackupJob, BackupSchedule, BackupSettings
from .serializers import (
    BackupJobSerializer, BackupJobCreateSerializer, BackupScheduleSerializer,
    BackupSettingsSerializer, BackupStatsSerializer, BackupProgressSerializer
)
from .services import BackupService


class BackupJobListCreateView(APIView):
    """List all backup jobs or create a new one"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Get list of backup jobs"""
        jobs = BackupJob.objects.all()
        
        # Filter by status if provided
        status_filter = request.query_params.get('status')
        if status_filter:
            jobs = jobs.filter(status=status_filter)
        
        # Filter by backup type if provided
        type_filter = request.query_params.get('type')
        if type_filter:
            jobs = jobs.filter(backup_type=type_filter)
        
        # Pagination
        page_size = int(request.query_params.get('page_size', 20))
        page = int(request.query_params.get('page', 1))
        start = (page - 1) * page_size
        end = start + page_size
        
        total_count = jobs.count()
        jobs = jobs[start:end]
        
        serializer = BackupJobSerializer(jobs, many=True)
        
        return Response({
            'success': True,
            'data': {
                'results': serializer.data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })
    
    def post(self, request):
        """Create a new backup job"""
        serializer = BackupJobCreateSerializer(data=request.data)
        
        if serializer.is_valid():
            # Create backup job
            backup_job = serializer.save(created_by=request.user)
            
            # Start backup process asynchronously
            backup_service = BackupService()
            backup_service.create_backup_async(backup_job)
            
            # Return job details
            response_serializer = BackupJobSerializer(backup_job)
            return Response({
                'success': True,
                'data': response_serializer.data,
                'message': 'Backup job created and started successfully'
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'error': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class BackupJobDetailView(APIView):
    """Get, update, or delete a specific backup job"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request, job_id):
        """Get backup job details"""
        job = get_object_or_404(BackupJob, id=job_id)
        serializer = BackupJobSerializer(job)
        
        return Response({
            'success': True,
            'data': serializer.data
        })
    
    def delete(self, request, job_id):
        """Delete a backup job and its file"""
        job = get_object_or_404(BackupJob, id=job_id)
        
        # Delete backup file if it exists
        if job.file_path and os.path.exists(job.file_path):
            try:
                os.remove(job.file_path)
            except OSError:
                pass  # File might be in use or already deleted
        
        job.delete()
        
        return Response({
            'success': True,
            'message': 'Backup job deleted successfully'
        })


class BackupJobStatusView(APIView):
    """Get backup job status and progress"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request, job_id):
        """Get backup job status"""
        job = get_object_or_404(BackupJob, id=job_id)
        
        return Response({
            'success': True,
            'data': {
                'id': str(job.id),
                'status': job.status,
                'progress': job.progress,
                'error_message': job.error_message,
                'started_at': job.started_at,
                'completed_at': job.completed_at
            }
        })


class BackupJobDownloadView(APIView):
    """Download a backup file"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request, job_id):
        """Download backup file"""
        job = get_object_or_404(BackupJob, id=job_id)
        
        if not job.file_path or not os.path.exists(job.file_path):
            raise Http404("Backup file not found")
        
        if job.status != 'completed':
            return Response({
                'success': False,
                'error': 'Backup is not completed yet'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Determine content type
        content_type, _ = mimetypes.guess_type(job.file_path)
        if not content_type:
            content_type = 'application/octet-stream'
        
        # Create filename for download
        filename = f"{job.name}_{job.created_at.strftime('%Y%m%d_%H%M%S')}"
        if job.file_path.endswith('.zip'):
            filename += '.zip'
        elif job.file_path.endswith('.json'):
            filename += '.json'
        elif job.file_path.endswith('.gz'):
            filename += '.gz'
        
        response = FileResponse(
            open(job.file_path, 'rb'),
            content_type=content_type,
            as_attachment=True,
            filename=filename
        )
        
        return response


class BackupStatsView(APIView):
    """Get backup statistics"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Get backup statistics"""
        # Get counts by status
        total_backups = BackupJob.objects.count()
        successful_backups = BackupJob.objects.filter(status='completed').count()
        failed_backups = BackupJob.objects.filter(status='failed').count()
        pending_backups = BackupJob.objects.filter(
            status__in=['pending', 'in_progress']
        ).count()
        
        # Calculate success rate
        success_rate = 0
        if total_backups > 0:
            success_rate = (successful_backups / total_backups) * 100
        
        # Get total storage used
        total_size = BackupJob.objects.filter(
            status='completed',
            file_size__isnull=False
        ).aggregate(total=Sum('file_size'))['total'] or 0
        
        # Format storage size
        if total_size < 1024 * 1024:
            storage_used = f"{round(total_size / 1024, 1)} KB"
        elif total_size < 1024 * 1024 * 1024:
            storage_used = f"{round(total_size / (1024 * 1024), 1)} MB"
        else:
            storage_used = f"{round(total_size / (1024 * 1024 * 1024), 2)} GB"
        
        # Get last backup date
        last_backup = BackupJob.objects.filter(
            status='completed'
        ).order_by('-completed_at').first()
        
        last_backup_date = last_backup.completed_at if last_backup else None
        
        stats_data = {
            'total_backups': total_backups,
            'successful_backups': successful_backups,
            'failed_backups': failed_backups,
            'pending_backups': pending_backups,
            'total_storage_used': storage_used,
            'last_backup_date': last_backup_date,
            'success_rate': round(success_rate, 1)
        }
        
        serializer = BackupStatsSerializer(stats_data)
        
        return Response({
            'success': True,
            'data': serializer.data
        })


class BackupSettingsView(APIView):
    """Get or update backup settings"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Get backup settings"""
        settings = BackupSettings.get_settings()
        serializer = BackupSettingsSerializer(settings)
        
        return Response({
            'success': True,
            'data': serializer.data
        })
    
    def put(self, request):
        """Update backup settings"""
        settings = BackupSettings.get_settings()
        serializer = BackupSettingsSerializer(settings, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            
            return Response({
                'success': True,
                'data': serializer.data,
                'message': 'Backup settings updated successfully'
            })
        
        return Response({
            'success': False,
            'error': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class BackupScheduleListCreateView(APIView):
    """List all backup schedules or create a new one"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """Get list of backup schedules"""
        schedules = BackupSchedule.objects.all()
        serializer = BackupScheduleSerializer(schedules, many=True)
        
        return Response({
            'success': True,
            'data': serializer.data
        })
    
    def post(self, request):
        """Create a new backup schedule"""
        serializer = BackupScheduleSerializer(data=request.data)
        
        if serializer.is_valid():
            schedule = serializer.save(created_by=request.user)
            
            return Response({
                'success': True,
                'data': BackupScheduleSerializer(schedule).data,
                'message': 'Backup schedule created successfully'
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'error': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class BackupScheduleDetailView(APIView):
    """Get, update, or delete a specific backup schedule"""
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request, schedule_id):
        """Get backup schedule details"""
        schedule = get_object_or_404(BackupSchedule, id=schedule_id)
        serializer = BackupScheduleSerializer(schedule)
        
        return Response({
            'success': True,
            'data': serializer.data
        })
    
    def put(self, request, schedule_id):
        """Update backup schedule"""
        schedule = get_object_or_404(BackupSchedule, id=schedule_id)
        serializer = BackupScheduleSerializer(schedule, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save()
            
            return Response({
                'success': True,
                'data': serializer.data,
                'message': 'Backup schedule updated successfully'
            })
        
        return Response({
            'success': False,
            'error': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, schedule_id):
        """Delete backup schedule"""
        schedule = get_object_or_404(BackupSchedule, id=schedule_id)
        schedule.delete()
        
        return Response({
            'success': True,
            'message': 'Backup schedule deleted successfully'
        })
