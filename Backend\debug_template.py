#!/usr/bin/env python3
"""
Debug template creation
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()

def debug_template_creation():
    """Debug template creation"""
    print("🔍 DEBUGGING TEMPLATE CREATION")
    print("=" * 50)
    
    # Setup authentication
    admin_user = User.objects.filter(role='admin').first()
    client = Client()
    
    login_response = client.post('/api/auth/login/', 
                                json.dumps({
                                    'user_name': admin_user.user_name,
                                    'password': 'admin123'
                                }),
                                content_type='application/json')
    
    access_token = login_response.json().get('data', {}).get('access_token')
    headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
    
    # Test template creation with detailed error reporting
    template_data = {
        'name': 'Debug Test Template',
        'template_type': 'general',
        'title_template': 'Debug: {{title}}',
        'message_template': 'Debug message: {{message}}',
        'supports_email': True,
        'supports_sms': False,
        'supports_push': False
    }
    
    print("Template data:", json.dumps(template_data, indent=2))
    
    create_response = client.post('/api/admin/notifications/templates/create/',
                                json.dumps(template_data),
                                content_type='application/json',
                                **headers)
    
    print(f"Response status: {create_response.status_code}")
    print(f"Response content: {create_response.content}")
    
    if create_response.status_code == 200:
        print("✅ Template creation successful!")
        result = create_response.json()
        print("Created template:", json.dumps(result, indent=2))
    else:
        print("❌ Template creation failed")
        try:
            error_data = create_response.json()
            print("Error details:", json.dumps(error_data, indent=2))
        except:
            print("Raw error:", create_response.content)


if __name__ == '__main__':
    debug_template_creation()
