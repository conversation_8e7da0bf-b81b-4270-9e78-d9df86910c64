import React, { useState, useEffect } from 'react';
import { Users, Check } from 'lucide-react';

const RoleSelector = ({ 
  selectedRoles = [], 
  onRolesChange, 
  allowMultiple = true,
  showCounts = true 
}) => {
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadRoles();
  }, []);

  const loadRoles = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/notifications/roles/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRoles(data.data?.roles || []);
      } else {
        setError('Failed to load roles');
      }
    } catch (err) {
      setError('Error loading roles');
      console.error('Role loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRoleToggle = (roleValue) => {
    if (allowMultiple) {
      const newSelectedRoles = selectedRoles.includes(roleValue)
        ? selectedRoles.filter(r => r !== roleValue)
        : [...selectedRoles, roleValue];
      onRolesChange(newSelectedRoles);
    } else {
      onRolesChange(selectedRoles.includes(roleValue) ? [] : [roleValue]);
    }
  };

  const getRoleIcon = (role) => {
    const icons = {
      admin: '👑',
      restaurant: '🏪',
      customer: '👤',
      delivery_agent: '🚚'
    };
    return icons[role] || '👥';
  };

  const getRoleColor = (role) => {
    const colors = {
      admin: 'border-red-200 bg-red-50 text-red-700',
      restaurant: 'border-blue-200 bg-blue-50 text-blue-700',
      customer: 'border-green-200 bg-green-50 text-green-700',
      delivery_agent: 'border-yellow-200 bg-yellow-50 text-yellow-700'
    };
    return colors[role] || 'border-gray-200 bg-gray-50 text-gray-700';
  };

  const getSelectedRoleColor = (role) => {
    const colors = {
      admin: 'border-red-500 bg-red-100 text-red-800',
      restaurant: 'border-blue-500 bg-blue-100 text-blue-800',
      customer: 'border-green-500 bg-green-100 text-green-800',
      delivery_agent: 'border-yellow-500 bg-yellow-100 text-yellow-800'
    };
    return colors[role] || 'border-gray-500 bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        <span className="ml-2 text-gray-600">Loading roles...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-600 text-sm">{error}</p>
        <button 
          onClick={loadRoles}
          className="mt-2 text-red-700 hover:text-red-800 text-sm underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
        <Users size={16} />
        <span>Select User Roles</span>
        {selectedRoles.length > 0 && (
          <span className="text-blue-600">({selectedRoles.length} selected)</span>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {roles.map((role) => {
          const isSelected = selectedRoles.includes(role.value);
          const colorClass = isSelected ? getSelectedRoleColor(role.value) : getRoleColor(role.value);

          return (
            <button
              key={role.value}
              onClick={() => handleRoleToggle(role.value)}
              className={`relative p-4 border-2 rounded-lg transition-all duration-200 hover:shadow-md ${colorClass}`}
            >
              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <div className="w-5 h-5 bg-current rounded-full flex items-center justify-center">
                    <Check size={12} className="text-white" />
                  </div>
                </div>
              )}

              <div className="flex items-center gap-3">
                <div className="text-2xl">
                  {getRoleIcon(role.value)}
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium">
                    {role.label}
                  </div>
                  {showCounts && (
                    <div className="text-sm opacity-75">
                      {role.count} users
                    </div>
                  )}
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Selection summary */}
      {selectedRoles.length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="text-sm font-medium text-blue-800 mb-2">
            Selected Roles:
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedRoles.map((roleValue) => {
              const role = roles.find(r => r.value === roleValue);
              return role ? (
                <span
                  key={roleValue}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm"
                >
                  <span>{getRoleIcon(roleValue)}</span>
                  <span>{role.label}</span>
                  {showCounts && <span>({role.count})</span>}
                </span>
              ) : null;
            })}
          </div>
          <div className="mt-2 text-xs text-blue-600">
            Total users: {roles
              .filter(r => selectedRoles.includes(r.value))
              .reduce((sum, r) => sum + r.count, 0)}
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleSelector;
