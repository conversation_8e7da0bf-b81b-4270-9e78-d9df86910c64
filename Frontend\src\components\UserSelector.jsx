import React, { useState, useEffect, useRef } from 'react';
import { Search, X, User, Users, ChevronDown } from 'lucide-react';
import { adminApi } from '../services/adminApi';

const UserSelector = ({ 
  selectedUsers = [], 
  onUsersChange, 
  placeholder = "Search and select users...",
  maxSelections = null,
  roleFilter = null 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Search users when query changes
  useEffect(() => {
    const searchUsers = async () => {
      if (searchQuery.length < 2) {
        setUsers([]);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams({
          q: searchQuery,
          limit: '20'
        });

        if (roleFilter) {
          params.append('role', roleFilter);
        }

        const response = await fetch(`/api/admin/notifications/users/search/?${params}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setUsers(data.data?.users || []);
        } else {
          setError('Failed to search users');
        }
      } catch (err) {
        setError('Error searching users');
        console.error('User search error:', err);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchUsers, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery, roleFilter]);

  const handleUserSelect = (user) => {
    if (selectedUsers.find(u => u.id === user.id)) {
      return; // Already selected
    }

    if (maxSelections && selectedUsers.length >= maxSelections) {
      return; // Max selections reached
    }

    const newSelectedUsers = [...selectedUsers, user];
    onUsersChange(newSelectedUsers);
    setSearchQuery('');
    setUsers([]);
  };

  const handleUserRemove = (userId) => {
    const newSelectedUsers = selectedUsers.filter(u => u.id !== userId);
    onUsersChange(newSelectedUsers);
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const getRoleColor = (role) => {
    const colors = {
      admin: 'bg-red-100 text-red-800',
      restaurant: 'bg-blue-100 text-blue-800',
      customer: 'bg-green-100 text-green-800',
      delivery_agent: 'bg-yellow-100 text-yellow-800'
    };
    return colors[role] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Selected Users Display */}
      {selectedUsers.length > 0 && (
        <div className="mb-2 flex flex-wrap gap-2">
          {selectedUsers.map((user) => (
            <div
              key={user.id}
              className="flex items-center gap-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-1 text-sm"
            >
              <User size={14} className="text-blue-600" />
              <span className="text-blue-800 font-medium">{user.name || user.user_name}</span>
              <span className={`px-2 py-0.5 rounded text-xs ${getRoleColor(user.role)}`}>
                {user.role}
              </span>
              <button
                onClick={() => handleUserRemove(user.id)}
                className="text-blue-600 hover:text-blue-800"
              >
                <X size={14} />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Search Input */}
      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent cursor-text"
          onClick={handleInputFocus}
        >
          <div className="flex items-center gap-2">
            <Search size={16} className="text-gray-400" />
            <input
              ref={searchInputRef}
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsOpen(true)}
              placeholder={placeholder}
              className="flex-1 outline-none text-sm"
            />
            <ChevronDown 
              size={16} 
              className={`text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
            />
          </div>
        </div>

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {loading && (
              <div className="p-3 text-center text-gray-500">
                <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
                <span className="ml-2 text-sm">Searching...</span>
              </div>
            )}

            {error && (
              <div className="p-3 text-center text-red-500 text-sm">
                {error}
              </div>
            )}

            {!loading && !error && searchQuery.length < 2 && (
              <div className="p-3 text-center text-gray-500 text-sm">
                Type at least 2 characters to search users
              </div>
            )}

            {!loading && !error && searchQuery.length >= 2 && users.length === 0 && (
              <div className="p-3 text-center text-gray-500 text-sm">
                No users found matching "{searchQuery}"
              </div>
            )}

            {!loading && !error && users.length > 0 && (
              <div className="py-1">
                {users.map((user) => {
                  const isSelected = selectedUsers.find(u => u.id === user.id);
                  const isDisabled = maxSelections && selectedUsers.length >= maxSelections && !isSelected;

                  return (
                    <button
                      key={user.id}
                      onClick={() => !isSelected && !isDisabled && handleUserSelect(user)}
                      disabled={isSelected || isDisabled}
                      className={`w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-3 ${
                        isSelected ? 'bg-blue-50 cursor-not-allowed' : ''
                      } ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <User size={16} className="text-gray-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900 truncate">
                            {user.name || user.user_name}
                          </span>
                          <span className={`px-2 py-0.5 rounded text-xs ${getRoleColor(user.role)}`}>
                            {user.role}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {user.email}
                        </div>
                      </div>
                      {isSelected && (
                        <div className="text-blue-600">
                          <Users size={16} />
                        </div>
                      )}
                    </button>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selection Info */}
      {maxSelections && (
        <div className="mt-1 text-xs text-gray-500">
          {selectedUsers.length} of {maxSelections} users selected
        </div>
      )}
    </div>
  );
};

export default UserSelector;
