from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta
import random
from notifications.models import Notification, NotificationTemplate, NotificationSettings
from notifications.services import NotificationTemplateService

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample notification data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=20,
            help='Number of notifications to create'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        self.stdout.write('Creating sample notification data...')
        
        # Create default templates
        self.stdout.write('Creating default templates...')
        NotificationTemplateService.create_default_templates()
        
        # Get or create admin user
        admin_user = User.objects.filter(role='admin').first()
        if not admin_user:
            self.stdout.write('❌ No admin user found. Please create an admin user first.')
            return

        # Create notification settings
        self.stdout.write('Creating notification settings...')
        settings, created = NotificationSettings.objects.get_or_create(
            id=1,
            defaults={
                'updated_by': admin_user,
                'email_enabled': True,
                'email_from_name': 'Afghan Sofra',
                'email_from_address': '<EMAIL>',
                'sms_enabled': False,
                'push_enabled': True,
            }
        )
        if created:
            self.stdout.write('✅ Created notification settings')
        else:
            self.stdout.write('✅ Notification settings already exist')
        
        # Sample notification data
        notification_types = ['order', 'user', 'restaurant', 'delivery', 'payment', 'system']
        recipient_types = ['user', 'role', 'all']
        statuses = ['draft', 'sent', 'delivered', 'failed']
        channels_options = [
            ['email'],
            ['email', 'push'],
            ['email', 'sms'],
            ['push'],
        ]
        
        sample_notifications = [
            {
                'title': 'Welcome to Afghan Sofra!',
                'message': 'Thank you for joining our platform. Enjoy delicious Afghan cuisine!',
                'type': 'user',
            },
            {
                'title': 'Order Confirmation',
                'message': 'Your order has been confirmed and is being prepared.',
                'type': 'order',
            },
            {
                'title': 'Restaurant Application Approved',
                'message': 'Congratulations! Your restaurant application has been approved.',
                'type': 'restaurant',
            },
            {
                'title': 'Delivery Assignment',
                'message': 'You have been assigned a new delivery order.',
                'type': 'delivery',
            },
            {
                'title': 'Payment Successful',
                'message': 'Your payment has been processed successfully.',
                'type': 'payment',
            },
            {
                'title': 'System Maintenance',
                'message': 'Scheduled maintenance will occur tonight from 2-4 AM.',
                'type': 'system',
            },
            {
                'title': 'New Menu Items Available',
                'message': 'Check out our new delicious menu items!',
                'type': 'restaurant',
            },
            {
                'title': 'Order Delivered',
                'message': 'Your order has been successfully delivered. Enjoy your meal!',
                'type': 'order',
            },
            {
                'title': 'Profile Verification Required',
                'message': 'Please verify your profile to continue using our services.',
                'type': 'user',
            },
            {
                'title': 'Weekly Earnings Report',
                'message': 'Your weekly earnings report is now available.',
                'type': 'delivery',
            },
        ]
        
        self.stdout.write(f'Creating {count} sample notifications...')
        
        for i in range(count):
            # Choose random sample or create variation
            if i < len(sample_notifications):
                sample = sample_notifications[i]
            else:
                sample = random.choice(sample_notifications)
                sample = {
                    'title': f"{sample['title']} #{i+1}",
                    'message': sample['message'],
                    'type': sample['type'],
                }
            
            # Random timestamp within last 30 days
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            
            created_at = timezone.now() - timedelta(
                days=days_ago, 
                hours=hours_ago, 
                minutes=minutes_ago
            )
            
            # Create notification
            notification = Notification.objects.create(
                title=sample['title'],
                message=sample['message'],
                notification_type=sample['type'],
                recipient_type=random.choice(recipient_types),
                channels=random.choice(channels_options),
                status=random.choice(statuses),
                priority=random.randint(1, 4),
                total_recipients=random.randint(1, 100),
                successful_deliveries=random.randint(0, 80),
                failed_deliveries=random.randint(0, 20),
                created_by=admin_user,
                context_data={
                    'sample_data': True,
                    'generated_at': timezone.now().isoformat()
                }
            )
            
            # Update timestamp to backdated value
            notification.created_at = created_at
            if notification.status in ['sent', 'delivered']:
                notification.sent_at = created_at + timedelta(minutes=random.randint(1, 30))
            notification.save()
            
            if (i + 1) % 5 == 0:
                self.stdout.write(f'Created {i + 1} notifications...')
        
        # Create some templates
        templates_created = NotificationTemplate.objects.count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created:\n'
                f'- {count} sample notifications\n'
                f'- {templates_created} notification templates\n'
                f'- Notification settings configured'
            )
        )
