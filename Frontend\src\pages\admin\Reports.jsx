import React, { useState, useEffect } from "react";
import {
  FileText,
  Download,
  Calendar,
  Filter,
  TrendingUp,
  Users,
  ShoppingBag,
  DollarSign,
  Store,
  Truck,
  BarChart3,
  PieChart,
  Activity,
  RefreshCw,
  Search,
  Eye,
  AlertCircle,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { adminApi } from "../../services/adminApi";

const Reports = () => {
  const [loading, setLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState("overview");
  const [dateRange, setDateRange] = useState("month");
  const [reportData, setReportData] = useState(null);
  const [reportHistory, setReportHistory] = useState([]);
  const [quickStats, setQuickStats] = useState([]);
  const [error, setError] = useState(null);

  const reportTypes = [
    {
      id: "overview",
      name: "Business Overview",
      description: "Complete business performance summary",
      icon: <BarChart3 size={20} />,
      color: "blue",
    },
    {
      id: "sales",
      name: "Sales Report",
      description: "Revenue and order analytics",
      icon: <DollarSign size={20} />,
      color: "green",
    },
    {
      id: "users",
      name: "User Analytics",
      description: "Customer and user behavior",
      icon: <Users size={20} />,
      color: "purple",
    },
    {
      id: "restaurants",
      name: "Restaurant Performance",
      description: "Restaurant metrics and rankings",
      icon: <Store size={20} />,
      color: "orange",
    },
    {
      id: "delivery",
      name: "Delivery Analytics",
      description: "Delivery performance and efficiency",
      icon: <Truck size={20} />,
      color: "indigo",
    },
    {
      id: "financial",
      name: "Financial Report",
      description: "Detailed financial breakdown",
      icon: <PieChart size={20} />,
      color: "emerald",
    },
  ];

  // Load initial data
  useEffect(() => {
    loadReportHistory();
    loadQuickStats();
  }, []);

  const loadReportHistory = async () => {
    try {
      const result = await adminApi.getReportHistory();
      if (result.success) {
        setReportHistory(result.data.reports || []);
      }
    } catch (error) {
      console.error("Error loading report history:", error);
    }
  };

  const loadQuickStats = async () => {
    try {
      // For now, use mock data. In a real app, this would come from a dashboard API
      const mockStats = [
        {
          label: "Total Revenue",
          value: "$45,230",
          change: "+12.5%",
          trend: "up",
          icon: <DollarSign size={20} />,
        },
        {
          label: "Total Orders",
          value: "1,234",
          change: "+8.2%",
          trend: "up",
          icon: <ShoppingBag size={20} />,
        },
        {
          label: "Active Users",
          value: "892",
          change: "+15.3%",
          trend: "up",
          icon: <Users size={20} />,
        },
        {
          label: "Restaurants",
          value: "67",
          change: "+5.1%",
          trend: "up",
          icon: <Store size={20} />,
        },
      ];
      setQuickStats(mockStats);
    } catch (error) {
      console.error("Error loading quick stats:", error);
    }
  };

  const generateReport = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await adminApi.generateReport(selectedReport, dateRange);

      if (result.success) {
        setReportData({
          id: result.data.id,
          generated: result.data.generated_at || new Date().toISOString(),
          type: selectedReport,
          period: dateRange,
          status: result.data.status || "completed",
          download_url: result.data.download_url,
        });

        // Refresh report history
        loadReportHistory();
      } else {
        setError(result.error?.message || "Failed to generate report");
      }
    } catch (error) {
      console.error("Error generating report:", error);
      setError("Error generating report. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = async (format, reportId = null) => {
    try {
      const id = reportId || reportData?.id;
      if (!id) {
        alert("No report available for download");
        return;
      }

      const result = await adminApi.downloadReport(id, format);

      if (result.success) {
        // Create download link
        const url = window.URL.createObjectURL(result.data);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${selectedReport}_report_${dateRange}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert("Failed to download report");
      }
    } catch (error) {
      console.error("Error downloading report:", error);
      alert("Error downloading report");
    }
  };

  return (
    <div className='p-6 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>
            Reports & Analytics
          </h1>
          <p className='text-gray-600 mt-1'>
            Generate comprehensive business reports and insights
          </p>
        </div>
        <div className='flex items-center space-x-3 mt-4 sm:mt-0'>
          <Button
            variant='outline'
            icon={<RefreshCw size={16} />}
            onClick={() => window.location.reload()}
          >
            Refresh
          </Button>
          <Button
            variant='primary'
            icon={<Download size={16} />}
            onClick={() => downloadReport("pdf")}
            disabled={!reportData}
          >
            Export PDF
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
          <div className='flex items-center'>
            <AlertCircle size={20} className='text-red-600 mr-3' />
            <div>
              <h4 className='font-medium text-red-800'>Error</h4>
              <p className='text-sm text-red-700'>{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Quick Stats */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        {quickStats.map((stat, index) => (
          <Card key={index}>
            <div className='p-6'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center space-x-3'>
                  <div className='p-2 bg-primary-100 rounded-lg'>
                    {stat.icon}
                  </div>
                  <div>
                    <p className='text-sm text-gray-600'>{stat.label}</p>
                    <p className='text-2xl font-bold text-gray-900'>
                      {stat.value}
                    </p>
                  </div>
                </div>
                <Badge
                  variant={stat.trend === "up" ? "success" : "danger"}
                  size='small'
                >
                  {stat.change}
                </Badge>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Report Generation */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Report Types */}
        <div className='lg:col-span-1'>
          <Card>
            <div className='p-6'>
              <h3 className='text-lg font-semibold mb-4'>Report Types</h3>
              <div className='space-y-3'>
                {reportTypes.map((report) => (
                  <button
                    key={report.id}
                    onClick={() => setSelectedReport(report.id)}
                    className={`w-full p-3 rounded-lg border transition-all ${
                      selectedReport === report.id
                        ? "border-primary-500 bg-primary-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <div className='flex items-center space-x-3'>
                      <div
                        className={`p-2 rounded-lg bg-${report.color}-100 text-${report.color}-600`}
                      >
                        {report.icon}
                      </div>
                      <div className='text-left'>
                        <p className='font-medium text-gray-900'>
                          {report.name}
                        </p>
                        <p className='text-sm text-gray-600'>
                          {report.description}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </Card>
        </div>

        {/* Report Configuration */}
        <div className='lg:col-span-2'>
          <Card>
            <div className='p-6'>
              <h3 className='text-lg font-semibold mb-4'>Generate Report</h3>

              {/* Date Range Selection */}
              <div className='mb-6'>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Date Range
                </label>
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                >
                  <option value='week'>Last 7 Days</option>
                  <option value='month'>Last 30 Days</option>
                  <option value='quarter'>Last 3 Months</option>
                  <option value='year'>Last 12 Months</option>
                  <option value='custom'>Custom Range</option>
                </select>
              </div>

              {/* Generate Button */}
              <Button
                variant='primary'
                icon={<BarChart3 size={16} />}
                onClick={generateReport}
                loading={loading}
                className='w-full mb-4'
              >
                {loading ? "Generating Report..." : "Generate Report"}
              </Button>

              {/* Report Preview */}
              {reportData && (
                <div className='mt-6 p-4 bg-gray-50 rounded-lg'>
                  <div className='flex items-center justify-between mb-3'>
                    <h4 className='font-medium text-gray-900'>
                      Report Generated
                    </h4>
                    <Badge variant='success'>Ready</Badge>
                  </div>
                  <div className='space-y-2 text-sm text-gray-600'>
                    <p>
                      Type:{" "}
                      {reportTypes.find((r) => r.id === reportData.type)?.name}
                    </p>
                    <p>Period: {reportData.period}</p>
                    <p>
                      Generated:{" "}
                      {new Date(reportData.generated).toLocaleString()}
                    </p>
                  </div>
                  <div className='flex space-x-2 mt-4'>
                    <Button
                      size='small'
                      variant='outline'
                      icon={<Eye size={14} />}
                      onClick={() => alert("Opening report preview...")}
                    >
                      Preview
                    </Button>
                    <Button
                      size='small'
                      variant='outline'
                      icon={<Download size={14} />}
                      onClick={() => downloadReport("excel")}
                    >
                      Excel
                    </Button>
                    <Button
                      size='small'
                      variant='outline'
                      icon={<Download size={14} />}
                      onClick={() => downloadReport("csv")}
                    >
                      CSV
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>

      {/* Recent Reports */}
      <Card>
        <div className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Recent Reports</h3>
          <div className='space-y-3'>
            {reportHistory.length > 0 ? (
              reportHistory.map((report, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50'
                >
                  <div className='flex items-center space-x-3'>
                    <FileText size={20} className='text-gray-400' />
                    <div>
                      <p className='font-medium text-gray-900'>
                        {report.name || `${report.report_type} Report`}
                      </p>
                      <p className='text-sm text-gray-600'>
                        {report.report_type} •{" "}
                        {new Date(report.created_at).toLocaleDateString()} •{" "}
                        {report.file_size || "N/A"}
                      </p>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Badge
                      variant={
                        report.status === "completed"
                          ? "success"
                          : report.status === "failed"
                          ? "danger"
                          : "warning"
                      }
                      size='small'
                    >
                      {report.status}
                    </Badge>
                    {report.status === "completed" && (
                      <Button
                        size='small'
                        variant='outline'
                        icon={<Download size={14} />}
                        onClick={() => downloadReport("pdf", report.id)}
                      >
                        Download
                      </Button>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className='text-center py-8'>
                <FileText size={48} className='mx-auto text-gray-300 mb-4' />
                <p className='text-gray-500'>No reports generated yet</p>
                <p className='text-sm text-gray-400 mt-1'>
                  Generate your first report using the options above
                </p>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Reports;
