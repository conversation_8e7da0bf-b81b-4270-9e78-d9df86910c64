# api/views.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import generics, permissions, status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta

from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from users.models import  User
from .serializers import (

    UserLoginSerializer,
    UserRegistrationSerializer,
    EmailVerificationSerializer,
    PasswordChangeSerializer
)
from users.models import OTPVerification

class UserLoginView(APIView):
    def post(self, request):
        # Debug logging
        print(f"🔍 Login request data: {request.data}")
        print(f"🔍 Request headers: {dict(request.headers)}")

        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)

            # Return response with tokens, role, and redirection info
            return Response({
                'success': True,
                'message': 'Login successful',
                'data': {
                    'access_token': access_token,
                    'refresh_token': refresh_token,
                    'user': {
                        'id': user.id,
                        'name': user.name,
                        'username': user.user_name,
                        'email': user.email,
                        'role': user.role,
                        'is_verified': user.is_verified
                    },
                    'redirect_to': f'/dashboard/{user.role}'
                }
            }, status=status.HTTP_200_OK)

        # Debug logging for validation errors
        print(f"🔍 Validation errors: {serializer.errors}")

        # Format validation errors for better frontend handling
        errors = {}
        for field, field_errors in serializer.errors.items():
            if isinstance(field_errors, list):
                errors[field] = field_errors[0] if field_errors else 'Invalid value'
            else:
                errors[field] = str(field_errors)

        return Response({
            'success': False,
            'message': 'Login failed',
            'errors': errors
        }, status=status.HTTP_400_BAD_REQUEST)
    





class UserRegistrationView(APIView):
    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.save()

                return Response({
                    'success': True,
                    'message': f'Registration successful! Please check {user.email} for your verification code.',
                    'data': {
                        'email': user.email,
                        'user_id': user.id,
                        'username': user.user_name,
                        'name': user.name,
                        'role': user.role,
                        'requires_verification': True
                    }
                }, status=status.HTTP_201_CREATED)
            except Exception as e:
                return Response({
                    'success': False,
                    'message': 'Registration failed',
                    'errors': {
                        'non_field_errors': f'Failed to create account: {str(e)}'
                    }
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Format validation errors for better frontend handling
        errors = {}
        for field, field_errors in serializer.errors.items():
            if isinstance(field_errors, list):
                errors[field] = field_errors[0] if field_errors else 'Invalid value'
            else:
                errors[field] = str(field_errors)

        return Response({
            'success': False,
            'message': 'Registration failed',
            'errors': errors
        }, status=status.HTTP_400_BAD_REQUEST)

class VerifyEmailView(APIView):
    def post(self, request):
        serializer = EmailVerificationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.validated_data['user']
                otp_record = serializer.validated_data['otp_record']

                # Mark OTP as used
                otp_record.is_used = True
                otp_record.save()

                # Mark user as verified
                user.is_verified = True
                user.save()

                # Generate JWT tokens
                refresh = RefreshToken.for_user(user)
                access_token = str(refresh.access_token)
                refresh_token = str(refresh)

                return Response({
                    'success': True,
                    'message': 'Email verified successfully! You can now login.',
                    'data': {
                        'access_token': access_token,
                        'refresh_token': refresh_token,
                        'user': {
                            'id': user.id,
                            'name': user.name,
                            'username': user.user_name,
                            'email': user.email,
                            'role': user.role,
                            'is_verified': True
                        },
                        'redirect_to': f'/dashboard/{user.role}'
                    }
                }, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({
                    'success': False,
                    'message': 'Verification failed',
                    'errors': {
                        'non_field_errors': 'Failed to verify email. Please try again.'
                    }
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Format validation errors for better frontend handling
        errors = {}
        for field, field_errors in serializer.errors.items():
            if isinstance(field_errors, list):
                errors[field] = field_errors[0] if field_errors else 'Invalid value'
            else:
                errors[field] = str(field_errors)

        return Response({
            'success': False,
            'message': 'Email verification failed',
            'errors': errors
        }, status=status.HTTP_400_BAD_REQUEST)

class ResendOTPView(APIView):
    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'error': 'User with this email does not exist'}, status=status.HTTP_404_NOT_FOUND)
        
        # Generate and send new OTP
        OTPVerification.generate_otp(user, 'registration').send_otp_email()
        
        return Response({
            'message': 'New OTP has been sent to your email',
            'email': user.email
        }, status=status.HTTP_200_OK)

class PasswordChangeView(APIView):
    def post(self, request):
        serializer = PasswordChangeSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            otp_record = serializer.validated_data['otp_record']
            new_password = serializer.validated_data['new_password']
            
            # Mark OTP as used
            otp_record.is_used = True
            otp_record.save()
            
            # Update password
            user.set_password(new_password)
            user.save()
            
            return Response({
                'message': 'Password changed successfully'
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RequestPasswordChangeOTPView(APIView):
    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'error': 'User with this email does not exist'}, status=status.HTTP_404_NOT_FOUND)
        
        # Generate and send OTP for password change
        OTPVerification.generate_otp(user, 'password_reset').send_otp_email()
        
        return Response({
            'message': 'OTP for password change has been sent to your email',
            'email': user.email
        }, status=status.HTTP_200_OK)


class TokenRefreshAPIView(APIView):
    """
    Custom token refresh view that handles refresh token validation
    and returns new access tokens
    """
    def post(self, request):
        try:
            refresh_token = request.data.get('refresh')

            if not refresh_token:
                return Response(
                    {'error': 'Refresh token is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate and refresh the token
            refresh = RefreshToken(refresh_token)
            access_token = str(refresh.access_token)

            # Get user information
            user = User.objects.get(id=refresh['user_id'])

            return Response({
                'success': True,
                'access_token': access_token,
                'refresh_token': str(refresh),  # Return the same refresh token
                'user': {
                    'id': user.id,
                    'name': user.name,
                    'username': user.user_name,
                    'email': user.email,
                    'role': user.role,
                    'is_verified': user.is_verified
                }
            }, status=status.HTTP_200_OK)

        except TokenError as e:
            return Response(
                {'error': 'Invalid or expired refresh token'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': 'Token refresh failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminDashboardStatsView(APIView):
    """
    Admin dashboard statistics endpoint
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get comprehensive admin dashboard statistics"""
        try:
            # Check if user is admin
            if request.user.role != 'admin':
                return Response({
                    'success': False,
                    'message': 'Admin access required'
                }, status=status.HTTP_403_FORBIDDEN)

            # Import models here to avoid circular imports
            from restaurant.models import Restaurant
            from orders.models import Order
            from deliveryAgent.models import DeliveryAgentProfile

            # Calculate date ranges
            today = timezone.now().date()
            yesterday = today - timedelta(days=1)
            week_ago = today - timedelta(days=7)
            month_ago = today - timedelta(days=30)

            # User statistics
            total_users = User.objects.count()
            new_users_today = User.objects.filter(date_joined__date=today).count()
            new_users_week = User.objects.filter(date_joined__date__gte=week_ago).count()
            new_users_month = User.objects.filter(date_joined__date__gte=month_ago).count()

            # Restaurant statistics
            total_restaurants = Restaurant.objects.count()
            active_restaurants = Restaurant.objects.filter(is_active=True).count()
            pending_restaurant_approvals = Restaurant.objects.filter(is_verified=False).count()
            new_restaurants_week = Restaurant.objects.filter(created_at__date__gte=week_ago).count()

            # Order statistics
            total_orders = Order.objects.count()
            orders_today = Order.objects.filter(created_at__date=today).count()
            orders_week = Order.objects.filter(created_at__date__gte=week_ago).count()
            orders_month = Order.objects.filter(created_at__date__gte=month_ago).count()

            # Order status breakdown
            pending_orders = Order.objects.filter(status='pending').count()
            confirmed_orders = Order.objects.filter(status='confirmed').count()
            ready_orders = Order.objects.filter(status='ready').count()
            assigned_orders = Order.objects.filter(status='assigned').count()
            picked_up_orders = Order.objects.filter(status='picked_up').count()
            delivered_orders = Order.objects.filter(status='delivered').count()
            cancelled_orders = Order.objects.filter(status='cancelled').count()

            # Revenue statistics
            total_revenue = Order.objects.filter(status='delivered').aggregate(
                total=Sum('total_amount')
            )['total'] or 0

            revenue_today = Order.objects.filter(
                status='delivered',
                created_at__date=today
            ).aggregate(total=Sum('total_amount'))['total'] or 0

            revenue_week = Order.objects.filter(
                status='delivered',
                created_at__date__gte=week_ago
            ).aggregate(total=Sum('total_amount'))['total'] or 0

            revenue_month = Order.objects.filter(
                status='delivered',
                created_at__date__gte=month_ago
            ).aggregate(total=Sum('total_amount'))['total'] or 0

            # Average order value
            avg_order_value = Order.objects.filter(status='delivered').aggregate(
                avg=Avg('total_amount')
            )['avg'] or 0

            # Delivery agent statistics
            total_delivery_agents = DeliveryAgentProfile.objects.count()
            active_delivery_agents = DeliveryAgentProfile.objects.filter(
                employment_status='active'
            ).count()
            pending_delivery_approvals = DeliveryAgentProfile.objects.filter(
                status='pending'
            ).count()
            online_delivery_agents = DeliveryAgentProfile.objects.filter(
                is_online=True,
                availability='available'
            ).count()

            # Calculate growth percentages
            prev_week_users = User.objects.filter(
                date_joined__date__gte=week_ago - timedelta(days=7),
                date_joined__date__lt=week_ago
            ).count()
            user_growth = ((new_users_week - prev_week_users) / max(prev_week_users, 1)) * 100 if prev_week_users > 0 else 0

            prev_week_orders = Order.objects.filter(
                created_at__date__gte=week_ago - timedelta(days=7),
                created_at__date__lt=week_ago
            ).count()
            order_growth = ((orders_week - prev_week_orders) / max(prev_week_orders, 1)) * 100 if prev_week_orders > 0 else 0

            prev_week_revenue = Order.objects.filter(
                status='delivered',
                created_at__date__gte=week_ago - timedelta(days=7),
                created_at__date__lt=week_ago
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            revenue_growth = ((revenue_week - prev_week_revenue) / max(prev_week_revenue, 1)) * 100 if prev_week_revenue > 0 else 0

            # System health metrics
            system_status = {
                'status': 'healthy',
                'last_checked': timezone.now().isoformat(),
                'uptime': '99.9%',
                'response_time': '120ms',
                'active_connections': total_users,
                'server_load': min(85, max(15, (orders_today * 2) + 15))  # Simulated load based on activity
            }

            dashboard_stats = {
                'users': {
                    'total': total_users,
                    'new_today': new_users_today,
                    'new_week': new_users_week,
                    'new_month': new_users_month,
                    'growth_percentage': round(user_growth, 1)
                },
                'restaurants': {
                    'total': total_restaurants,
                    'active': active_restaurants,
                    'pending_approvals': pending_restaurant_approvals,
                    'new_week': new_restaurants_week
                },
                'orders': {
                    'total': total_orders,
                    'today': orders_today,
                    'week': orders_week,
                    'month': orders_month,
                    'growth_percentage': round(order_growth, 1),
                    'status_breakdown': {
                        'pending': pending_orders,
                        'confirmed': confirmed_orders,
                        'ready': ready_orders,
                        'assigned': assigned_orders,
                        'picked_up': picked_up_orders,
                        'delivered': delivered_orders,
                        'cancelled': cancelled_orders
                    }
                },
                'revenue': {
                    'total': float(total_revenue),
                    'today': float(revenue_today),
                    'week': float(revenue_week),
                    'month': float(revenue_month),
                    'average_order_value': float(avg_order_value),
                    'growth_percentage': round(revenue_growth, 1)
                },
                'delivery_agents': {
                    'total': total_delivery_agents,
                    'active': active_delivery_agents,
                    'pending_approvals': pending_delivery_approvals,
                    'online': online_delivery_agents
                },
                'system_status': system_status,
                'last_updated': timezone.now().isoformat()
            }

            return Response({
                'success': True,
                'data': dashboard_stats
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Failed to fetch dashboard statistics: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
