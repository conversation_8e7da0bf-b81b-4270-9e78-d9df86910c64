from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    Notification, NotificationTemplate, NotificationDelivery,
    NotificationSettings, NotificationStatistics
)


class NotificationDeliveryInline(admin.TabularInline):
    model = NotificationDelivery
    extra = 0
    readonly_fields = ['status', 'sent_at', 'delivered_at', 'error_message']
    fields = ['recipient_email', 'channel', 'status', 'sent_at', 'delivered_at']


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'notification_type', 'status_badge', 'recipient_type',
        'total_recipients', 'delivery_rate_display', 'created_at'
    ]
    list_filter = [
        'notification_type', 'status', 'recipient_type', 'channels', 'created_at'
    ]
    search_fields = ['title', 'message', 'created_by__user_name']
    readonly_fields = [
        'id', 'total_recipients', 'successful_deliveries', 'failed_deliveries',
        'delivery_rate', 'created_at', 'updated_at'
    ]
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    list_per_page = 50

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'message', 'notification_type', 'template')
        }),
        ('Recipients', {
            'fields': ('recipient_type', 'recipient_users', 'recipient_roles', 'recipient_emails')
        }),
        ('Delivery', {
            'fields': ('channels', 'status', 'scheduled_at', 'sent_at', 'priority')
        }),
        ('Template Data', {
            'fields': ('context_data',),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': ('total_recipients', 'successful_deliveries', 'failed_deliveries', 'delivery_rate'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [NotificationDeliveryInline]

    def status_badge(self, obj):
        """Display status with color coding"""
        colors = {
            'draft': '#6c757d',
            'scheduled': '#17a2b8',
            'sending': '#ffc107',
            'sent': '#28a745',
            'delivered': '#007bff',
            'failed': '#dc3545',
            'cancelled': '#6f42c1'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; '
            'border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.status.upper()
        )
    status_badge.short_description = 'Status'

    def delivery_rate_display(self, obj):
        """Display delivery rate with color coding"""
        rate = obj.delivery_rate
        if rate >= 95:
            color = '#28a745'  # Green
        elif rate >= 80:
            color = '#ffc107'  # Yellow
        else:
            color = '#dc3545'  # Red

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, rate
        )
    delivery_rate_display.short_description = 'Delivery Rate'


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'template_type', 'supports_channels', 'is_active', 'created_at'
    ]
    list_filter = ['template_type', 'is_active', 'supports_email', 'supports_sms', 'supports_push']
    search_fields = ['name', 'title_template', 'message_template']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'template_type', 'is_active')
        }),
        ('Template Content', {
            'fields': ('title_template', 'message_template')
        }),
        ('Channel Support', {
            'fields': ('supports_email', 'supports_sms', 'supports_push', 'supports_in_app')
        }),
        ('Email Templates', {
            'fields': ('email_subject_template', 'email_html_template'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def supports_channels(self, obj):
        """Display supported channels"""
        channels = []
        if obj.supports_email:
            channels.append('📧 Email')
        if obj.supports_sms:
            channels.append('📱 SMS')
        if obj.supports_push:
            channels.append('🔔 Push')
        if obj.supports_in_app:
            channels.append('📱 In-App')

        return ', '.join(channels) if channels else 'None'
    supports_channels.short_description = 'Supported Channels'


@admin.register(NotificationDelivery)
class NotificationDeliveryAdmin(admin.ModelAdmin):
    list_display = [
        'notification_title', 'recipient_email', 'channel', 'status_badge',
        'sent_at', 'delivered_at'
    ]
    list_filter = ['channel', 'status', 'sent_at']
    search_fields = ['notification__title', 'recipient_email', 'recipient_user__user_name']
    readonly_fields = [
        'id', 'sent_at', 'delivered_at', 'opened_at', 'clicked_at',
        'created_at', 'updated_at'
    ]
    date_hierarchy = 'created_at'
    ordering = ['-created_at']

    def notification_title(self, obj):
        return obj.notification.title
    notification_title.short_description = 'Notification'

    def status_badge(self, obj):
        """Display status with color coding"""
        colors = {
            'pending': '#6c757d',
            'sent': '#17a2b8',
            'delivered': '#28a745',
            'failed': '#dc3545',
            'bounced': '#fd7e14',
            'opened': '#007bff',
            'clicked': '#6f42c1'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; '
            'border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.status.upper()
        )
    status_badge.short_description = 'Status'


@admin.register(NotificationSettings)
class NotificationSettingsAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'email_enabled', 'sms_enabled', 'push_enabled', 'updated_at']
    readonly_fields = ['updated_at']

    fieldsets = (
        ('Email Settings', {
            'fields': (
                'email_enabled', 'email_from_name', 'email_from_address',
                'email_reply_to'
            )
        }),
        ('SMS Settings', {
            'fields': (
                'sms_enabled', 'sms_provider', 'sms_api_key',
                'sms_api_secret', 'sms_from_number'
            ),
            'classes': ('collapse',)
        }),
        ('Push Notification Settings', {
            'fields': (
                'push_enabled', 'push_provider', 'push_api_key', 'push_server_key'
            ),
            'classes': ('collapse',)
        }),
        ('Rate Limiting', {
            'fields': (
                'max_emails_per_hour', 'max_sms_per_hour', 'max_push_per_hour'
            ),
            'classes': ('collapse',)
        }),
        ('Retry Settings', {
            'fields': ('max_retry_attempts', 'retry_delay_minutes'),
            'classes': ('collapse',)
        }),
        ('Templates', {
            'fields': ('default_email_template',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('updated_by', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(NotificationStatistics)
class NotificationStatisticsAdmin(admin.ModelAdmin):
    list_display = [
        'date', 'total_sent', 'total_delivered', 'total_failed',
        'delivery_rate_display', 'email_delivery_rate_display'
    ]
    list_filter = ['date']
    readonly_fields = [
        'date', 'order_notifications', 'user_notifications', 'restaurant_notifications',
        'delivery_notifications', 'system_notifications', 'email_sent', 'email_delivered',
        'email_failed', 'sms_sent', 'sms_delivered', 'sms_failed', 'push_sent',
        'push_delivered', 'push_failed', 'total_sent', 'total_delivered', 'total_failed',
        'delivery_rate', 'email_delivery_rate', 'created_at', 'updated_at'
    ]
    ordering = ['-date']

    def delivery_rate_display(self, obj):
        """Display delivery rate with color coding"""
        rate = obj.delivery_rate
        if rate >= 95:
            color = '#28a745'  # Green
        elif rate >= 80:
            color = '#ffc107'  # Yellow
        else:
            color = '#dc3545'  # Red

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, rate
        )
    delivery_rate_display.short_description = 'Overall Rate'

    def email_delivery_rate_display(self, obj):
        """Display email delivery rate with color coding"""
        rate = obj.email_delivery_rate
        if rate >= 95:
            color = '#28a745'  # Green
        elif rate >= 80:
            color = '#ffc107'  # Yellow
        else:
            color = '#dc3545'  # Red

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, rate
        )
    email_delivery_rate_display.short_description = 'Email Rate'

    def has_add_permission(self, request):
        """Disable adding statistics through admin"""
        return False

    def has_change_permission(self, request, obj=None):
        """Disable editing statistics through admin"""
        return False
