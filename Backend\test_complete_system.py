#!/usr/bin/env python3
"""
Complete test of EVERY notification feature
"""

import os
import sys
import django
import json
import time

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()

def test_complete_system():
    """Test every single notification feature"""
    print("🚀 COMPLETE NOTIFICATION SYSTEM TEST")
    print("=" * 80)
    print("Testing: Templates, Settings, Sending, Filtering, Real-time")
    print("=" * 80)
    
    # Setup authentication
    admin_user = User.objects.filter(role='admin').first()
    client = Client()
    
    login_response = client.post('/api/auth/login/', 
                                json.dumps({
                                    'user_name': admin_user.user_name,
                                    'password': 'admin123'
                                }),
                                content_type='application/json')
    
    access_token = login_response.json().get('data', {}).get('access_token')
    headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
    
    test_results = []
    
    # ========================================
    # 1. TEST TEMPLATE CREATION
    # ========================================
    print("\n🎨 1. TESTING TEMPLATE CREATION")
    print("-" * 50)
    
    templates_to_create = [
        {
            'name': 'Order Confirmation',
            'template_type': 'order',
            'title_template': 'Order #{{order_id}} Confirmed',
            'message_template': 'Hello {{customer_name}}, your order from {{restaurant_name}} has been confirmed. Total: ${{total_amount}}.',
            'supports_email': True,
            'supports_sms': True,
            'supports_push': True
        },
        {
            'name': 'Welcome Message',
            'template_type': 'user',
            'title_template': 'Welcome {{user_name}}!',
            'message_template': 'Hi {{user_name}}, welcome to Afghan Sofra! Start exploring delicious food.',
            'supports_email': True,
            'supports_sms': False,
            'supports_push': True
        },
        {
            'name': 'Delivery Update',
            'template_type': 'delivery',
            'title_template': 'Delivery Update #{{order_id}}',
            'message_template': 'Your order #{{order_id}} is {{status}}. Driver: {{driver_name}}.',
            'supports_email': True,
            'supports_sms': True,
            'supports_push': True
        }
    ]
    
    created_templates = []
    
    for template_data in templates_to_create:
        print(f"   Creating: {template_data['name']}")
        
        create_response = client.post('/api/admin/notifications/templates/create/',
                                    json.dumps(template_data),
                                    content_type='application/json',
                                    **headers)
        
        if create_response.status_code == 200:
            template = create_response.json()['data']
            created_templates.append(template)
            print(f"   ✅ Created: {template['name']}")
            test_results.append(('Template Creation', 'PASS'))
        else:
            print(f"   ❌ Failed: {create_response.content}")
            test_results.append(('Template Creation', 'FAIL'))
    
    # ========================================
    # 2. TEST SETTINGS CONFIGURATION
    # ========================================
    print("\n⚙️ 2. TESTING SETTINGS CONFIGURATION")
    print("-" * 50)
    
    # Update all settings
    settings_update = {
        'email_enabled': True,
        'email_from_name': 'Afghan Sofra Notifications',
        'email_from_address': '<EMAIL>',
        'max_emails_per_hour': 2000,
        'sms_enabled': True,
        'sms_provider': 'twilio',
        'max_sms_per_hour': 500,
        'push_enabled': True,
        'push_provider': 'firebase',
        'max_push_per_hour': 5000,
        'max_retry_attempts': 3,
        'retry_delay_minutes': 5
    }
    
    print("   Updating notification settings...")
    update_response = client.patch('/api/admin/notifications/settings/',
                                 json.dumps(settings_update),
                                 content_type='application/json',
                                 **headers)
    
    if update_response.status_code == 200:
        print("   ✅ Settings updated successfully")
        test_results.append(('Settings Update', 'PASS'))
    else:
        print(f"   ❌ Settings update failed: {update_response.content}")
        test_results.append(('Settings Update', 'FAIL'))
    
    # Test settings retrieval
    print("   Retrieving updated settings...")
    get_response = client.get('/api/admin/notifications/settings/', **headers)
    if get_response.status_code == 200:
        settings = get_response.json()['data']
        print(f"   ✅ Retrieved settings: Email={settings.get('email_enabled')}, SMS={settings.get('sms_enabled')}")
        test_results.append(('Settings Retrieval', 'PASS'))
    else:
        print("   ❌ Failed to retrieve settings")
        test_results.append(('Settings Retrieval', 'FAIL'))
    
    # ========================================
    # 3. TEST DYNAMIC DATA ENDPOINTS
    # ========================================
    print("\n🔄 3. TESTING DYNAMIC DATA")
    print("-" * 50)
    
    # Test user search
    print("   Testing user search...")
    search_response = client.get('/api/admin/notifications/users/search/?q=admin&limit=5', **headers)
    if search_response.status_code == 200:
        users = search_response.json()['data']['users']
        print(f"   ✅ Found {len(users)} users")
        test_results.append(('User Search', 'PASS'))
    else:
        print("   ❌ User search failed")
        test_results.append(('User Search', 'FAIL'))
    
    # Test roles
    print("   Testing role retrieval...")
    roles_response = client.get('/api/admin/notifications/roles/', **headers)
    if roles_response.status_code == 200:
        roles = roles_response.json()['data']['roles']
        print(f"   ✅ Found {len(roles)} roles")
        test_results.append(('Role Retrieval', 'PASS'))
    else:
        print("   ❌ Role retrieval failed")
        test_results.append(('Role Retrieval', 'FAIL'))
    
    # Test notification types
    print("   Testing notification types...")
    types_response = client.get('/api/admin/notifications/types/', **headers)
    if types_response.status_code == 200:
        types = types_response.json()['data']['types']
        print(f"   ✅ Found {len(types)} notification types")
        test_results.append(('Notification Types', 'PASS'))
    else:
        print("   ❌ Notification types failed")
        test_results.append(('Notification Types', 'FAIL'))
    
    return test_results, created_templates, headers, client, admin_user


def test_notification_sending(test_results, created_templates, headers, client, admin_user):
    """Test notification sending with all variations"""
    print("\n📤 4. TESTING NOTIFICATION SENDING")
    print("-" * 50)
    
    # Test different notification scenarios
    notification_tests = [
        {
            'name': 'All Users - General',
            'data': {
                'title': 'System Announcement',
                'message': 'Welcome to our updated notification system!',
                'notification_type': 'general',
                'recipient_type': 'all',
                'channels': ['email'],
                'priority': 2
            }
        },
        {
            'name': 'Role-based - Admin Only',
            'data': {
                'title': 'Admin Alert',
                'message': 'System maintenance scheduled for tonight.',
                'notification_type': 'system',
                'recipient_type': 'role',
                'recipient_roles': ['admin'],
                'channels': ['email', 'push'],
                'priority': 3
            }
        },
        {
            'name': 'Specific User',
            'data': {
                'title': 'Personal Message',
                'message': 'This is a test message for you specifically.',
                'notification_type': 'user',
                'recipient_type': 'user',
                'recipient_users': [admin_user.id],
                'channels': ['email'],
                'priority': 1
            }
        },
        {
            'name': 'Custom Emails',
            'data': {
                'title': 'External Notification',
                'message': 'This goes to external email addresses.',
                'notification_type': 'marketing',
                'recipient_type': 'custom',
                'recipient_emails': ['<EMAIL>', '<EMAIL>'],
                'channels': ['email'],
                'priority': 1
            }
        },
        {
            'name': 'Multi-channel High Priority',
            'data': {
                'title': 'URGENT: System Alert',
                'message': 'Critical system issue detected. Please check immediately.',
                'notification_type': 'system',
                'recipient_type': 'role',
                'recipient_roles': ['admin'],
                'channels': ['email', 'sms', 'push'],
                'priority': 4
            }
        }
    ]
    
    sent_notifications = []
    
    for test in notification_tests:
        print(f"   Sending: {test['name']}")
        
        send_response = client.post('/api/admin/notifications/send/',
                                  json.dumps(test['data']),
                                  content_type='application/json',
                                  **headers)
        
        if send_response.status_code == 200:
            result = send_response.json()
            notification_id = result['data']['id']
            sent_notifications.append(notification_id)
            print(f"   ✅ Sent: {test['name']} (ID: {notification_id})")
            test_results.append((f"Send - {test['name']}", 'PASS'))
        else:
            print(f"   ❌ Failed: {test['name']} - {send_response.content}")
            test_results.append((f"Send - {test['name']}", 'FAIL'))
    
    return test_results, sent_notifications


def test_filtering_and_stats(test_results, headers, client):
    """Test filtering and statistics"""
    print("\n🔍 5. TESTING FILTERING & STATISTICS")
    print("-" * 50)
    
    # Test various filters
    filter_tests = [
        ('type=system', 'System Type Filter'),
        ('status=sent', 'Sent Status Filter'),
        ('priority=3', 'High Priority Filter'),
        ('search=test', 'Search Filter'),
        ('recipient_type=all', 'All Users Filter')
    ]
    
    for filter_param, description in filter_tests:
        print(f"   Testing: {description}")
        
        filter_response = client.get(f'/api/admin/notifications/?{filter_param}', **headers)
        if filter_response.status_code == 200:
            data = filter_response.json()
            count = len(data.get('results', []))
            print(f"   ✅ {description}: {count} results")
            test_results.append((f"Filter - {description}", 'PASS'))
        else:
            print(f"   ❌ {description} failed")
            test_results.append((f"Filter - {description}", 'FAIL'))
    
    # Test statistics
    print("\n   Testing statistics...")
    stats_response = client.get('/api/admin/notifications/stats/', **headers)
    if stats_response.status_code == 200:
        stats = stats_response.json()['data']
        print(f"   ✅ Stats: {stats.get('sent_today', 0)} sent today, {stats.get('delivery_rate', 0)}% delivery rate")
        test_results.append(('Statistics', 'PASS'))
    else:
        print("   ❌ Statistics failed")
        test_results.append(('Statistics', 'FAIL'))
    
    return test_results


def print_final_results(test_results):
    """Print comprehensive test results"""
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 80)
    
    total_tests = len(test_results)
    passed_tests = len([r for r in test_results if r[1] == 'PASS'])
    failed_tests = total_tests - passed_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    # Group results by category
    categories = {}
    for test_name, result in test_results:
        category = test_name.split(' - ')[0] if ' - ' in test_name else test_name.split()[0]
        if category not in categories:
            categories[category] = {'PASS': 0, 'FAIL': 0}
        categories[category][result] += 1
    
    print("\n📋 Results by Category:")
    for category, results in categories.items():
        total = results['PASS'] + results['FAIL']
        success_rate = (results['PASS'] / total * 100) if total > 0 else 0
        print(f"   {category}: {results['PASS']}/{total} ({success_rate:.1f}%)")
    
    if failed_tests > 0:
        print("\n❌ Failed Tests:")
        for test_name, result in test_results:
            if result == 'FAIL':
                print(f"   - {test_name}")
    
    print("\n" + "=" * 80)
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! NOTIFICATION SYSTEM IS PERFECT!")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    print("=" * 80)


if __name__ == '__main__':
    # Run all tests
    test_results, created_templates, headers, client, admin_user = test_complete_system()
    test_results, sent_notifications = test_notification_sending(test_results, created_templates, headers, client, admin_user)
    test_results = test_filtering_and_stats(test_results, headers, client)
    print_final_results(test_results)
