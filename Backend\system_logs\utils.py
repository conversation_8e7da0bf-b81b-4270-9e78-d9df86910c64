import logging
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import SystemLog

User = get_user_model()
logger = logging.getLogger(__name__)


def create_log(level, category, message, details=None, user=None, ip_address=None, 
               user_agent=None, source=None, request_id=None, session_id=None,
               response_time=None, extra_data=None):
    """
    Create a system log entry
    
    Args:
        level (str): Log level (debug, info, warning, error, critical)
        category (str): Log category (authentication, database, api, etc.)
        message (str): Log message
        details (str, optional): Additional details
        user (User, optional): User associated with the log
        ip_address (str, optional): IP address
        user_agent (str, optional): User agent string
        source (str, optional): Source of the log (file:line or module)
        request_id (str, optional): Request ID for tracing
        session_id (str, optional): Session ID
        response_time (float, optional): Response time in milliseconds
        extra_data (dict, optional): Additional data as JSON
    
    Returns:
        SystemLog: Created log instance
    """
    try:
        # Validate level
        valid_levels = [choice[0] for choice in SystemLog.LOG_LEVELS]
        if level not in valid_levels:
            level = 'info'
        
        # Validate category
        valid_categories = [choice[0] for choice in SystemLog.LOG_CATEGORIES]
        if category not in valid_categories:
            category = 'other'
        
        # Create log entry
        log_entry = SystemLog.objects.create(
            level=level,
            category=category,
            message=message,
            details=details,
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            source=source,
            request_id=request_id,
            session_id=session_id,
            response_time=response_time,
            extra_data=extra_data or {}
        )
        
        return log_entry
        
    except Exception as e:
        # Fallback to standard logging if database logging fails
        logger.error(f"Failed to create system log: {e}")
        logger.log(
            getattr(logging, level.upper(), logging.INFO),
            f"[{category}] {message} - {details}"
        )
        return None


def log_user_action(user, action, details=None, category='user_management', 
                   ip_address=None, request_id=None, extra_data=None):
    """
    Log user actions for audit trail
    
    Args:
        user (User): User performing the action
        action (str): Action description
        details (str, optional): Additional details
        category (str): Log category
        ip_address (str, optional): IP address
        request_id (str, optional): Request ID
        extra_data (dict, optional): Additional data
    """
    return create_log(
        level='info',
        category=category,
        message=f"User action: {action}",
        details=details,
        user=user,
        ip_address=ip_address,
        request_id=request_id,
        extra_data=extra_data
    )


def log_admin_action(admin_user, action, target=None, details=None, 
                    ip_address=None, request_id=None, extra_data=None):
    """
    Log admin actions for audit trail
    
    Args:
        admin_user (User): Admin user performing the action
        action (str): Action description
        target (str, optional): Target of the action
        details (str, optional): Additional details
        ip_address (str, optional): IP address
        request_id (str, optional): Request ID
        extra_data (dict, optional): Additional data
    """
    message = f"Admin action: {action}"
    if target:
        message += f" on {target}"
    
    return create_log(
        level='info',
        category='admin',
        message=message,
        details=details,
        user=admin_user,
        ip_address=ip_address,
        request_id=request_id,
        extra_data=extra_data
    )


def log_security_event(event_type, message, details=None, user=None, 
                      ip_address=None, level='warning', extra_data=None):
    """
    Log security-related events
    
    Args:
        event_type (str): Type of security event
        message (str): Event message
        details (str, optional): Additional details
        user (User, optional): User associated with the event
        ip_address (str, optional): IP address
        level (str): Log level (default: warning)
        extra_data (dict, optional): Additional data
    """
    return create_log(
        level=level,
        category='security',
        message=f"Security event: {event_type} - {message}",
        details=details,
        user=user,
        ip_address=ip_address,
        extra_data=extra_data
    )


def log_database_event(operation, table=None, details=None, user=None, 
                      level='info', extra_data=None):
    """
    Log database-related events
    
    Args:
        operation (str): Database operation
        table (str, optional): Table name
        details (str, optional): Additional details
        user (User, optional): User performing the operation
        level (str): Log level (default: info)
        extra_data (dict, optional): Additional data
    """
    message = f"Database operation: {operation}"
    if table:
        message += f" on {table}"
    
    return create_log(
        level=level,
        category='database',
        message=message,
        details=details,
        user=user,
        extra_data=extra_data
    )


def log_payment_event(event_type, amount=None, order_id=None, details=None, 
                     user=None, level='info', extra_data=None):
    """
    Log payment-related events
    
    Args:
        event_type (str): Type of payment event
        amount (Decimal, optional): Payment amount
        order_id (str, optional): Order ID
        details (str, optional): Additional details
        user (User, optional): User associated with the payment
        level (str): Log level (default: info)
        extra_data (dict, optional): Additional data
    """
    message = f"Payment event: {event_type}"
    if amount:
        message += f" - Amount: {amount}"
    if order_id:
        message += f" - Order: {order_id}"
    
    return create_log(
        level=level,
        category='payments',
        message=message,
        details=details,
        user=user,
        extra_data=extra_data
    )


def log_order_event(event_type, order_id, details=None, user=None, 
                   level='info', extra_data=None):
    """
    Log order-related events
    
    Args:
        event_type (str): Type of order event
        order_id (str): Order ID
        details (str, optional): Additional details
        user (User, optional): User associated with the order
        level (str): Log level (default: info)
        extra_data (dict, optional): Additional data
    """
    return create_log(
        level=level,
        category='orders',
        message=f"Order event: {event_type} - Order: {order_id}",
        details=details,
        user=user,
        extra_data=extra_data
    )
