from rest_framework import serializers
from .models import BackupJob, BackupSchedule, BackupSettings


class BackupJobSerializer(serializers.ModelSerializer):
    """Serializer for BackupJob model"""
    
    file_size_formatted = serializers.CharField(source='formatted_file_size', read_only=True)
    duration = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.name', read_only=True)
    
    class Meta:
        model = BackupJob
        fields = [
            'id', 'name', 'backup_type', 'status', 'progress',
            'file_path', 'file_size', 'file_size_formatted', 'storage_location',
            'created_by', 'created_by_name', 'created_at', 'started_at', 'completed_at',
            'options', 'retention_days', 'error_message', 'duration'
        ]
        read_only_fields = [
            'id', 'created_by', 'created_at', 'started_at', 'completed_at',
            'file_size', 'file_path', 'error_message', 'progress'
        ]
    
    def get_duration(self, obj):
        """Get backup duration in seconds"""
        if obj.duration:
            return obj.duration.total_seconds()
        return None


class BackupJobCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating backup jobs"""
    
    class Meta:
        model = BackupJob
        fields = [
            'name', 'backup_type', 'storage_location', 'options', 'retention_days'
        ]
    
    def validate_name(self, value):
        """Validate backup name"""
        if not value.strip():
            raise serializers.ValidationError("Backup name cannot be empty")
        return value.strip()
    
    def validate_retention_days(self, value):
        """Validate retention days"""
        if value < 1:
            raise serializers.ValidationError("Retention days must be at least 1")
        if value > 365:
            raise serializers.ValidationError("Retention days cannot exceed 365")
        return value


class BackupScheduleSerializer(serializers.ModelSerializer):
    """Serializer for BackupSchedule model"""
    
    created_by_name = serializers.CharField(source='created_by.name', read_only=True)
    
    class Meta:
        model = BackupSchedule
        fields = [
            'id', 'name', 'backup_type', 'frequency', 'time', 'is_active',
            'options', 'retention_days', 'storage_location',
            'created_by', 'created_by_name', 'created_at', 'last_run', 'next_run'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'last_run', 'next_run']


class BackupSettingsSerializer(serializers.ModelSerializer):
    """Serializer for BackupSettings model"""
    
    updated_by_name = serializers.CharField(source='updated_by.name', read_only=True)
    
    class Meta:
        model = BackupSettings
        fields = [
            'default_storage_location', 'max_backup_size_gb', 'backup_directory',
            'default_retention_days', 'auto_cleanup_enabled',
            'email_notifications', 'notification_emails',
            'max_concurrent_backups', 'compression_enabled',
            'updated_at', 'updated_by', 'updated_by_name'
        ]
        read_only_fields = ['updated_at', 'updated_by', 'updated_by_name']


class BackupStatsSerializer(serializers.Serializer):
    """Serializer for backup statistics"""
    
    total_backups = serializers.IntegerField()
    successful_backups = serializers.IntegerField()
    failed_backups = serializers.IntegerField()
    pending_backups = serializers.IntegerField()
    total_storage_used = serializers.CharField()
    last_backup_date = serializers.DateTimeField(allow_null=True)
    success_rate = serializers.FloatField()


class BackupProgressSerializer(serializers.Serializer):
    """Serializer for backup progress updates"""
    
    progress = serializers.IntegerField(min_value=0, max_value=100)
    status = serializers.ChoiceField(choices=BackupJob.STATUS_CHOICES)
    message = serializers.CharField(required=False, allow_blank=True)
