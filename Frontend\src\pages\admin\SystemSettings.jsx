import React, { useState, useEffect } from "react";
import {
  Settings,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Edit3,
  Eye,
  EyeOff,
  Globe,
  Truck,
  DollarSign,
  Bell,
  Shield,
  Palette,
  Database,
  Mail,
  Phone,
  Clock,
  MapPin,
  CreditCard,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { adminApi } from "../../services/adminApi";

const SystemSettings = () => {
  const [loading, setLoading] = useState(false);
  const [editingSettings, setEditingSettings] = useState({});
  const [saving, setSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState(null);
  const [settings, setSettings] = useState([]);

  const categoryIcons = {
    general: Globe,
    delivery: Truck,
    business: DollarSign,
    notification: Bell,
    security: Shield,
    appearance: Palette,
    database: Database,
    contact: Mail,
    payment: CreditCard,
  };

  // Mock settings data
  const mockSettings = [
    {
      id: 1,
      key: "site_name",
      name: "Site Name",
      description: "The name of your food delivery platform",
      value: "Afghan Sufra",
      typed_value: "Afghan Sufra",
      setting_type: "string",
      category: "general",
      is_public: true,
      is_editable: true,
    },
    {
      id: 2,
      key: "site_description",
      name: "Site Description",
      description: "Brief description of your platform",
      value: "Authentic Afghan cuisine delivered to your door",
      typed_value: "Authentic Afghan cuisine delivered to your door",
      setting_type: "text",
      category: "general",
      is_public: true,
      is_editable: true,
    },
    {
      id: 3,
      key: "default_delivery_fee",
      name: "Default Delivery Fee",
      description: "Standard delivery fee in USD",
      value: "2.99",
      typed_value: 2.99,
      setting_type: "decimal",
      category: "delivery",
      is_public: true,
      is_editable: true,
    },
    {
      id: 4,
      key: "free_delivery_threshold",
      name: "Free Delivery Threshold",
      description: "Minimum order amount for free delivery",
      value: "25.00",
      typed_value: 25.0,
      setting_type: "decimal",
      category: "delivery",
      is_public: true,
      is_editable: true,
    },
    {
      id: 5,
      key: "estimated_delivery_time",
      name: "Estimated Delivery Time",
      description: "Average delivery time in minutes",
      value: "30",
      typed_value: 30,
      setting_type: "integer",
      category: "delivery",
      is_public: true,
      is_editable: true,
    },
    {
      id: 6,
      key: "tax_rate",
      name: "Tax Rate",
      description: "Tax rate percentage",
      value: "8.5",
      typed_value: 8.5,
      setting_type: "decimal",
      category: "business",
      is_public: true,
      is_editable: true,
    },
    {
      id: 7,
      key: "contact_email",
      name: "Contact Email",
      description: "Primary contact email address",
      value: "<EMAIL>",
      typed_value: "<EMAIL>",
      setting_type: "string",
      category: "contact",
      is_public: true,
      is_editable: true,
    },
    {
      id: 8,
      key: "contact_phone",
      name: "Contact Phone",
      description: "Primary contact phone number",
      value: "+****************",
      typed_value: "+****************",
      setting_type: "string",
      category: "contact",
      is_public: true,
      is_editable: true,
    },
    {
      id: 9,
      key: "email_notifications",
      name: "Email Notifications",
      description: "Enable email notifications for orders",
      value: "true",
      typed_value: true,
      setting_type: "boolean",
      category: "notification",
      is_public: false,
      is_editable: true,
    },
    {
      id: 10,
      key: "maintenance_mode",
      name: "Maintenance Mode",
      description: "Enable maintenance mode to disable ordering",
      value: "false",
      typed_value: false,
      setting_type: "boolean",
      category: "general",
      is_public: false,
      is_editable: true,
    },
  ];

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const result = await adminApi.getSystemSettings();
      if (result.success) {
        setSettings(result.data.settings || []);
      } else {
        console.error("Failed to load settings:", result.error);
        setSaveStatus({
          type: "error",
          message: "Failed to load system settings. Using fallback data.",
        });
        // Fallback to mock data if API fails
        setSettings(mockSettings);
      }
    } catch (error) {
      console.error("Error loading settings:", error);
      setSaveStatus({
        type: "error",
        message: "Error loading system settings. Using fallback data.",
      });
      // Fallback to mock data if API fails
      setSettings(mockSettings);
    } finally {
      setLoading(false);
    }
  };

  // Group settings by category
  const groupedSettings = settings.reduce((groups, setting) => {
    const category = setting.category || "general";
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(setting);
    return groups;
  }, {});

  const handleEditSetting = (settingId, currentValue) => {
    setEditingSettings((prev) => ({
      ...prev,
      [settingId]: currentValue,
    }));
  };

  const handleCancelEdit = (settingId) => {
    setEditingSettings((prev) => {
      const newState = { ...prev };
      delete newState[settingId];
      return newState;
    });
  };

  const handleSaveSetting = async (settingId) => {
    const newValue = editingSettings[settingId];
    if (newValue === undefined) return;

    setSaving(true);
    setSaveStatus(null);

    try {
      // Call the real API
      const result = await adminApi.updateSystemSetting(settingId, newValue);

      if (result.success) {
        // Update the setting in local state
        setSettings((prev) =>
          prev.map((setting) =>
            setting.id === settingId
              ? {
                  ...setting,
                  typed_value: newValue,
                  value: newValue.toString(),
                }
              : setting
          )
        );

        setSaveStatus({
          type: "success",
          message: "Setting updated successfully!",
        });
        handleCancelEdit(settingId);
      } else {
        setSaveStatus({
          type: "error",
          message:
            result.error?.message ||
            "Failed to save setting. Please try again.",
        });
      }

      // Clear status after delay
      setTimeout(() => {
        setSaveStatus(null);
      }, 3000);
    } catch (error) {
      console.error("Error saving setting:", error);
      setSaveStatus({
        type: "error",
        message: "Failed to save setting. Please try again.",
      });
    } finally {
      setSaving(false);
    }
  };

  const renderSettingValue = (setting) => {
    const isEditing = editingSettings.hasOwnProperty(setting.id);
    const currentValue = isEditing
      ? editingSettings[setting.id]
      : setting.typed_value;

    if (isEditing) {
      return (
        <div className='flex items-center space-x-2 flex-wrap'>
          {setting.setting_type === "boolean" ? (
            <select
              value={currentValue.toString()}
              onChange={(e) =>
                setEditingSettings((prev) => ({
                  ...prev,
                  [setting.id]: e.target.value === "true",
                }))
              }
              className='px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary-500'
            >
              <option value='true'>Enabled</option>
              <option value='false'>Disabled</option>
            </select>
          ) : setting.setting_type === "text" ? (
            <textarea
              value={currentValue}
              onChange={(e) =>
                setEditingSettings((prev) => ({
                  ...prev,
                  [setting.id]: e.target.value,
                }))
              }
              className='px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 min-w-48'
              rows={2}
            />
          ) : (
            <input
              type={
                setting.setting_type === "integer" ||
                setting.setting_type === "decimal"
                  ? "number"
                  : "text"
              }
              value={currentValue}
              onChange={(e) =>
                setEditingSettings((prev) => ({
                  ...prev,
                  [setting.id]: e.target.value,
                }))
              }
              className='px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 min-w-48'
              step={setting.setting_type === "decimal" ? "0.01" : "1"}
            />
          )}
          <Button
            size='small'
            onClick={() => handleSaveSetting(setting.id)}
            disabled={saving}
          >
            {saving ? (
              <RefreshCw size={14} className='animate-spin' />
            ) : (
              <Save size={14} />
            )}
          </Button>
          <Button
            size='small'
            variant='outline'
            onClick={() => handleCancelEdit(setting.id)}
            disabled={saving}
          >
            Cancel
          </Button>
        </div>
      );
    }

    return (
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-2'>
          <span className='font-medium'>
            {setting.setting_type === "boolean"
              ? currentValue
                ? "Enabled"
                : "Disabled"
              : currentValue?.toString()}
          </span>
          <Badge
            variant={setting.is_public ? "success" : "secondary"}
            size='small'
          >
            {setting.is_public ? (
              <>
                <Eye size={10} className='mr-1' />
                Public
              </>
            ) : (
              <>
                <EyeOff size={10} className='mr-1' />
                Private
              </>
            )}
          </Badge>
        </div>
        {setting.is_editable && (
          <Button
            size='small'
            variant='outline'
            onClick={() => handleEditSetting(setting.id, currentValue)}
          >
            <Edit3 size={14} />
          </Button>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className='p-6'>
        <div className='flex items-center justify-center'>
          <RefreshCw className='animate-spin mr-2' size={20} />
          <span>Loading system settings...</span>
        </div>
      </div>
    );
  }

  return (
    <div className='p-6 space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center'>
          <Settings className='text-primary-500 mr-3' size={28} />
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>
              System Settings
            </h1>
            <p className='text-gray-600'>
              Manage system-wide configuration and settings
            </p>
          </div>
        </div>
        <div className='flex items-center space-x-3'>
          {saveStatus && (
            <div
              className={`flex items-center px-3 py-2 rounded-lg ${
                saveStatus.type === "success"
                  ? "bg-green-100 text-green-700"
                  : "bg-red-100 text-red-700"
              }`}
            >
              {saveStatus.type === "success" ? (
                <CheckCircle size={16} className='mr-2' />
              ) : (
                <AlertCircle size={16} className='mr-2' />
              )}
              {saveStatus.message}
            </div>
          )}
          <Button onClick={loadSettings} variant='outline' disabled={loading}>
            <RefreshCw
              size={16}
              className={`mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Settings by Category */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {Object.entries(groupedSettings).map(([category, categorySettings]) => {
          const IconComponent = categoryIcons[category] || Settings;

          return (
            <Card key={category} className='p-6'>
              <div className='flex items-center mb-4'>
                <div className='p-2 rounded-lg bg-blue-100 mr-3'>
                  <IconComponent className='text-blue-600' size={20} />
                </div>
                <div>
                  <h2 className='text-lg font-semibold capitalize'>
                    {category}
                  </h2>
                  <p className='text-sm text-gray-600'>
                    {categorySettings.length} settings
                  </p>
                </div>
              </div>

              <div className='space-y-4'>
                {categorySettings.map((setting) => (
                  <div
                    key={setting.id}
                    className='border-b border-gray-100 pb-4 last:border-b-0 last:pb-0'
                  >
                    <div className='mb-2'>
                      <h3 className='font-medium text-gray-900'>
                        {setting.name}
                      </h3>
                      {setting.description && (
                        <p className='text-sm text-gray-600'>
                          {setting.description}
                        </p>
                      )}
                    </div>
                    {renderSettingValue(setting)}
                  </div>
                ))}
              </div>
            </Card>
          );
        })}
      </div>

      {/* Summary */}
      <Card className='p-6 bg-blue-50'>
        <h3 className='font-semibold text-blue-800 mb-4'>Settings Summary</h3>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
          <div className='text-center'>
            <div className='text-2xl font-bold text-blue-600'>
              {settings.length}
            </div>
            <div className='text-sm text-blue-700'>Total Settings</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-green-600'>
              {settings.filter((s) => s.is_public).length}
            </div>
            <div className='text-sm text-green-700'>Public Settings</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-purple-600'>
              {Object.keys(groupedSettings).length}
            </div>
            <div className='text-sm text-purple-700'>Categories</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-orange-600'>
              {settings.filter((s) => s.is_editable).length}
            </div>
            <div className='text-sm text-orange-700'>Editable</div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SystemSettings;
