from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
import uuid
import json

User = get_user_model()


class NotificationTemplate(models.Model):
    """
    Model for reusable notification templates
    """
    TEMPLATE_TYPES = [
        ('order', 'Order'),
        ('user', 'User'),
        ('restaurant', 'Restaurant'),
        ('delivery', 'Delivery'),
        ('payment', 'Payment'),
        ('system', 'System'),
        ('marketing', 'Marketing'),
        ('general', 'General'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)

    # Template content
    title_template = models.CharField(max_length=200)
    message_template = models.TextField()

    # Supported channels
    supports_email = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    supports_sms = models.BooleanField(default=False)
    supports_push = models.BooleanField(default=True)
    supports_in_app = models.BooleanField(default=True)

    # Email specific templates
    email_subject_template = models.CharField(max_length=200, blank=True, null=True)
    email_html_template = models.TextField(blank=True, null=True)

    # Metadata
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Notification Template'
        verbose_name_plural = 'Notification Templates'

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def render_title(self, context=None):
        """Render title with context variables"""
        if not context:
            return self.title_template

        try:
            return self.title_template.format(**context)
        except (KeyError, ValueError):
            return self.title_template

    def render_message(self, context=None):
        """Render message with context variables"""
        if not context:
            return self.message_template

        try:
            return self.message_template.format(**context)
        except (KeyError, ValueError):
            return self.message_template


class Notification(models.Model):
    """
    Model for individual notifications
    """
    NOTIFICATION_TYPES = [
        ('order', 'Order'),
        ('user', 'User'),
        ('restaurant', 'Restaurant'),
        ('delivery', 'Delivery'),
        ('payment', 'Payment'),
        ('system', 'System'),
        ('marketing', 'Marketing'),
        ('general', 'General'),
    ]

    RECIPIENT_TYPES = [
        ('user', 'Specific User'),
        ('role', 'User Role'),
        ('all', 'All Users'),
        ('custom', 'Custom List'),
    ]

    CHANNELS = [
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('push', 'Push Notification'),
        ('in_app', 'In-App Notification'),
    ]

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('scheduled', 'Scheduled'),
        ('sending', 'Sending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    # Primary fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)

    # Recipients
    recipient_type = models.CharField(max_length=20, choices=RECIPIENT_TYPES)
    recipient_users = models.ManyToManyField(User, blank=True, related_name='received_notifications')
    recipient_roles = models.JSONField(default=list, blank=True)  # List of roles
    recipient_emails = models.JSONField(default=list, blank=True)  # Custom email list

    # Delivery channels
    channels = models.JSONField(default=list)  # List of channels to use

    # Template reference
    template = models.ForeignKey(
        NotificationTemplate,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='notifications'
    )

    # Status and timing
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    scheduled_at = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)

    # Metadata
    context_data = models.JSONField(default=dict, blank=True)  # Variables for template rendering
    priority = models.IntegerField(default=1)  # 1=low, 2=medium, 3=high, 4=urgent

    # Tracking
    total_recipients = models.IntegerField(default=0)
    successful_deliveries = models.IntegerField(default=0)
    failed_deliveries = models.IntegerField(default=0)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_notifications')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['notification_type', 'created_at']),
            models.Index(fields=['scheduled_at']),
        ]
        verbose_name = 'Notification'
        verbose_name_plural = 'Notifications'

    def __str__(self):
        return f"{self.title} - {self.status}"

    @property
    def delivery_rate(self):
        """Calculate delivery success rate"""
        if self.total_recipients == 0:
            return 0
        return (self.successful_deliveries / self.total_recipients) * 100

    @property
    def is_scheduled(self):
        """Check if notification is scheduled for future"""
        return self.scheduled_at and self.scheduled_at > timezone.now()

    def get_recipients_display(self):
        """Get human-readable recipients description"""
        if self.recipient_type == 'all':
            return 'All Users'
        elif self.recipient_type == 'role':
            roles = ', '.join(self.recipient_roles) if self.recipient_roles else 'No roles'
            return f'Users with roles: {roles}'
        elif self.recipient_type == 'user':
            count = self.recipient_users.count()
            return f'{count} specific user(s)'
        elif self.recipient_type == 'custom':
            count = len(self.recipient_emails) if self.recipient_emails else 0
            return f'{count} custom email(s)'
        return 'Unknown'


class NotificationDelivery(models.Model):
    """
    Model to track individual notification deliveries
    """
    DELIVERY_STATUS = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('bounced', 'Bounced'),
        ('opened', 'Opened'),
        ('clicked', 'Clicked'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE, related_name='deliveries')

    # Recipient info
    recipient_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    recipient_email = models.EmailField()
    recipient_phone = models.CharField(max_length=20, blank=True, null=True)

    # Delivery details
    channel = models.CharField(max_length=20, choices=Notification.CHANNELS)
    status = models.CharField(max_length=20, choices=DELIVERY_STATUS, default='pending')

    # Timing
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    opened_at = models.DateTimeField(null=True, blank=True)
    clicked_at = models.DateTimeField(null=True, blank=True)

    # Error tracking
    error_message = models.TextField(blank=True, null=True)
    retry_count = models.IntegerField(default=0)

    # External tracking
    external_id = models.CharField(max_length=200, blank=True, null=True)  # Provider message ID

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['notification', 'status']),
            models.Index(fields=['recipient_user', 'created_at']),
            models.Index(fields=['channel', 'status']),
        ]
        verbose_name = 'Notification Delivery'
        verbose_name_plural = 'Notification Deliveries'

    def __str__(self):
        return f"{self.notification.title} -> {self.recipient_email} ({self.channel})"


class NotificationSettings(models.Model):
    """
    Model for notification system settings
    """
    # Email settings
    email_enabled = models.BooleanField(default=True)
    email_from_name = models.CharField(max_length=100, default='Afghan Sofra')
    email_from_address = models.EmailField(default='<EMAIL>')
    email_reply_to = models.EmailField(blank=True, null=True)

    # SMS settings
    sms_enabled = models.BooleanField(default=False)
    sms_provider = models.CharField(max_length=50, default='twilio')
    sms_api_key = models.CharField(max_length=200, blank=True, null=True)
    sms_api_secret = models.CharField(max_length=200, blank=True, null=True)
    sms_from_number = models.CharField(max_length=20, blank=True, null=True)

    # Push notification settings
    push_enabled = models.BooleanField(default=True)
    push_provider = models.CharField(max_length=50, default='firebase')
    push_api_key = models.CharField(max_length=500, blank=True, null=True)
    push_server_key = models.CharField(max_length=500, blank=True, null=True)

    # Rate limiting
    max_emails_per_hour = models.IntegerField(default=1000)
    max_sms_per_hour = models.IntegerField(default=100)
    max_push_per_hour = models.IntegerField(default=5000)

    # Retry settings
    max_retry_attempts = models.IntegerField(default=3)
    retry_delay_minutes = models.IntegerField(default=5)

    # Template settings
    default_email_template = models.ForeignKey(
        NotificationTemplate,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='default_email_settings'
    )

    # Metadata
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Notification Settings'
        verbose_name_plural = 'Notification Settings'

    def __str__(self):
        return f"Notification Settings (Updated: {self.updated_at})"

    @classmethod
    def get_settings(cls):
        """Get current notification settings (singleton pattern)"""
        settings, created = cls.objects.get_or_create(
            id=1,
            defaults={
                'updated_by_id': 1,  # Assuming admin user exists
            }
        )
        return settings


class NotificationStatistics(models.Model):
    """
    Model for daily notification statistics
    """
    date = models.DateField(unique=True, db_index=True)

    # Counts by type
    order_notifications = models.IntegerField(default=0)
    user_notifications = models.IntegerField(default=0)
    restaurant_notifications = models.IntegerField(default=0)
    delivery_notifications = models.IntegerField(default=0)
    system_notifications = models.IntegerField(default=0)

    # Counts by channel
    email_sent = models.IntegerField(default=0)
    email_delivered = models.IntegerField(default=0)
    email_failed = models.IntegerField(default=0)

    sms_sent = models.IntegerField(default=0)
    sms_delivered = models.IntegerField(default=0)
    sms_failed = models.IntegerField(default=0)

    push_sent = models.IntegerField(default=0)
    push_delivered = models.IntegerField(default=0)
    push_failed = models.IntegerField(default=0)

    # Total counts
    total_sent = models.IntegerField(default=0)
    total_delivered = models.IntegerField(default=0)
    total_failed = models.IntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date']
        verbose_name = 'Notification Statistics'
        verbose_name_plural = 'Notification Statistics'

    def __str__(self):
        return f"Notification Stats for {self.date} - {self.total_sent} sent"

    @property
    def delivery_rate(self):
        """Calculate overall delivery rate"""
        if self.total_sent == 0:
            return 0
        return (self.total_delivered / self.total_sent) * 100

    @property
    def email_delivery_rate(self):
        """Calculate email delivery rate"""
        if self.email_sent == 0:
            return 0
        return (self.email_delivered / self.email_sent) * 100
