import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { adminApi } from "../../services/adminApi";
import {
  Users,
  ShoppingBag,
  Home,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  Package,
  BarChart2,
  Store,
  Building2,
  Truck,
  Star,
  Eye,
  ArrowRight,
  Calendar,
  Activity,
  Bell,
  Settings,
  RefreshCw,
  Download,
  Filter,
  Search,
  MapPin,
  Phone,
  Mail,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Shield,
  Globe,
  Database,
  Server,
  Wifi,
} from "lucide-react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";
import { mockOrders } from "../../data/orders";
import { mockRestaurants } from "../../data/restaurants";
import { mockUsers } from "../../data/users";

// Chart data
const revenueData = [
  { name: "Jan", revenue: 4200, orders: 120, users: 45 },
  { name: "Feb", revenue: 5100, orders: 145, users: 52 },
  { name: "Mar", revenue: 6300, orders: 180, users: 68 },
  { name: "Apr", revenue: 7800, orders: 220, users: 75 },
  { name: "May", revenue: 6900, orders: 195, users: 82 },
  { name: "Jun", revenue: 8500, orders: 240, users: 91 },
  { name: "Jul", revenue: 9200, orders: 265, users: 98 },
  { name: "Aug", revenue: 10100, orders: 290, users: 105 },
  { name: "Sep", revenue: 11500, orders: 320, users: 112 },
  { name: "Oct", revenue: 10800, orders: 305, users: 118 },
  { name: "Nov", revenue: 12200, orders: 340, users: 125 },
  { name: "Dec", revenue: 15800, orders: 420, users: 135 },
];

// Dynamic data will be calculated from API response

function AdminDashboard() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState("month");

  const [metrics, setMetrics] = useState({
    totalUsers: 0,
    totalRestaurants: 0,
    totalOrders: 0,
    totalRevenue: 0,
    pendingApprovals: 0,
    pendingDeliveryApprovals: 0,
    activeDeliveryAgents: 0,
    todayOrders: 0,
    todayRevenue: 0,
    avgOrderValue: 0,
    customerSatisfaction: 0,
    monthlyGrowth: {
      users: 12.5,
      restaurants: 8.3,
      orders: 15.7,
      revenue: 18.2,
    },
  });

  const [recentActivities, setRecentActivities] = useState([]);
  const [recentOrders, setRecentOrders] = useState([]);
  const [pendingRestaurants, setPendingRestaurants] = useState([]);
  const [topRestaurants, setTopRestaurants] = useState([]);
  const [systemStatus, setSystemStatus] = useState({
    status: "healthy",
    lastChecked: new Date(),
    issues: [],
    uptime: "99.9%",
    responseTime: "120ms",
    activeConnections: 1247,
    serverLoad: 45,
  });

  const [notifications, setNotifications] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [orderStatusData, setOrderStatusData] = useState([]);
  const [topRestaurantsData, setTopRestaurantsData] = useState([]);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState("connecting"); // connecting, connected, error

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load comprehensive dashboard statistics
      const dashboardStats = await adminApi.getDashboardStats();

      if (dashboardStats.success) {
        const data = dashboardStats.data;

        setMetrics({
          totalUsers: data.users.total,
          totalRestaurants: data.restaurants.total,
          totalOrders: data.orders.total,
          totalRevenue: data.revenue.total,
          pendingApprovals: data.restaurants.pending_approvals,
          pendingDeliveryApprovals: data.delivery_agents.pending_approvals,
          activeDeliveryAgents: data.delivery_agents.active,
          todayOrders: data.orders.today,
          todayRevenue: data.revenue.today,
          avgOrderValue: data.revenue.average_order_value,
          customerSatisfaction: 4.7, // Keep this as static for now
          monthlyGrowth: {
            users: data.users.growth_percentage,
            restaurants: 8.3, // Keep static for now
            orders: data.orders.growth_percentage,
            revenue: data.revenue.growth_percentage,
          },
        });

        // Update system status
        setSystemStatus(data.system_status);

        // Update last updated timestamp
        setLastUpdated(new Date());

        // Update connection status
        setConnectionStatus("connected");

        // Calculate order status data for pie chart
        const statusBreakdown = data.orders.status_breakdown;
        const totalOrders = Object.values(statusBreakdown).reduce(
          (sum, count) => sum + count,
          0
        );

        if (totalOrders > 0) {
          setOrderStatusData([
            {
              name: "Delivered",
              value: Math.round(
                (statusBreakdown.delivered / totalOrders) * 100
              ),
              color: "#10B981",
            },
            {
              name: "In Progress",
              value: Math.round(
                ((statusBreakdown.assigned + statusBreakdown.picked_up) /
                  totalOrders) *
                  100
              ),
              color: "#F59E0B",
            },
            {
              name: "Cancelled",
              value: Math.round(
                (statusBreakdown.cancelled / totalOrders) * 100
              ),
              color: "#EF4444",
            },
            {
              name: "Pending",
              value: Math.round(
                ((statusBreakdown.pending +
                  statusBreakdown.confirmed +
                  statusBreakdown.ready) /
                  totalOrders) *
                  100
              ),
              color: "#6B7280",
            },
          ]);
        }

        // Set default top restaurants data (can be enhanced later with real data)
        if (data.orders.total > 0) {
          setTopRestaurantsData([
            {
              name: "Top Restaurant 1",
              orders: Math.floor(data.orders.total * 0.15),
              revenue: Math.floor(data.revenue.total * 0.18),
            },
            {
              name: "Top Restaurant 2",
              orders: Math.floor(data.orders.total * 0.12),
              revenue: Math.floor(data.revenue.total * 0.15),
            },
            {
              name: "Top Restaurant 3",
              orders: Math.floor(data.orders.total * 0.1),
              revenue: Math.floor(data.revenue.total * 0.12),
            },
          ]);
        } else {
          setTopRestaurantsData([]);
        }
      } else {
        console.error("Failed to load dashboard stats:", dashboardStats.error);

        // Update connection status
        setConnectionStatus("error");

        // Show user-friendly error message
        if (dashboardStats.error?.message?.includes("Authentication")) {
          // Redirect to login if authentication failed
          navigate("/login");
          return;
        }

        // Fallback to default values for other errors
        setMetrics({
          totalUsers: 0,
          totalRestaurants: 0,
          totalOrders: 0,
          totalRevenue: 0,
          pendingApprovals: 0,
          pendingDeliveryApprovals: 0,
          activeDeliveryAgents: 0,
          todayOrders: 0,
          todayRevenue: 0,
          avgOrderValue: 0,
          customerSatisfaction: 0,
          monthlyGrowth: {
            users: 0,
            restaurants: 0,
            orders: 0,
            revenue: 0,
          },
        });

        // Set empty data for charts
        setOrderStatusData([]);
        setTopRestaurantsData([]);
      }

      setRecentActivities([
        {
          id: 1,
          type: "order",
          message: "New order #ORD-8421 from Ahmad Khan",
          time: "2 minutes ago",
          icon: <ShoppingBag size={16} />,
          color: "text-blue-600",
          amount: "$24.50",
        },
        {
          id: 2,
          type: "restaurant",
          message: "Kabul Kitchen updated their menu",
          time: "15 minutes ago",
          icon: <Store size={16} />,
          color: "text-green-600",
        },
        {
          id: 3,
          type: "user",
          message: "12 new users registered today",
          time: "1 hour ago",
          icon: <Users size={16} />,
          color: "text-purple-600",
        },
        {
          id: 4,
          type: "delivery",
          message: "Delivery agent Mohammad completed 15 deliveries",
          time: "2 hours ago",
          icon: <Truck size={16} />,
          color: "text-orange-600",
        },
        {
          id: 5,
          type: "approval",
          message: "Restaurant 'Herat Delights' approved",
          time: "3 hours ago",
          icon: <CheckCircle size={16} />,
          color: "text-green-600",
        },
        {
          id: 6,
          type: "system",
          message: "System backup completed successfully",
          time: "4 hours ago",
          icon: <Database size={16} />,
          color: "text-gray-600",
        },
      ]);

      setSystemStatus({
        status: "healthy",
        lastChecked: new Date(),
        issues: [],
        uptime: "99.9%",
        responseTime: "120ms",
        activeConnections: 1247,
        serverLoad: 45,
      });

      setLoading(false);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      setLoading(false);
    }
  };

  // Refresh function
  const refreshDashboard = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  useEffect(() => {
    loadDashboardData();

    // Auto-refresh every 5 minutes
    const interval = setInterval(() => {
      loadDashboardData();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage) => {
    const value = parseFloat(percentage);
    if (isNaN(value)) return "";
    const sign = value >= 0 ? "+" : "";
    const color = value >= 0 ? "text-green-600" : "text-red-600";
    const icon =
      value >= 0 ? <TrendingUp size={14} /> : <TrendingDown size={14} />;
    return (
      <span className={`flex items-center text-xs ${color}`}>
        {icon} {sign}
        {value.toFixed(1)}%
      </span>
    );
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString();
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case "restaurant_approval":
        return <Home size={20} className='text-blue-500' />;
      case "order":
        return <ShoppingBag size={20} className='text-green-500' />;
      case "user":
        return <Users size={20} className='text-purple-500' />;
      case "system":
        return <BarChart2 size={20} className='text-orange-500' />;
      default:
        return <AlertCircle size={20} className='text-gray-500' />;
    }
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Dashboard Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Dashboard</h1>
          <p className='mt-2 text-gray-600'>
            Welcome back, {user?.name}! Here's what's happening with your
            platform today.
          </p>
          <div className='flex items-center space-x-4 mt-2'>
            {lastUpdated && (
              <p className='text-sm text-gray-500'>
                Last updated: {lastUpdated.toLocaleTimeString()}
              </p>
            )}
            <div className='flex items-center space-x-2'>
              <div
                className={`w-2 h-2 rounded-full ${
                  connectionStatus === "connected"
                    ? "bg-green-500"
                    : connectionStatus === "error"
                    ? "bg-red-500"
                    : "bg-yellow-500"
                }`}
              ></div>
              <span className='text-sm text-gray-500'>
                {connectionStatus === "connected"
                  ? "Connected"
                  : connectionStatus === "error"
                  ? "Connection Error"
                  : "Connecting..."}
              </span>
            </div>
          </div>
        </div>
        <div className='flex items-center space-x-4'>
          <select
            className='px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
          >
            <option value='today'>Today</option>
            <option value='week'>Last 7 Days</option>
            <option value='month'>Last 30 Days</option>
            <option value='quarter'>Last 3 Months</option>
          </select>
          <Button
            variant='outline'
            icon={
              <RefreshCw
                size={18}
                className={refreshing ? "animate-spin" : ""}
              />
            }
            onClick={refreshDashboard}
            disabled={loading || refreshing}
            className='border-gray-300 text-gray-700 hover:bg-gray-50'
          >
            {refreshing ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>
      {/* Key Performance Indicators */}
      <div
        className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${
          refreshing ? "opacity-75 transition-opacity" : ""
        }`}
      >
        {/* Total Revenue */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl'>
              <DollarSign size={24} className='text-white' />
            </div>
            <div className='flex items-center space-x-1 text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
              <TrendingUp size={14} />
              <span className='text-sm font-medium'>
                +{metrics.monthlyGrowth.revenue.toFixed(1)}%
              </span>
            </div>
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>Total Revenue</p>
            <p className='text-3xl font-bold text-gray-900'>
              {formatCurrency(metrics.totalRevenue)}
            </p>
            <p className='text-sm text-gray-500'>
              Today:{" "}
              <span className='font-medium text-gray-700'>
                {formatCurrency(metrics.todayRevenue)}
              </span>
            </p>
          </div>
        </div>

        {/* Total Orders */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl'>
              <ShoppingBag size={24} className='text-white' />
            </div>
            <div className='flex items-center space-x-1 text-blue-600 bg-blue-50 px-2 py-1 rounded-full'>
              <TrendingUp size={14} />
              <span className='text-sm font-medium'>
                +{metrics.monthlyGrowth.orders.toFixed(1)}%
              </span>
            </div>
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>Total Orders</p>
            <p className='text-3xl font-bold text-gray-900'>
              {metrics.totalOrders.toLocaleString()}
            </p>
            <p className='text-sm text-gray-500'>
              Today:{" "}
              <span className='font-medium text-gray-700'>
                {metrics.todayOrders}
              </span>
            </p>
          </div>
        </div>

        {/* Active Users */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl'>
              <Users size={24} className='text-white' />
            </div>
            <div className='flex items-center space-x-1 text-purple-600 bg-purple-50 px-2 py-1 rounded-full'>
              <TrendingUp size={14} />
              <span className='text-sm font-medium'>
                +{metrics.monthlyGrowth.users.toFixed(1)}%
              </span>
            </div>
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>Active Users</p>
            <p className='text-3xl font-bold text-gray-900'>
              {metrics.totalUsers.toLocaleString()}
            </p>
            <p className='text-sm text-gray-500'>
              New today: <span className='font-medium text-gray-700'>24</span>
            </p>
          </div>
        </div>

        {/* Restaurants */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl'>
              <Store size={24} className='text-white' />
            </div>
            {metrics.pendingApprovals > 0 ? (
              <div className='flex items-center space-x-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-full'>
                <Clock size={14} />
                <span className='text-sm font-medium'>
                  {metrics.pendingApprovals} pending
                </span>
              </div>
            ) : (
              <div className='flex items-center space-x-1 text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
                <CheckCircle size={14} />
                <span className='text-sm font-medium'>All approved</span>
              </div>
            )}
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>
              Active Restaurants
            </p>
            <p className='text-3xl font-bold text-gray-900'>
              {metrics.totalRestaurants}
            </p>
            <p className='text-sm text-gray-500'>
              <button
                onClick={() => navigate("/admin/restaurant-approvals")}
                className='font-medium text-orange-600 hover:text-orange-700 transition-colors'
              >
                Manage approvals →
              </button>
            </p>
          </div>
        </div>
      </div>

      {/* Delivery Agent Management Section */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Delivery Agent Approvals */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl'>
              <Truck size={24} className='text-white' />
            </div>
            {metrics.pendingDeliveryApprovals > 0 ? (
              <div className='flex items-center space-x-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-full'>
                <Clock size={14} />
                <span className='text-sm font-medium'>
                  {metrics.pendingDeliveryApprovals} pending
                </span>
              </div>
            ) : (
              <div className='flex items-center space-x-1 text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
                <CheckCircle size={14} />
                <span className='text-sm font-medium'>All approved</span>
              </div>
            )}
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>
              Delivery Agent Approvals
            </p>
            <p className='text-3xl font-bold text-gray-900'>
              {metrics.activeDeliveryAgents}
            </p>
            <p className='text-sm text-gray-500'>
              <button
                onClick={() => navigate("/admin/employees")}
                className='font-medium text-indigo-600 hover:text-indigo-700 transition-colors'
              >
                Manage employees →
              </button>
            </p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl'>
              <Zap size={24} className='text-white' />
            </div>
            <div className='flex items-center space-x-1 text-cyan-600 bg-cyan-50 px-2 py-1 rounded-full'>
              <Activity size={14} />
              <span className='text-sm font-medium'>Quick Access</span>
            </div>
          </div>
          <div className='space-y-3'>
            <button
              onClick={() => navigate("/admin/employees")}
              className='w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors'
            >
              <div className='flex items-center space-x-2'>
                <Users size={16} className='text-green-600' />
                <span className='text-sm font-medium text-gray-700'>
                  Employee Management
                </span>
              </div>
              <ArrowRight size={14} className='text-gray-400' />
            </button>
            <button
              onClick={() => navigate("/admin/restaurants")}
              className='w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors'
            >
              <div className='flex items-center space-x-2'>
                <Store size={16} className='text-orange-600' />
                <span className='text-sm font-medium text-gray-700'>
                  Restaurant Management
                </span>
              </div>
              <ArrowRight size={14} className='text-gray-400' />
            </button>
          </div>
        </div>
      </div>

      {/* Analytics Charts */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Order Status Distribution */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='flex justify-between items-center mb-6'>
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                Order Status
              </h3>
              <p className='text-sm text-gray-500'>
                Current order distribution
              </p>
            </div>
          </div>
          <div className='h-80'>
            <ResponsiveContainer width='100%' height='100%'>
              <PieChart>
                <Pie
                  data={orderStatusData}
                  cx='50%'
                  cy='50%'
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={100}
                  fill='#8884d8'
                  dataKey='value'
                >
                  {orderStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: "white",
                    border: "1px solid #E5E7EB",
                    borderRadius: "12px",
                    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
                  }}
                  formatter={(value) => [`${value}%`, "Percentage"]}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Top Restaurants & System Health */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Top Restaurants */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='flex justify-between items-center mb-6'>
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                Top Restaurants
              </h3>
              <p className='text-sm text-gray-500'>
                Best performing restaurants
              </p>
            </div>
            <Button
              variant='outline'
              size='small'
              onClick={() => navigate("/admin/restaurants")}
              className='text-gray-600 border-gray-300 hover:bg-gray-50'
            >
              View All
            </Button>
          </div>
          <div className='space-y-3'>
            {topRestaurantsData.length > 0 ? (
              topRestaurantsData.slice(0, 3).map((restaurant, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors'
                >
                  <div className='flex items-center space-x-3'>
                    <div className='w-8 h-8 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center text-white font-semibold text-sm'>
                      #{index + 1}
                    </div>
                    <div>
                      <h4 className='font-medium text-gray-900 text-sm'>
                        {restaurant.name}
                      </h4>
                      <div className='text-xs text-gray-500'>
                        {restaurant.orders} orders
                      </div>
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='font-semibold text-gray-900 text-sm'>
                      ${restaurant.revenue.toLocaleString()}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className='text-center text-gray-500 py-8'>
                <Store size={48} className='mx-auto text-gray-300 mb-4' />
                <p className='text-sm'>No restaurant data available</p>
              </div>
            )}
          </div>
        </div>

        {/* System Health */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='mb-4'>
            <h3 className='text-lg font-semibold text-gray-900'>
              System Health
            </h3>
            <p className='text-sm text-gray-500'>Real-time monitoring</p>
          </div>
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center'>
                <div className='w-3 h-3 rounded-full bg-emerald-500 mr-3'></div>
                <span className='font-medium text-gray-700'>Status</span>
              </div>
              <span className='text-sm font-medium text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
                Healthy
              </span>
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <div className='text-center p-3 bg-gray-50 rounded-lg'>
                <div className='text-lg font-bold text-gray-900'>
                  {systemStatus.uptime}
                </div>
                <div className='text-xs text-gray-500'>Uptime</div>
              </div>
              <div className='text-center p-3 bg-gray-50 rounded-lg'>
                <div className='text-lg font-bold text-gray-900'>
                  {systemStatus.responseTime}
                </div>
                <div className='text-xs text-gray-500'>Response</div>
              </div>
            </div>

            <div className='flex justify-between items-center'>
              <span className='text-sm text-gray-600'>Server Load</span>
              <div className='flex items-center space-x-2'>
                <span className='text-sm font-semibold text-gray-900'>
                  {systemStatus.serverLoad}%
                </span>
                <div className='w-16 h-2 bg-gray-200 rounded-full'>
                  <div
                    className='h-2 bg-emerald-500 rounded-full transition-all duration-300'
                    style={{ width: `${systemStatus.serverLoad}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdminDashboard;
