<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backup Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>🧪 Backup Frontend Test Suite</h1>
    
    <div class="test-section">
        <h2>🔐 Authentication Test</h2>
        <button class="test-button" onclick="testAuth()">Test Authentication</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 API Connectivity Test</h2>
        <button class="test-button" onclick="testAPI()">Test All APIs</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🚀 Backup Creation Test</h2>
        <button class="test-button" onclick="testBackupCreation('database')">Test Database Backup</button>
        <button class="test-button" onclick="testBackupCreation('full')">Test Full Backup</button>
        <button class="test-button" onclick="testBackupCreation('partial')">Test Partial Backup</button>
        <div class="progress-bar" style="display: none;" id="backup-progress">
            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
        </div>
        <div id="backup-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📋 Backup List Test</h2>
        <button class="test-button" onclick="testBackupList()">Load Backup List</button>
        <div id="list-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        let authToken = null;

        // Utility functions
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // Authentication test
        async function testAuth() {
            clearLog('auth-result');
            log('auth-result', '🔍 Testing authentication...', 'info');

            // Check localStorage
            const user = JSON.parse(localStorage.getItem('afghanSofraUser') || '{}');
            const token = user.access_token || localStorage.getItem('access_token');
            
            log('auth-result', `Local storage user: ${JSON.stringify(user, null, 2)}`, 'info');
            log('auth-result', `Token exists: ${!!token}`, token ? 'success' : 'error');
            
            if (token) {
                authToken = token;
                log('auth-result', '✅ Authentication token found', 'success');
                
                // Test token validity
                try {
                    const response = await fetch(`${API_BASE}/admin/backup/stats/`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        log('auth-result', '✅ Token is valid', 'success');
                    } else {
                        log('auth-result', `❌ Token validation failed: ${response.status}`, 'error');
                    }
                } catch (error) {
                    log('auth-result', `❌ Token test error: ${error.message}`, 'error');
                }
            } else {
                log('auth-result', '❌ No authentication token found', 'error');
                log('auth-result', '💡 Please log in to the admin panel first', 'info');
            }
        }

        // API connectivity test
        async function testAPI() {
            clearLog('api-result');
            log('api-result', '🔍 Testing API connectivity...', 'info');

            if (!authToken) {
                log('api-result', '❌ No auth token. Run authentication test first.', 'error');
                return;
            }

            const headers = {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            };

            const endpoints = [
                { name: 'Backup Stats', url: '/admin/backup/stats/' },
                { name: 'Backup List', url: '/admin/backup/backups/' },
                { name: 'Backup Settings', url: '/admin/backup/settings/' }
            ];

            for (const endpoint of endpoints) {
                try {
                    log('api-result', `Testing ${endpoint.name}...`, 'info');
                    const response = await fetch(`${API_BASE}${endpoint.url}`, { headers });
                    
                    if (response.ok) {
                        const data = await response.json();
                        log('api-result', `✅ ${endpoint.name}: Success`, 'success');
                        log('api-result', `   Response: ${JSON.stringify(data, null, 2)}`, 'info');
                    } else {
                        log('api-result', `❌ ${endpoint.name}: Failed (${response.status})`, 'error');
                    }
                } catch (error) {
                    log('api-result', `❌ ${endpoint.name}: Error - ${error.message}`, 'error');
                }
            }
        }

        // Backup creation test
        async function testBackupCreation(type) {
            clearLog('backup-result');
            log('backup-result', `🚀 Testing ${type} backup creation...`, 'info');

            if (!authToken) {
                log('backup-result', '❌ No auth token. Run authentication test first.', 'error');
                return;
            }

            const progressBar = document.getElementById('backup-progress');
            const progressFill = document.getElementById('progress-fill');
            progressBar.style.display = 'block';
            progressFill.style.width = '0%';

            const headers = {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            };

            const backupData = {
                name: `Frontend Test ${type.charAt(0).toUpperCase() + type.slice(1)} Backup - ${new Date().toLocaleString()}`,
                backup_type: type,
                storage_location: 'local',
                retention_days: 7
            };

            try {
                // Create backup
                log('backup-result', 'Creating backup...', 'info');
                const createResponse = await fetch(`${API_BASE}/admin/backup/backups/`, {
                    method: 'POST',
                    headers,
                    body: JSON.stringify(backupData)
                });

                if (!createResponse.ok) {
                    throw new Error(`Backup creation failed: ${createResponse.status}`);
                }

                const createResult = await createResponse.json();
                const backupId = createResult.data.id;
                log('backup-result', `✅ Backup created with ID: ${backupId}`, 'success');

                // Monitor progress
                log('backup-result', 'Monitoring progress...', 'info');
                const monitorInterval = setInterval(async () => {
                    try {
                        const statusResponse = await fetch(`${API_BASE}/admin/backup/backups/${backupId}/status/`, { headers });
                        
                        if (statusResponse.ok) {
                            const statusData = await statusResponse.json();
                            const progress = statusData.data.progress;
                            const status = statusData.data.status;
                            
                            progressFill.style.width = `${progress}%`;
                            log('backup-result', `Progress: ${progress}% - Status: ${status}`, 'info');
                            
                            if (status === 'completed') {
                                clearInterval(monitorInterval);
                                progressBar.style.display = 'none';
                                log('backup-result', '✅ Backup completed successfully!', 'success');
                                
                                // Get final details
                                const detailResponse = await fetch(`${API_BASE}/admin/backup/backups/${backupId}/`, { headers });
                                if (detailResponse.ok) {
                                    const details = await detailResponse.json();
                                    log('backup-result', `Final details: ${JSON.stringify(details.data, null, 2)}`, 'info');
                                }
                            } else if (status === 'failed') {
                                clearInterval(monitorInterval);
                                progressBar.style.display = 'none';
                                log('backup-result', `❌ Backup failed: ${statusData.data.error_message || 'Unknown error'}`, 'error');
                            }
                        }
                    } catch (error) {
                        log('backup-result', `❌ Progress monitoring error: ${error.message}`, 'error');
                    }
                }, 1000);

                // Timeout after 30 seconds
                setTimeout(() => {
                    clearInterval(monitorInterval);
                    progressBar.style.display = 'none';
                    log('backup-result', '⏰ Monitoring timeout', 'error');
                }, 30000);

            } catch (error) {
                progressBar.style.display = 'none';
                log('backup-result', `❌ Backup creation error: ${error.message}`, 'error');
            }
        }

        // Backup list test
        async function testBackupList() {
            clearLog('list-result');
            log('list-result', '📋 Testing backup list...', 'info');

            if (!authToken) {
                log('list-result', '❌ No auth token. Run authentication test first.', 'error');
                return;
            }

            const headers = {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            };

            try {
                const response = await fetch(`${API_BASE}/admin/backup/backups/`, { headers });
                
                if (response.ok) {
                    const data = await response.json();
                    log('list-result', '✅ Backup list loaded successfully', 'success');
                    log('list-result', `Total backups: ${data.data.total_count}`, 'info');
                    log('list-result', `Backups in current page: ${data.data.results.length}`, 'info');
                    
                    if (data.data.results.length > 0) {
                        log('list-result', '\nRecent backups:', 'info');
                        data.data.results.slice(0, 5).forEach((backup, index) => {
                            log('list-result', `${index + 1}. ${backup.name} (${backup.backup_type}) - ${backup.status} - ${backup.file_size_formatted}`, 'info');
                        });
                    }
                    
                    log('list-result', `\nFull response: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    log('list-result', `❌ Failed to load backup list: ${response.status}`, 'error');
                    const errorText = await response.text();
                    log('list-result', `Error details: ${errorText}`, 'error');
                }
            } catch (error) {
                log('list-result', `❌ Backup list error: ${error.message}`, 'error');
            }
        }

        // Auto-run authentication test on page load
        window.onload = function() {
            testAuth();
        };
    </script>
</body>
</html>
