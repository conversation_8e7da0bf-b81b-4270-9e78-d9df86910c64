#!/usr/bin/env python3
"""
Test script for sending notifications via API
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()

def test_send_notification():
    """Test sending a notification via the API"""
    print("🧪 Testing Send Notification API...")
    print("=" * 50)
    
    # Use existing admin user
    admin_user = User.objects.filter(role='admin').first()
    if not admin_user:
        print("❌ No admin user found")
        return
    
    print(f"✅ Using admin user: {admin_user.user_name}")
    
    # Create a test client
    client = Client()
    
    # Login
    login_response = client.post('/api/auth/login/', 
                                json.dumps({
                                    'user_name': admin_user.user_name,
                                    'password': 'admin123'
                                }),
                                content_type='application/json')
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.content}")
        return
    
    access_token = login_response.json().get('data', {}).get('access_token')
    headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
    
    # Test different notification scenarios
    test_cases = [
        {
            'name': 'All Users Notification',
            'data': {
                'title': 'Test Notification for All Users',
                'message': 'This is a test notification sent to all users.',
                'notification_type': 'general',
                'recipient_type': 'all',
                'channels': ['email'],
                'priority': 2
            }
        },
        {
            'name': 'Role-based Notification',
            'data': {
                'title': 'Test Notification for Admins',
                'message': 'This is a test notification sent to admin users only.',
                'notification_type': 'system',
                'recipient_type': 'role',
                'recipient_roles': ['admin'],
                'channels': ['email'],
                'priority': 3
            }
        },
        {
            'name': 'Specific User Notification',
            'data': {
                'title': 'Test Notification for Specific User',
                'message': 'This is a test notification sent to a specific user.',
                'notification_type': 'user',
                'recipient_type': 'user',
                'recipient_users': [admin_user.id],
                'channels': ['email'],
                'priority': 1
            }
        },
        {
            'name': 'Custom Email Notification',
            'data': {
                'title': 'Test Notification for Custom Emails',
                'message': 'This is a test notification sent to custom email addresses.',
                'notification_type': 'marketing',
                'recipient_type': 'custom',
                'recipient_emails': ['<EMAIL>', '<EMAIL>'],
                'channels': ['email'],
                'priority': 1
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📤 Test {i}: {test_case['name']}")
        
        response = client.post('/api/admin/notifications/send/',
                              json.dumps(test_case['data']),
                              content_type='application/json',
                              **headers)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result.get('message', 'Notification sent')}")
            notification_id = result.get('data', {}).get('id')
            if notification_id:
                print(f"Notification ID: {notification_id}")
        else:
            print(f"❌ Failed: {response.content}")
    
    print("\n" + "=" * 50)
    print("🏁 Send Notification Testing Complete!")


if __name__ == '__main__':
    test_send_notification()
