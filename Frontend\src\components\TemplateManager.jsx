import React, { useState, useEffect } from "react";
import {
  Plus,
  Edit3,
  Trash2,
  <PERSON><PERSON>,
  Eye,
  Save,
  X,
  MessageSquare,
  Mail,
  Smartphone,
} from "lucide-react";
import Button from "./common/Button";
import Card from "./common/Card";
import Input from "./common/Input";

const TemplateManager = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    template_type: "general",
    title_template: "",
    message_template: "",
    supports_email: true,
    supports_sms: false,
    supports_push: false,
    variables: [],
  });

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/admin/notifications/templates/", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTemplates(Array.isArray(data) ? data : []);
      } else {
        setError("Failed to load templates");
      }
    } catch (err) {
      setError("Error loading templates");
      console.error("Template loading error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = async () => {
    try {
      const response = await fetch(
        "/api/admin/notifications/templates/create/",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        }
      );

      if (response.ok) {
        await loadTemplates();
        setShowCreateModal(false);
        resetForm();
      } else {
        const errorData = await response.json();
        setError(errorData.error?.message || "Failed to create template");
      }
    } catch (err) {
      setError("Error creating template");
      console.error("Template creation error:", err);
    }
  };

  const handleUpdateTemplate = async () => {
    try {
      const response = await fetch(
        `/api/admin/notifications/templates/${editingTemplate.id}/update/`,
        {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        }
      );

      if (response.ok) {
        await loadTemplates();
        setEditingTemplate(null);
        resetForm();
      } else {
        const errorData = await response.json();
        setError(errorData.error?.message || "Failed to update template");
      }
    } catch (err) {
      setError("Error updating template");
      console.error("Template update error:", err);
    }
  };

  const handleDeleteTemplate = async (templateId) => {
    if (!confirm("Are you sure you want to delete this template?")) {
      return;
    }

    try {
      const response = await fetch(
        `/api/admin/notifications/templates/${templateId}/delete/`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        await loadTemplates();
      } else {
        const errorData = await response.json();
        setError(errorData.error?.message || "Failed to delete template");
      }
    } catch (err) {
      setError("Error deleting template");
      console.error("Template deletion error:", err);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      template_type: "general",
      title_template: "",
      message_template: "",
      supports_email: true,
      supports_sms: false,
      supports_push: false,
      variables: [],
    });
  };

  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const openEditModal = (template) => {
    setFormData({
      name: template.name,
      template_type: template.template_type || "general",
      title_template: template.title_template,
      message_template: template.message_template,
      supports_email: template.supports_email,
      supports_sms: template.supports_sms,
      supports_push: template.supports_push,
      variables: template.variables || [],
    });
    setEditingTemplate(template);
  };

  const handleFormChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const getChannelIcons = (template) => {
    const icons = [];
    if (template.supports_email)
      icons.push(<Mail key='email' size={16} className='text-blue-600' />);
    if (template.supports_sms)
      icons.push(
        <MessageSquare key='sms' size={16} className='text-green-600' />
      );
    if (template.supports_push)
      icons.push(
        <Smartphone key='push' size={16} className='text-purple-600' />
      );
    return icons;
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full'></div>
        <span className='ml-3 text-gray-600'>Loading templates...</span>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold text-gray-900'>
            Notification Templates
          </h2>
          <p className='text-gray-600'>
            Create and manage reusable notification templates
          </p>
        </div>
        <Button
          variant='primary'
          icon={<Plus size={16} />}
          onClick={openCreateModal}
        >
          Create Template
        </Button>
      </div>

      {error && (
        <div className='p-4 bg-red-50 border border-red-200 rounded-lg'>
          <p className='text-red-600 text-sm'>{error}</p>
        </div>
      )}

      {/* Templates Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {templates.map((template) => (
          <Card key={template.id} className='hover:shadow-lg transition-shadow'>
            <div className='p-6'>
              <div className='flex items-start justify-between mb-4'>
                <div className='flex-1'>
                  <h3 className='font-semibold text-gray-900 mb-1'>
                    {template.name}
                  </h3>
                  <p className='text-sm text-gray-600 mb-2'>
                    {template.description}
                  </p>
                  <div className='flex items-center gap-2'>
                    {getChannelIcons(template)}
                  </div>
                </div>
              </div>

              <div className='space-y-3'>
                <div>
                  <label className='text-xs font-medium text-gray-500 uppercase tracking-wide'>
                    Title Template
                  </label>
                  <p className='text-sm text-gray-800 bg-gray-50 p-2 rounded mt-1 font-mono'>
                    {template.title_template || "No title template"}
                  </p>
                </div>

                <div>
                  <label className='text-xs font-medium text-gray-500 uppercase tracking-wide'>
                    Message Template
                  </label>
                  <p className='text-sm text-gray-800 bg-gray-50 p-2 rounded mt-1 font-mono line-clamp-3'>
                    {template.message_template || "No message template"}
                  </p>
                </div>
              </div>

              <div className='flex items-center justify-between mt-6 pt-4 border-t border-gray-200'>
                <div className='flex items-center gap-2'>
                  <Button
                    size='small'
                    variant='outline'
                    icon={<Eye size={14} />}
                    onClick={() => {
                      /* Preview functionality */
                    }}
                  >
                    Preview
                  </Button>
                  <Button
                    size='small'
                    variant='outline'
                    icon={<Copy size={14} />}
                    onClick={() => {
                      /* Duplicate functionality */
                    }}
                  >
                    Duplicate
                  </Button>
                </div>
                <div className='flex items-center gap-2'>
                  <Button
                    size='small'
                    variant='outline'
                    icon={<Edit3 size={14} />}
                    onClick={() => openEditModal(template)}
                  >
                    Edit
                  </Button>
                  <Button
                    size='small'
                    variant='outline'
                    icon={<Trash2 size={14} />}
                    onClick={() => handleDeleteTemplate(template.id)}
                    className='text-red-600 hover:text-red-700'
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {templates.length === 0 && !loading && (
        <div className='text-center py-12'>
          <MessageSquare size={48} className='mx-auto text-gray-300 mb-4' />
          <h3 className='text-lg font-medium text-gray-900 mb-2'>
            No templates yet
          </h3>
          <p className='text-gray-600 mb-4'>
            Create your first notification template to get started
          </p>
          <Button
            variant='primary'
            icon={<Plus size={16} />}
            onClick={openCreateModal}
          >
            Create Template
          </Button>
        </div>
      )}

      {/* Create/Edit Modal */}
      {(showCreateModal || editingTemplate) && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto'>
            <div className='p-6'>
              <div className='flex items-center justify-between mb-6'>
                <h3 className='text-lg font-semibold'>
                  {editingTemplate ? "Edit Template" : "Create Template"}
                </h3>
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    setEditingTemplate(null);
                    resetForm();
                  }}
                  className='text-gray-400 hover:text-gray-600'
                >
                  <X size={20} />
                </button>
              </div>

              <div className='space-y-4'>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Template Name
                  </label>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleFormChange("name", e.target.value)}
                    placeholder='Enter template name'
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Template Type
                  </label>
                  <select
                    value={formData.template_type}
                    onChange={(e) =>
                      handleFormChange("template_type", e.target.value)
                    }
                    className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  >
                    <option value='general'>General</option>
                    <option value='order'>Order</option>
                    <option value='user'>User</option>
                    <option value='restaurant'>Restaurant</option>
                    <option value='delivery'>Delivery</option>
                    <option value='payment'>Payment</option>
                    <option value='system'>System</option>
                    <option value='marketing'>Marketing</option>
                  </select>
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Title Template
                  </label>
                  <Input
                    value={formData.title_template}
                    onChange={(e) =>
                      handleFormChange("title_template", e.target.value)
                    }
                    placeholder='Enter title template (use {{variable}} for dynamic content)'
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Message Template
                  </label>
                  <textarea
                    value={formData.message_template}
                    onChange={(e) =>
                      handleFormChange("message_template", e.target.value)
                    }
                    placeholder='Enter message template (use {{variable}} for dynamic content)'
                    rows={4}
                    className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-3'>
                    Supported Channels
                  </label>
                  <div className='space-y-2'>
                    {[
                      { key: "supports_email", label: "Email", icon: Mail },
                      {
                        key: "supports_sms",
                        label: "SMS",
                        icon: MessageSquare,
                      },
                      {
                        key: "supports_push",
                        label: "Push Notifications",
                        icon: Smartphone,
                      },
                    ].map(({ key, label, icon: Icon }) => (
                      <label key={key} className='flex items-center'>
                        <input
                          type='checkbox'
                          checked={formData[key]}
                          onChange={(e) =>
                            handleFormChange(key, e.target.checked)
                          }
                          className='mr-3'
                        />
                        <Icon size={16} className='mr-2' />
                        <span>{label}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              <div className='flex items-center justify-end gap-3 mt-6 pt-6 border-t border-gray-200'>
                <Button
                  variant='outline'
                  onClick={() => {
                    setShowCreateModal(false);
                    setEditingTemplate(null);
                    resetForm();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant='primary'
                  icon={<Save size={16} />}
                  onClick={
                    editingTemplate
                      ? handleUpdateTemplate
                      : handleCreateTemplate
                  }
                >
                  {editingTemplate ? "Update Template" : "Create Template"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateManager;
