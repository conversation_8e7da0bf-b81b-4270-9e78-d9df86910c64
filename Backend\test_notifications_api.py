#!/usr/bin/env python3
"""
Test script for notifications API endpoints
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse

User = get_user_model()

def test_notifications_api():
    """Test the notifications API endpoints"""
    print("🧪 Testing Notifications API Endpoints...")
    print("=" * 60)
    
    # Use existing admin user
    admin_user = User.objects.filter(role='admin').first()
    if not admin_user:
        print("❌ No admin user found in database")
        return
    
    print(f"✅ Using existing admin user: {admin_user.user_name}")
    
    # Set a known password for testing
    admin_user.set_password('admin123')
    admin_user.save()
    
    # Create a test client
    client = Client()
    
    # Login the admin user
    login_data = {
        'user_name': admin_user.user_name,
        'password': 'admin123'
    }
    print(f"🔍 Login request data: {login_data}")
    
    login_response = client.post('/api/auth/login/', 
                                json.dumps(login_data),
                                content_type='application/json')
    
    print(f"Login response status: {login_response.status_code}")
    
    if login_response.status_code == 200:
        login_data = login_response.json()
        # Access token is nested in data field
        access_token = login_data.get('data', {}).get('access_token')
        if access_token:
            print(f"✅ Admin login successful, token: {access_token[:20]}...")
        else:
            print(f"❌ No access token in response: {login_data}")
            return
        
        # Set authorization header
        headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
        
        # Test 1: Get notifications
        print("\n🔍 Testing GET /api/admin/notifications/")
        notifications_response = client.get('/api/admin/notifications/', **headers)
        print(f"Status: {notifications_response.status_code}")
        
        if notifications_response.status_code == 200:
            notifications_data = notifications_response.json()
            print(f"✅ Notifications retrieved successfully")
            print(f"Response structure: {list(notifications_data.keys())}")
            if 'results' in notifications_data:
                print(f"Total notifications: {notifications_data.get('count', 'N/A')}")
                print(f"Notifications in response: {len(notifications_data['results'])}")
            elif 'data' in notifications_data and 'notifications' in notifications_data['data']:
                print(f"Notifications in response: {len(notifications_data['data']['notifications'])}")
        else:
            print(f"❌ Failed to get notifications: {notifications_response.content}")
        
        # Test 2: Get notification statistics
        print("\n📊 Testing GET /api/admin/notifications/stats/")
        stats_response = client.get('/api/admin/notifications/stats/', **headers)
        print(f"Status: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print(f"✅ Stats retrieved successfully")
            print(f"Stats data: {json.dumps(stats_data, indent=2)}")
        else:
            print(f"❌ Failed to get stats: {stats_response.content}")
        
        # Test 3: Get notification settings
        print("\n⚙️ Testing GET /api/admin/notifications/settings/")
        settings_response = client.get('/api/admin/notifications/settings/', **headers)
        print(f"Status: {settings_response.status_code}")
        
        if settings_response.status_code == 200:
            settings_data = settings_response.json()
            print(f"✅ Settings retrieved successfully")
            print(f"Settings keys: {list(settings_data.get('data', {}).keys())}")
        else:
            print(f"❌ Failed to get settings: {settings_response.content}")
        
        # Test 4: Get notification templates
        print("\n📝 Testing GET /api/admin/notifications/templates/")
        templates_response = client.get('/api/admin/notifications/templates/', **headers)
        print(f"Status: {templates_response.status_code}")
        
        if templates_response.status_code == 200:
            templates_data = templates_response.json()
            print(f"✅ Templates retrieved successfully")
            if isinstance(templates_data, list):
                print(f"Templates count: {len(templates_data)}")
            elif 'results' in templates_data:
                print(f"Templates count: {len(templates_data['results'])}")
        else:
            print(f"❌ Failed to get templates: {templates_response.content}")
        
        # Test 5: Send test notification
        print("\n📤 Testing POST /api/admin/notifications/test/")
        test_response = client.post('/api/admin/notifications/test/', **headers)
        print(f"Status: {test_response.status_code}")
        
        if test_response.status_code == 200:
            test_data = test_response.json()
            print(f"✅ Test notification sent successfully")
            print(f"Response: {json.dumps(test_data, indent=2)}")
        else:
            print(f"❌ Failed to send test notification: {test_response.content}")
        
        # Test 6: Send custom notification
        print("\n📨 Testing POST /api/admin/notifications/send/")
        send_data = {
            'title': 'API Test Notification',
            'message': 'This is a test notification sent via API',
            'notification_type': 'system',
            'recipient_type': 'user',
            'recipient_users': [admin_user.id],
            'channels': ['email'],
            'priority': 2
        }
        
        send_response = client.post('/api/admin/notifications/send/', 
                                   json.dumps(send_data),
                                   content_type='application/json',
                                   **headers)
        print(f"Status: {send_response.status_code}")
        
        if send_response.status_code == 200:
            send_result = send_response.json()
            print(f"✅ Custom notification sent successfully")
            print(f"Notification ID: {send_result.get('data', {}).get('id', 'N/A')}")
        else:
            print(f"❌ Failed to send custom notification: {send_response.content}")
        
        # Test 7: Get notification history
        print("\n📈 Testing GET /api/admin/notifications/history/")
        history_response = client.get('/api/admin/notifications/history/?days=7', **headers)
        print(f"Status: {history_response.status_code}")
        
        if history_response.status_code == 200:
            history_data = history_response.json()
            print(f"✅ History retrieved successfully")
            if 'data' in history_data:
                daily_stats = history_data['data'].get('daily_stats', [])
                summary = history_data['data'].get('summary', {})
                print(f"Daily stats entries: {len(daily_stats)}")
                print(f"Summary: {summary}")
        else:
            print(f"❌ Failed to get history: {history_response.content}")
        
    else:
        print(f"❌ Admin login failed: {login_response.content}")
    
    print("\n" + "=" * 60)
    print("🏁 Notifications API Testing Complete!")


if __name__ == '__main__':
    test_notifications_api()
