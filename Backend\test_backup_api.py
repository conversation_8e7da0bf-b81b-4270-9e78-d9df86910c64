#!/usr/bin/env python3
"""
Test script for backup API endpoints
"""

import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse

User = get_user_model()

def test_backup_api():
    """Test backup API endpoints"""
    
    # Create a test admin user if it doesn't exist
    admin_user, created = User.objects.get_or_create(
        user_name='admin_test',
        defaults={
            'name': 'Admin Test',
            'phone': '1234567890',
            'email': '<EMAIL>',
            'role': 'admin',
            'is_staff': True,
            'is_superuser': True,
            'is_verified': True
        }
    )

    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print("✅ Created test admin user")
    else:
        # Ensure existing user is verified
        if not admin_user.is_verified:
            admin_user.is_verified = True
            admin_user.save()
        print("✅ Using existing admin user")
    
    # Create a test client
    client = Client()
    
    # Login as admin
    login_response = client.post('/api/auth/login/', {
        'user_name': 'admin_test',
        'password': 'admin123'
    })
    
    if login_response.status_code == 200:
        print("✅ Admin login successful")
        login_data = login_response.json()
        access_token = (login_data.get('data', {}).get('access_token') or
                       login_data.get('access_token') or
                       login_data.get('access'))

        if not access_token:
            print(f"❌ No access token in response: {login_data}")
            return

        # Set authorization header
        headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
        
        # Test backup stats endpoint
        print("\n🔍 Testing backup stats endpoint...")
        stats_response = client.get('/api/admin/backup/stats/', **headers)
        print(f"Status: {stats_response.status_code}")
        if stats_response.status_code == 200:
            print("✅ Backup stats endpoint working")
            print(f"Response: {stats_response.json()}")
        else:
            print(f"❌ Backup stats failed: {stats_response.content}")
        
        # Test backup list endpoint
        print("\n🔍 Testing backup list endpoint...")
        list_response = client.get('/api/admin/backup/backups/', **headers)
        print(f"Status: {list_response.status_code}")
        if list_response.status_code == 200:
            print("✅ Backup list endpoint working")
            print(f"Response: {list_response.json()}")
        else:
            print(f"❌ Backup list failed: {list_response.content}")
        
        # Test backup creation endpoint
        print("\n🔍 Testing backup creation endpoint...")
        create_data = {
            'name': 'Test Database Backup',
            'backup_type': 'database',
            'storage_location': 'local',
            'retention_days': 7
        }
        create_response = client.post('/api/admin/backup/backups/', 
                                    json.dumps(create_data),
                                    content_type='application/json',
                                    **headers)
        print(f"Status: {create_response.status_code}")
        if create_response.status_code == 201:
            print("✅ Backup creation endpoint working")
            backup_data = create_response.json()
            print(f"Response: {backup_data}")
            
            # Test backup status endpoint
            if backup_data.get('success') and backup_data.get('data', {}).get('id'):
                backup_id = backup_data['data']['id']
                print(f"\n🔍 Testing backup status endpoint for backup {backup_id}...")
                status_response = client.get(f'/api/admin/backup/backups/{backup_id}/status/', **headers)
                print(f"Status: {status_response.status_code}")
                if status_response.status_code == 200:
                    print("✅ Backup status endpoint working")
                    print(f"Response: {status_response.json()}")
                else:
                    print(f"❌ Backup status failed: {status_response.content}")
        else:
            print(f"❌ Backup creation failed: {create_response.content}")
        
        # Test backup settings endpoint
        print("\n🔍 Testing backup settings endpoint...")
        settings_response = client.get('/api/admin/backup/settings/', **headers)
        print(f"Status: {settings_response.status_code}")
        if settings_response.status_code == 200:
            print("✅ Backup settings endpoint working")
            print(f"Response: {settings_response.json()}")
        else:
            print(f"❌ Backup settings failed: {settings_response.content}")
            
    else:
        print(f"❌ Admin login failed: {login_response.content}")

if __name__ == '__main__':
    test_backup_api()
