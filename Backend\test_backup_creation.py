#!/usr/bin/env python3
"""
Test script for backup creation process
"""

import os
import sys
import django
import time

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from backup_management.models import BackupJob
from backup_management.services import BackupService

User = get_user_model()

def test_backup_creation():
    """Test backup creation process"""
    
    # Get or create admin user
    admin_user = User.objects.filter(role='admin').first()
    if not admin_user:
        admin_user = User.objects.filter(is_superuser=True).first()
    
    if not admin_user:
        print("❌ No admin user found")
        return
    
    print(f"✅ Using admin user: {admin_user.name}")
    
    # Create a test backup job
    backup_job = BackupJob.objects.create(
        name="Test Database Backup",
        backup_type="database",
        storage_location="local",
        retention_days=7,
        created_by=admin_user
    )
    
    print(f"✅ Created backup job: {backup_job.id}")
    print(f"📊 Initial status: {backup_job.status}, Progress: {backup_job.progress}%")
    
    # Create backup service and start backup
    backup_service = BackupService()
    print(f"✅ Backup service initialized")
    print(f"📁 Backup directory: {backup_service.backup_dir}")
    
    # Start backup in sync mode for testing
    print("🚀 Starting backup creation...")
    try:
        result = backup_service.create_backup(backup_job)
        print(f"✅ Backup creation result: {result}")
    except Exception as e:
        print(f"❌ Backup creation failed: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Refresh backup job from database
    backup_job.refresh_from_db()
    print(f"📊 Final status: {backup_job.status}, Progress: {backup_job.progress}%")
    print(f"📁 File path: {backup_job.file_path}")
    print(f"📏 File size: {backup_job.formatted_file_size}")
    
    if backup_job.error_message:
        print(f"❌ Error message: {backup_job.error_message}")
    
    # Check if backup file exists
    if backup_job.file_path and os.path.exists(backup_job.file_path):
        file_size = os.path.getsize(backup_job.file_path)
        print(f"✅ Backup file exists: {backup_job.file_path} ({file_size} bytes)")
    else:
        print(f"❌ Backup file not found: {backup_job.file_path}")

def test_async_backup():
    """Test async backup creation"""
    
    # Get or create admin user
    admin_user = User.objects.filter(role='admin').first()
    if not admin_user:
        admin_user = User.objects.filter(is_superuser=True).first()
    
    if not admin_user:
        print("❌ No admin user found")
        return
    
    print(f"✅ Using admin user: {admin_user.name}")
    
    # Create a test backup job
    backup_job = BackupJob.objects.create(
        name="Test Async Database Backup",
        backup_type="database",
        storage_location="local",
        retention_days=7,
        created_by=admin_user
    )
    
    print(f"✅ Created async backup job: {backup_job.id}")
    
    # Create backup service and start async backup
    backup_service = BackupService()
    thread = backup_service.create_backup_async(backup_job)
    
    print("🚀 Started async backup creation...")
    
    # Monitor progress for 30 seconds
    for i in range(30):
        backup_job.refresh_from_db()
        print(f"📊 Status: {backup_job.status}, Progress: {backup_job.progress}%")
        
        if backup_job.status in ['completed', 'failed']:
            break
            
        time.sleep(1)
    
    # Final status
    backup_job.refresh_from_db()
    print(f"📊 Final status: {backup_job.status}, Progress: {backup_job.progress}%")
    
    if backup_job.error_message:
        print(f"❌ Error message: {backup_job.error_message}")

if __name__ == '__main__':
    print("🔍 Testing synchronous backup creation...")
    test_backup_creation()
    
    print("\n" + "="*50 + "\n")
    
    print("🔍 Testing asynchronous backup creation...")
    test_async_backup()
