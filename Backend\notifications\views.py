from datetime import datetime, timedelta
from django.db.models import Q, Count, Sum
from django.utils import timezone
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdminUser
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth import get_user_model

from .models import (
    Notification, NotificationTemplate, NotificationDelivery,
    NotificationSettings, NotificationStatistics
)
from .serializers import (
    NotificationSerializer, NotificationListSerializer, SendNotificationSerializer,
    NotificationTemplateSerializer, NotificationSettingsSerializer,
    NotificationStatisticsSerializer, NotificationStatsSerializer,
    BulkNotificationSerializer
)
from .services import NotificationService

User = get_user_model()


class NotificationPagination(PageNumberPagination):
    """Custom pagination for notifications"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class NotificationListView(generics.ListAPIView):
    """
    API view to retrieve notifications with filtering
    """
    serializer_class = NotificationListSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    pagination_class = NotificationPagination

    def get_queryset(self):
        queryset = Notification.objects.select_related('created_by', 'template').all()

        # Filter by type
        notification_type = self.request.query_params.get('type')
        if notification_type and notification_type != 'all':
            queryset = queryset.filter(notification_type=notification_type)

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter and status_filter != 'all':
            queryset = queryset.filter(status=status_filter)

        # Filter by date range
        date_range = self.request.query_params.get('date_range', 'all')
        now = timezone.now()

        if date_range == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            queryset = queryset.filter(created_at__gte=start_date)
        elif date_range == 'yesterday':
            yesterday = now - timedelta(days=1)
            start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
            queryset = queryset.filter(created_at__range=[start_date, end_date])
        elif date_range == 'last_7_days':
            start_date = now - timedelta(days=7)
            queryset = queryset.filter(created_at__gte=start_date)
        elif date_range == 'last_30_days':
            start_date = now - timedelta(days=30)
            queryset = queryset.filter(created_at__gte=start_date)

        # Search functionality
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(message__icontains=search) |
                Q(created_by__user_name__icontains=search)
            )

        return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        """Override list to return custom response format"""
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response({
                'notifications': serializer.data
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'notifications': serializer.data
            }
        })


class NotificationDetailView(generics.RetrieveAPIView):
    """
    API view to retrieve a single notification with full details
    """
    queryset = Notification.objects.select_related('created_by', 'template').prefetch_related('deliveries')
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    lookup_field = 'id'


@api_view(['POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def send_notification_view(request):
    """
    API view to send a new notification
    """
    try:
        serializer = SendNotificationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'error': {'message': 'Invalid notification data'},
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create notification service
        notification_service = NotificationService()

        # Send notification
        notification = notification_service.send_notification(
            created_by=request.user,
            **serializer.validated_data
        )

        # Return notification details
        response_serializer = NotificationSerializer(notification)
        return Response({
            'success': True,
            'data': response_serializer.data,
            'message': 'Notification sent successfully'
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def notification_stats_view(request):
    """
    API view to get notification statistics
    """
    try:
        # Get date range for stats
        date_range = request.query_params.get('date_range', 'today')
        now = timezone.now()

        if date_range == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            queryset = Notification.objects.filter(created_at__gte=start_date)
        elif date_range == 'last_7_days':
            start_date = now - timedelta(days=7)
            queryset = Notification.objects.filter(created_at__gte=start_date)
        elif date_range == 'last_30_days':
            start_date = now - timedelta(days=30)
            queryset = Notification.objects.filter(created_at__gte=start_date)
        else:
            queryset = Notification.objects.all()

        # Calculate basic stats
        total_sent = queryset.filter(status__in=['sent', 'delivered']).count()
        total_delivered = queryset.filter(status='delivered').count()
        total_failed = queryset.filter(status='failed').count()
        pending = queryset.filter(status__in=['draft', 'scheduled', 'sending']).count()

        delivery_rate = (total_delivered / total_sent * 100) if total_sent > 0 else 0

        # Get delivery stats by channel
        deliveries = NotificationDelivery.objects.filter(
            notification__in=queryset
        )

        email_stats = deliveries.filter(channel='email').aggregate(
            sent=Count('id', filter=Q(status__in=['sent', 'delivered'])),
            delivered=Count('id', filter=Q(status='delivered')),
            failed=Count('id', filter=Q(status='failed'))
        )

        sms_stats = deliveries.filter(channel='sms').aggregate(
            sent=Count('id', filter=Q(status__in=['sent', 'delivered'])),
            delivered=Count('id', filter=Q(status='delivered')),
            failed=Count('id', filter=Q(status='failed'))
        )

        push_stats = deliveries.filter(channel='push').aggregate(
            sent=Count('id', filter=Q(status__in=['sent', 'delivered'])),
            delivered=Count('id', filter=Q(status='delivered')),
            failed=Count('id', filter=Q(status='failed'))
        )

        # Get stats by type
        type_stats = queryset.values('notification_type').annotate(
            count=Count('id')
        )
        type_counts = {item['notification_type']: item['count'] for item in type_stats}

        # Prepare response data
        stats_data = {
            'sent_today': total_sent,
            'delivery_rate': round(delivery_rate, 1),
            'pending': pending,
            'failed': total_failed,

            'email_sent': email_stats['sent'] or 0,
            'email_delivered': email_stats['delivered'] or 0,
            'email_failed': email_stats['failed'] or 0,

            'sms_sent': sms_stats['sent'] or 0,
            'sms_delivered': sms_stats['delivered'] or 0,
            'sms_failed': sms_stats['failed'] or 0,

            'push_sent': push_stats['sent'] or 0,
            'push_delivered': push_stats['delivered'] or 0,
            'push_failed': push_stats['failed'] or 0,

            'order_notifications': type_counts.get('order', 0),
            'user_notifications': type_counts.get('user', 0),
            'restaurant_notifications': type_counts.get('restaurant', 0),
            'delivery_notifications': type_counts.get('delivery', 0),
            'system_notifications': type_counts.get('system', 0),
        }

        return Response({
            'success': True,
            'data': stats_data
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'PATCH'])
@permission_classes([IsAuthenticated, IsAdminUser])
def notification_settings_view(request):
    """
    API view to get and update notification settings
    """
    try:
        settings = NotificationSettings.get_settings()

        if request.method == 'GET':
            serializer = NotificationSettingsSerializer(settings)
            return Response({
                'success': True,
                'data': serializer.data
            })

        elif request.method == 'PATCH':
            serializer = NotificationSettingsSerializer(
                settings,
                data=request.data,
                partial=True
            )

            if serializer.is_valid():
                settings.updated_by = request.user
                serializer.save()

                return Response({
                    'success': True,
                    'data': serializer.data,
                    'message': 'Settings updated successfully'
                })
            else:
                return Response({
                    'success': False,
                    'error': {'message': 'Invalid settings data'},
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class NotificationTemplateListView(generics.ListCreateAPIView):
    """
    API view to list and create notification templates
    """
    queryset = NotificationTemplate.objects.filter(is_active=True)
    serializer_class = NotificationTemplateSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class NotificationTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view to retrieve, update, and delete notification templates
    """
    queryset = NotificationTemplate.objects.all()
    serializer_class = NotificationTemplateSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    lookup_field = 'id'


@api_view(['POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def bulk_notification_action_view(request):
    """
    API view for bulk notification actions
    """
    try:
        serializer = BulkNotificationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'error': {'message': 'Invalid bulk action data'},
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        notification_ids = serializer.validated_data['notification_ids']
        action = serializer.validated_data['action']

        notifications = Notification.objects.filter(id__in=notification_ids)

        if action == 'send':
            # Send notifications that are in draft status
            draft_notifications = notifications.filter(status='draft')
            notification_service = NotificationService()

            sent_count = 0
            for notification in draft_notifications:
                try:
                    notification_service.process_notification(notification)
                    sent_count += 1
                except Exception as e:
                    print(f"Failed to send notification {notification.id}: {e}")

            return Response({
                'success': True,
                'data': {'sent_count': sent_count},
                'message': f'Sent {sent_count} notifications'
            })

        elif action == 'cancel':
            # Cancel scheduled notifications
            cancelled_count = notifications.filter(
                status__in=['draft', 'scheduled']
            ).update(status='cancelled')

            return Response({
                'success': True,
                'data': {'cancelled_count': cancelled_count},
                'message': f'Cancelled {cancelled_count} notifications'
            })

        elif action == 'delete':
            # Delete notifications (only drafts and cancelled)
            deletable = notifications.filter(
                status__in=['draft', 'cancelled', 'failed']
            )
            deleted_count = deletable.count()
            deletable.delete()

            return Response({
                'success': True,
                'data': {'deleted_count': deleted_count},
                'message': f'Deleted {deleted_count} notifications'
            })

        else:
            return Response({
                'success': False,
                'error': {'message': 'Invalid action'}
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def notification_history_view(request):
    """
    API view to get notification history and analytics
    """
    try:
        # Get date range
        days = int(request.query_params.get('days', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)

        # Get daily statistics
        daily_stats = NotificationStatistics.objects.filter(
            date__range=[start_date, end_date]
        ).order_by('date')

        serializer = NotificationStatisticsSerializer(daily_stats, many=True)

        # Calculate summary
        total_sent = sum(stat.total_sent for stat in daily_stats)
        total_delivered = sum(stat.total_delivered for stat in daily_stats)
        total_failed = sum(stat.total_failed for stat in daily_stats)

        overall_delivery_rate = (total_delivered / total_sent * 100) if total_sent > 0 else 0

        return Response({
            'success': True,
            'data': {
                'daily_stats': serializer.data,
                'summary': {
                    'total_sent': total_sent,
                    'total_delivered': total_delivered,
                    'total_failed': total_failed,
                    'delivery_rate': round(overall_delivery_rate, 1),
                    'date_range': {
                        'start': start_date,
                        'end': end_date,
                        'days': days
                    }
                }
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def test_notification_view(request):
    """
    API view to send a test notification
    """
    try:
        # Send test notification to the requesting admin
        notification_service = NotificationService()

        test_notification = notification_service.send_notification(
            created_by=request.user,
            title='Test Notification',
            message='This is a test notification from Afghan Sofra admin panel.',
            notification_type='system',
            recipient_type='user',
            recipient_users=[request.user.id],
            channels=['email'],
            priority=1
        )

        return Response({
            'success': True,
            'data': {'notification_id': test_notification.id},
            'message': 'Test notification sent successfully'
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def search_users_view(request):
    """
    API view to search users for notification targeting
    """
    try:
        search_query = request.query_params.get('q', '')
        role_filter = request.query_params.get('role', '')
        limit = int(request.query_params.get('limit', 20))

        # Build queryset
        queryset = User.objects.filter(is_verified=True)

        # Apply search filter
        if search_query:
            queryset = queryset.filter(
                Q(user_name__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(name__icontains=search_query)
            )

        # Apply role filter
        if role_filter and role_filter != 'all':
            queryset = queryset.filter(role=role_filter)

        # Limit results
        users = queryset[:limit]

        # Serialize user data
        user_data = []
        for user in users:
            user_data.append({
                'id': user.id,
                'user_name': user.user_name,
                'email': user.email,
                'name': getattr(user, 'name', user.user_name),
                'role': user.role,
                'is_verified': user.is_verified,
                'avatar': getattr(user, 'profile_picture', None)
            })

        return Response({
            'success': True,
            'data': {
                'users': user_data,
                'total': queryset.count(),
                'showing': len(user_data)
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def get_user_roles_view(request):
    """
    API view to get available user roles dynamically
    """
    try:
        # Get unique roles from database
        roles = User.objects.values_list('role', flat=True).distinct()

        # Get role counts
        role_data = []
        for role in roles:
            if role:  # Skip empty roles
                count = User.objects.filter(role=role, is_verified=True).count()
                role_data.append({
                    'value': role,
                    'label': role.replace('_', ' ').title(),
                    'count': count
                })

        # Sort by count (descending)
        role_data.sort(key=lambda x: x['count'], reverse=True)

        return Response({
            'success': True,
            'data': {
                'roles': role_data,
                'total_roles': len(role_data)
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def get_notification_types_view(request):
    """
    API view to get available notification types dynamically
    """
    try:
        # Get notification types from model choices
        notification_types = []
        for value, label in Notification.NOTIFICATION_TYPES:
            # Get count of notifications for this type
            count = Notification.objects.filter(notification_type=value).count()
            notification_types.append({
                'value': value,
                'label': label,
                'count': count
            })

        return Response({
            'success': True,
            'data': {
                'types': notification_types
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def create_template_view(request):
    """
    API view to create a new notification template
    """
    try:
        serializer = NotificationTemplateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'error': {'message': 'Invalid template data'},
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        template = serializer.save(created_by=request.user)

        return Response({
            'success': True,
            'data': NotificationTemplateSerializer(template).data,
            'message': 'Template created successfully'
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT', 'PATCH'])
@permission_classes([IsAuthenticated, IsAdminUser])
def update_template_view(request, template_id):
    """
    API view to update a notification template
    """
    try:
        template = NotificationTemplate.objects.get(id=template_id)

        partial = request.method == 'PATCH'
        serializer = NotificationTemplateSerializer(template, data=request.data, partial=partial)

        if not serializer.is_valid():
            return Response({
                'success': False,
                'error': {'message': 'Invalid template data'},
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        template = serializer.save()

        return Response({
            'success': True,
            'data': NotificationTemplateSerializer(template).data,
            'message': 'Template updated successfully'
        })

    except NotificationTemplate.DoesNotExist:
        return Response({
            'success': False,
            'error': {'message': 'Template not found'}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated, IsAdminUser])
def delete_template_view(request, template_id):
    """
    API view to delete a notification template
    """
    try:
        template = NotificationTemplate.objects.get(id=template_id)

        # Check if template is being used
        notifications_using_template = Notification.objects.filter(template=template).count()
        if notifications_using_template > 0:
            return Response({
                'success': False,
                'error': {'message': f'Cannot delete template. It is being used by {notifications_using_template} notifications.'}
            }, status=status.HTTP_400_BAD_REQUEST)

        template.delete()

        return Response({
            'success': True,
            'message': 'Template deleted successfully'
        })

    except NotificationTemplate.DoesNotExist:
        return Response({
            'success': False,
            'error': {'message': 'Template not found'}
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': {'message': str(e)}
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
